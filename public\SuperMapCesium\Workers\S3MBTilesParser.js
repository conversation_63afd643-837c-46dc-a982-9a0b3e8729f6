define(["require","./createTaskProcessorWorker","./Check-7b2a090c","./FeatureDetection-806b12f0","./when-b60132fc","./Cartesian2-47311507","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Color-2a095a27","./ComponentDatatype-c140a87d","./getStringFromTypedArray-c37342c0","./buildModuleUrl-8958744c","./S3MCompressType-75aa9ff0","./IndexDatatype-8a5eead4","./RuntimeError-4a5c8994","./BoundingRectangle-1f901ba8","./S3MPixelFormat-4f2b7689","./pako_inflate-f73548c4","./arrayFill-4513d7ad","./CompressedTextureBuffer-290a1ff4","./PixelFormat-9345f1c7","./Math-119be1a3","./WebGLConstants-4ae0db90","./Event-16a2dfbf"],(function(t,e,r,a,n,A,o,i,B,E,C,s,y,l,d,f,u,P,c,p,T,L,D,g){"use strict";function F(t,e,r){if("function"==typeof t.slice)return t.slice(e,r);for(var n=Array.prototype.slice.call(t,e,r),A=a.FeatureDetection.typedArrayTypes,o=A.length,i=0;i<o;++i)if(t instanceof A[i]){n=new A[i](n);break}return n}function M(){}var m;function I(t,e,r){var a,A=t.num_points(),o=r.num_components(),i=new m.AttributeQuantizationTransform;if(i.InitFromAttribute(r)){for(var B=new Array(o),C=0;C<o;++C)B[C]=i.min_value(C);a={quantizationBits:i.quantization_bits(),minValues:B,range:i.range(),octEncoded:!1}}m.destroy(i),(i=new m.AttributeOctahedronTransform).InitFromAttribute(r)&&(a={quantizationBits:i.quantization_bits(),octEncoded:!0}),m.destroy(i);var s,y=A*o;s=n.defined(a)?function(t,e,r,a,n){var A,o;a.quantizationBits<=8?(o=new m.DracoUInt8Array,A=new Uint8Array(n),e.GetAttributeUInt8ForAllPoints(t,r,o)):(o=new m.DracoUInt16Array,A=new Uint16Array(n),e.GetAttributeUInt16ForAllPoints(t,r,o));for(var i=0;i<n;++i)A[i]=o.GetValue(i);return m.destroy(o),A}(t,e,r,a,y):function(t,e,r,a){var n,A;switch(r.data_type()){case 1:case 11:A=new m.DracoInt8Array,n=new Int8Array(a),e.GetAttributeInt8ForAllPoints(t,r,A);break;case 2:A=new m.DracoUInt8Array,n=new Uint8Array(a),e.GetAttributeUInt8ForAllPoints(t,r,A);break;case 3:A=new m.DracoInt16Array,n=new Int16Array(a),e.GetAttributeInt16ForAllPoints(t,r,A);break;case 4:A=new m.DracoUInt16Array,n=new Uint16Array(a),e.GetAttributeUInt16ForAllPoints(t,r,A);break;case 5:case 7:A=new m.DracoInt32Array,n=new Int32Array(a),e.GetAttributeInt32ForAllPoints(t,r,A);break;case 6:case 8:A=new m.DracoUInt32Array,n=new Uint32Array(a),e.GetAttributeUInt32ForAllPoints(t,r,A);break;case 9:case 10:A=new m.DracoFloat32Array,n=new Float32Array(a),e.GetAttributeFloatForAllPoints(t,r,A)}for(var o=0;o<a;++o)n[o]=A.GetValue(o);return m.destroy(A),n}(t,e,r,y);var l=E.ComponentDatatype.fromTypedArray(s);return{array:s,data:{componentsPerAttribute:o,componentDatatype:l,byteOffset:r.byte_offset(),byteStride:E.ComponentDatatype.getSizeInBytes(l)*o,normalized:r.normalized(),quantization:a}}}var v=new o.Cartesian3(40680631590769,40680631590769,40408299984661.445),_=new o.Cartesian3,S=new o.Cartesian3;function O(t,e,r,a){var A=Math.cos(e);_.x=A*Math.cos(t),_.y=A*Math.sin(t),_.z=Math.sin(e),_=o.Cartesian3.normalize(_,_),o.Cartesian3.multiplyComponents(v,_,S);var i=Math.sqrt(o.Cartesian3.dot(_,S));return S=o.Cartesian3.divideByScalar(S,i,S),_=o.Cartesian3.multiplyByScalar(_,r,_),n.defined(a)||(a=new o.Cartesian3),o.Cartesian3.add(S,_,a)}var N=new a.Matrix4,h=new a.Matrix4,R=new o.Cartesian3,G=new o.Cartographic;function x(t,e,r,B,C,s,l,d){var f=void 0,u=void 0,P=void 0,c=void 0,p=r.vertexAttributes,T=r.attrLocation;if(r.nCompressOptions=0,n.defined(B.posUniqueID)&&B.posUniqueID>=0){n.defined(d)||(r.nCompressOptions|=y.VertexCompressOption.SVC_Vertex);var L=e.GetAttribute(t,B.posUniqueID),D=I(t,e,L),g=D.data.componentsPerAttribute;r.verticesCount=D.array.length/g,r.vertCompressConstant=D.data.quantization.range/(1<<D.data.quantization.quantizationBits);var F=D.data.quantization.minValues;r.minVerticesValue=new i.Cartesian4(F[0],F[1],F[2],1),g>3&&(r.minVerticesValue.w=F[3]);var M=r.verticesCount;if(s&&(f=new o.Cartographic,u=new o.Cartographic,P=new Float32Array(2*M),c=new Float64Array(2*M)),n.defined(d)){var m=D.array,v=3===g?o.Cartesian3.unpackArray(m):i.Cartesian4.unpackArray(m);for(let t=0,e=v.length;t<e;t++){let e=v[t];o.Cartesian3.multiplyByScalar(e,r.vertCompressConstant,e),o.Cartesian3.add(e,r.minVerticesValue,e)}var _=a.Matrix4.multiply(d.sphereMatrix,d.geoMatrix,N),S=a.Matrix4.multiply(d.ellipsoidMatrix,d.geoMatrix,h);a.Matrix4.inverse(S,S);var x=new A.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=v.length;t<e;t++){let e=v[t];a.Matrix4.multiplyByPoint(_,e,R);let r=x.cartesianToCartographic(R,G);s&&(c[2*t]=r.longitude,c[2*t+1]=r.latitude,0===t?(f.longitude=r.longitude,f.latitude=r.latitude,u.longitude=r.longitude,u.latitude=r.latitude):(f.longitude=Math.max(r.longitude,f.longitude),f.latitude=Math.max(r.latitude,f.latitude),u.longitude=Math.min(r.longitude,u.longitude),u.latitude=Math.min(r.latitude,u.latitude)));let n=O(r.longitude,r.latitude,r.height,R);a.Matrix4.multiplyByPoint(S,n,e)}var b=new Array(3*v.length);3===g?o.Cartesian3.packArray(v,b):i.Cartesian4.packArray(v,b),D.array=new Float32Array(b),D.data.componentDatatype=E.ComponentDatatype.FLOAT,D.data.byteStride=4*g}if(T.aPosition=p.length,p.push({index:T.aPosition,typedArray:D.array,componentsPerAttribute:g,componentDatatype:D.data.componentDatatype,offsetInBytes:D.data.byteOffset,strideInBytes:D.data.byteStride,normalize:D.data.normalized}),!n.defined(d)&&s)for(var U=new o.Cartesian3,K=new o.Cartesian3,H=new o.Cartographic,V=0;V<M;V++)a.Matrix4.multiplyByPoint(C,o.Cartesian3.fromElements(D.array[3*V]*r.vertCompressConstant+F[0],D.array[3*V+1]*r.vertCompressConstant+F[1],D.array[3*V+2]*r.vertCompressConstant+F[2],U),K),H=o.Cartographic.fromCartesian(K),c[2*V]=H.longitude,c[2*V+1]=H.latitude,0===V?(f.longitude=H.longitude,f.latitude=H.latitude,u.longitude=H.longitude,u.latitude=H.latitude):(f.longitude=Math.max(H.longitude,f.longitude),f.latitude=Math.max(H.latitude,f.latitude),u.longitude=Math.min(H.longitude,u.longitude),u.latitude=Math.min(H.latitude,u.latitude));if(s){for(V=0;V<M;V++)P[2*V]=c[2*V]-u.longitude,P[2*V+1]=c[2*V+1]-u.latitude;T.img=p.length,p.push({index:T.img,typedArray:P,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),l.max=f,l.min=u}}if(n.defined(B.normalUniqueID)&&B.normalUniqueID>=0){r.nCompressOptions|=y.VertexCompressOption.SVC_Normal;var w=e.GetAttribute(t,B.normalUniqueID),Y=I(t,e,w),J=Y.data.quantization;r.normalRangeConstant=(1<<J.quantizationBits)-1,T.aNormal=p.length,p.push({index:T.aNormal,typedArray:Y.array,componentsPerAttribute:Y.data.componentsPerAttribute,componentDatatype:Y.data.componentDatatype,offsetInBytes:Y.data.byteOffset,strideInBytes:Y.data.byteStride,normalize:Y.data.normalized})}if(n.defined(B.colorUniqueID)&&B.colorUniqueID>=0){r.nCompressOptions|=y.VertexCompressOption.SVC_VertexColor;var k=e.GetAttribute(t,B.colorUniqueID),W=I(t,e,k);T.aColor=p.length,p.push({index:T.aColor,typedArray:W.array,componentsPerAttribute:W.data.componentsPerAttribute,componentDatatype:W.data.componentDatatype,offsetInBytes:W.data.byteOffset,strideInBytes:W.data.byteStride,normalize:W.data.normalized})}for(V=0;V<B.texCoordUniqueIDs.length;V++){r.texCoordCompressConstant=[],r.minTexCoordValue=[];var Z=B.texCoordUniqueIDs[V];if(!(Z<0)){var X=e.GetAttribute(t,Z),Q=I(t,e,X);if(n.defined(Q.data.quantization)){r.nCompressOptions|=y.VertexCompressOption.SVC_TexutreCoord,r.texCoordCompressConstant.push(Q.data.quantization.range/(1<<Q.data.quantization.quantizationBits));F=Q.data.quantization.minValues;r.minTexCoordValue.push(new A.Cartesian2(F[0],F[1]))}var z="aTexCoord"+V;T[z]=p.length,p.push({index:T[z],typedArray:Q.array,componentsPerAttribute:Q.data.componentsPerAttribute,componentDatatype:Q.data.componentDatatype,offsetInBytes:Q.data.byteOffset,strideInBytes:Q.data.byteStride,normalize:Q.data.normalized}),r.textureCoordIsW=!0}}for(V=0;V<B.vertexAttrUniqueIDs.length;V++){var j=B.vertexAttrUniqueIDs[V];if(!(j<0)){var q=e.GetAttribute(t,j),$=I(t,e,q);T.aVertexWeight=p.length,p.push({index:T.aVertexWeight,typedArray:$.array,componentsPerAttribute:$.data.componentsPerAttribute,componentDatatype:$.data.componentDatatype,offsetInBytes:$.data.byteOffset,strideInBytes:$.data.byteStride,normalize:$.data.normalized}),r.customVertexAttribute={VertexWeight:0}}}}M.dracoDecodePointCloud=function(t,e,r,a,n){for(var A=new(m=t).Decoder,o=["POSITION","NORMAL","COLOR"],i=0;i<o.length;++i)A.SkipAttributeTransform(m[o[i]]);var B=new m.DecoderBuffer;if(B.Init(e,r),A.GetEncodedGeometryType(B)!==m.POINT_CLOUD)throw new d.RuntimeError("Draco geometry type must be POINT_CLOUD.");var E=new m.PointCloud,C=A.DecodeBufferToPointCloud(B,E);if(!C.ok()||0===E.ptr)throw new d.RuntimeError("Error decoding draco point cloud: "+C.error_msg());m.destroy(B),x(E,A,a,n),m.destroy(E),m.destroy(A)},M.dracoDecodeMesh=function(t,e,r,n,A,o,i,B,E,C){for(var s=new(m=t).Decoder,y=["POSITION","NORMAL","COLOR","TEX_COORD"],f=0;f<y.length;++f)s.SkipAttributeTransform(m[y[f]]);var u=new m.DecoderBuffer;if(u.Init(e,r),s.GetEncodedGeometryType(u)!==m.TRIANGULAR_MESH)throw new d.RuntimeError("Unsupported draco mesh geometry type.");var P=new m.Mesh;if(!s.DecodeBufferToMesh(u,P).ok()||0===P.ptr)return!1;m.destroy(u),x(P,s,n,o,i,B,E,C);var c=function(t,e){for(var r=t.num_points(),a=t.num_faces(),n=new m.DracoInt32Array,A=3*a,o=l.IndexDatatype.createTypedArray(r,A),i=0,B=0;B<a;++B)e.GetFaceFromMesh(t,B,n),o[i+0]=n.GetValue(0),o[i+1]=n.GetValue(1),o[i+2]=n.GetValue(2),i+=3;var E=l.IndexDatatype.UNSIGNED_SHORT;return o instanceof Uint32Array&&(E=l.IndexDatatype.UNSIGNED_INT),m.destroy(n),{typedArray:o,numberOfIndices:A,indexDataType:E}}(P,s);A.indicesTypedArray=c.typedArray,A.indicesCount=c.numberOfIndices,A.indexType=c.indexDataType,A.primitiveType=a.PrimitiveType.TRIANGLES,m.destroy(P),m.destroy(s)};var b=Object.freeze({OSGBFile:0,OSGBCacheFile:1,ClampGroundPolygon:2,ClampObjectPolygon:3,ClampGroundLine:4,ClampObjectLine:5,IconPoint:6,Text:7,PointCloudFile:8,ExtendRegion3D:9,ExtendClampPolygonCache:10,PolylineEffect:11,RegionEffect:12,ClampGroundAndObjectLineCache:13,ClampGroundRealtimeRasterCache:14});function U(){}function K(t){var e=new s.BoundingSphere,r=t.instanceBounds;if(!n.defined(r))return e;var a=new o.Cartesian3(r[0],r[1],r[2]),A=new o.Cartesian3(r[3],r[4],r[5]),i=o.Cartesian3.lerp(a,A,.5,new o.Cartesian3),B=o.Cartesian3.distance(i,a);return e.center=i,e.radius=B,e}function H(t){var e,r,a=new s.BoundingSphere,A=new o.Cartesian3,i=t.vertexAttributes[0],B=i.componentsPerAttribute,E=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,C=1;E?(C=t.vertCompressConstant,e=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),r=new Uint16Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/2)):r=new Float32Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/4);for(var l=[],d=0;d<t.verticesCount;d++)o.Cartesian3.fromArray(r,B*d,A),E&&(A=o.Cartesian3.multiplyByScalar(A,C,A),A=o.Cartesian3.add(A,e,A)),l.push(o.Cartesian3.clone(A));return s.BoundingSphere.fromPoints(l,a),l.length=0,a}function V(t){var e,r,a=new s.BoundingSphere,A=new o.Cartesian3,i=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;i?(C=t.vertCompressConstant,r=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var l=[],d=0;d<t.verticesCount;d++)o.Cartesian3.fromArray(e,E*d,A),i&&(A=o.Cartesian3.multiplyByScalar(A,C,A),A=o.Cartesian3.add(A,r,A)),l.push(o.Cartesian3.clone(A));return s.BoundingSphere.fromPoints(l,a),l.length=0,a}function w(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,A=new s.BoundingSphere,B=new o.Cartesian3,E=new o.Cartesian3,C=t.vertexAttributes[0],l=C.componentsPerAttribute,d=t.attrLocation.aPosition,f=t.vertexAttributes[d],u=t.attrLocation.aTexCoord5,P=t.vertexAttributes[u],c=P.componentsPerAttribute;a?(l=3,c=3,e=J(t,f),r=function(t,e,r){for(var a,n,A,o=e.componentsPerAttribute,B=t.texCoordCompressConstant[r],E=new i.Cartesian4(t.minTexCoordValue[r].x,t.minTexCoordValue[r].y,t.minTexCoordValue[r].z,t.minTexCoordValue[r].w),C=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),s=new Float32Array(3*t.verticesCount),y=0;y<t.verticesCount;y++)a=C[o*y]*B+E.x,n=C[o*y+1]*B+E.y,A=C[o*y+2]*B+E.z,s[3*y]=a,s[3*y+1]=n,s[3*y+2]=A;return s}(t,P,5)):(e=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4),r=new Float32Array(P.typedArray.buffer,P.typedArray.byteOffset,P.typedArray.byteLength/4));for(var p=[],T=0;T<t.verticesCount;T++)o.Cartesian3.fromArray(e,l*T,B),o.Cartesian3.fromArray(r,c*T,E),o.Cartesian3.add(B,E,B),p.push(o.Cartesian3.clone(B));return s.BoundingSphere.fromPoints(p,A),p.length=0,A}function Y(t){var e=a.PrimitiveType.TRIANGLES;switch(t){case 1:e=a.PrimitiveType.POINTS;break;case 2:e=a.PrimitiveType.LINES;break;case 3:e=a.PrimitiveType.LINE_STRIP;break;case 4:e=a.PrimitiveType.TRIANGLES}return e}function J(t,e){for(var r,a,n,A=e.componentsPerAttribute,i=t.vertCompressConstant,B=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),E=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),C=new Float32Array(3*t.verticesCount),s=0;s<t.verticesCount;s++)r=E[A*s]*i+B.x,a=E[A*s+1]*i+B.y,n=E[A*s+2]*i+B.z,C[3*s]=r,C[3*s+1]=a,C[3*s+2]=n;return C}U.calcBoundingSphereInWorker=function(t,e){return e.instanceIndex>-1?K(e):n.defined(e.clampRegionEdge)?w(e):t>=b.ClampGroundPolygon&&t<=b.ClampObjectLine?V(e):t==b.ClampGroundAndObjectLineCache?w(e):H(e)},U.calcBoundingSphere=function(t,e,r){var a,A=t._fileType;return a=e.instanceIndex>-1?K(e):n.defined(e.clampRegionEdge)?w(e):A>=b.ClampGroundPolygon&&A<=b.ClampObjectLine?V(e):A==b.ClampGroundAndObjectLineCache?w(e):H(e),s.BoundingSphere.transform(a,r,a),a},U.calcBoundingRectangle=function(t,e){var r;return t._fileType===b.ClampGroundPolygon&&(r=function(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,i=new f.BoundingRectangle,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;a?(C=t.vertCompressConstant,r=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var s=[],l=0;l<t.verticesCount;l++){var d=e[E*l],u=e[E*l+1];a&&(d=C*d+r.x,u=C*u+r.y),s.push(new A.Cartesian2(d,u))}return f.BoundingRectangle.fromPoints(s,i),s.length=0,i}(e)),r},U.createEdge=function(t,e){if(!(e.length<1)){var r=function(t){for(var e=[],r=t.length,n=0;n<r;n++){var A=Y(t[n].primitiveType);A!==a.PrimitiveType.LINES&&A!==a.PrimitiveType.LINE_STRIP||e.push(t[n])}return e}(e);if(0!=r.length){var A,i=function(t){for(var e=0,r=t.length,n=0;n<r;n++){var A=t[n],o=Y(A.primitiveType);o==a.PrimitiveType.LINES?e+=A.indicesCount/2:o==a.PrimitiveType.LINE_STRIP&&e++}return e}(r),B=t.attrLocation.aPosition,C=t.vertexAttributes[B],s=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,l=C.componentsPerAttribute;s?(l=3,A=J(t,C)):A=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4);for(var d=function(t){for(var e=0,r=t.length,a=0;a<r;a++)e+=t[a].indicesCount;return e}(r),f=function(t,e,r){for(var n,A=[],i=r.length,B=0;B<i;B++){var E,C=r[B];E=0===C.indexType?new Uint16Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/2):new Uint32Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/4);var s=Y(C.primitiveType);if(s==a.PrimitiveType.LINES)for(n=0;n<C.indicesCount;n+=2){var y=[],l=new o.Cartesian3;l.x=t[E[n]*e],l.y=t[E[n]*e+1],l.z=t[E[n]*e+2],y.push(l);var d=new o.Cartesian3;d.x=t[E[n+1]*e],d.y=t[E[n+1]*e+1],d.z=t[E[n+1]*e+2],y.push(d),A.push(y)}else if(s==a.PrimitiveType.LINE_STRIP){for(y=[],n=0;n<C.indicesCount;n++){var f=new o.Cartesian3;f.x=t[E[n]*e],f.y=t[E[n]*e+1],f.z=t[E[n]*e+2],y.push(f)}A.push(y)}}return A}(A,l,r),u=4*d-4*i,P=new Float32Array(3*u),c=new Float32Array(3*u),p=new Float32Array(3*u),T=new Int8Array(2*u),L=0,D=0;D<i;D++){for(var g=f[D].length,F=0;F<g;F++){var M=4*L-4*D,m=3*M+12*F,I=f[D][F];0!=F&&(P[m-6]=I.x,P[m-5]=I.y,P[m-4]=I.z,P[m-3]=I.x,P[m-2]=I.y,P[m-1]=I.z),F!=g-1&&(P[m]=I.x,P[m+1]=I.y,P[m+2]=I.z,P[m+3]=I.x,P[m+4]=I.y,P[m+5]=I.z);var v=I;F+1<g&&(v=f[D][F+1]),0!=F&&(p[m-6]=v.x,p[m-5]=v.y,p[m-4]=v.z,p[m-3]=v.x,p[m-2]=v.y,p[m-1]=v.z),F!=g-1&&(p[m]=v.x,p[m+1]=v.y,p[m+2]=v.z,p[m+3]=v.x,p[m+4]=v.y,p[m+5]=v.z);var _=I;F>=1&&(_=f[D][F-1]),0!=F&&(c[m-6]=_.x,c[m-5]=_.y,c[m-4]=_.z,c[m-3]=_.x,c[m-2]=_.y,c[m-1]=_.z),F!=g-1&&(c[m]=_.x,c[m+1]=_.y,c[m+2]=_.z,c[m+3]=_.x,c[m+4]=_.y,c[m+5]=_.z),m=2*M+8*F,0!=F&&(T[m-4]=-1,T[m-3]=-1,T[m-2]=1,T[m-1]=-1),F!=g-1&&(T[m]=-1,T[m+1]=1,T[m+2]=1,T[m+3]=1)}L+=f[D].length}var S={vertexAttributes:[],attrLocation:{}},O=S.vertexAttributes,N=S.attrLocation;S.instanceCount=0,S.instanceMode=0,N.aPosition=0,O.push({index:N.aPosition,typedArray:P,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aNormal=1,O.push({index:N.aNormal,typedArray:c,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord0=2,O.push({index:N.aTexCoord0,typedArray:p,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord1=3,O.push({index:N.aTexCoord1,typedArray:T,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:2*Int8Array.BYTES_PER_ELEMENT,normalize:!1});for(var h=[],R=0;R<f.length;R++)h.push(f[R].length);var G=function(t,e,r,n){var A,o={};o.indicesCount=6*(t-e),o.indexType=n>65535?1:0,o.primitiveType=a.PrimitiveType.TRIANGLES,A=0===o.indexType?new Uint16Array(o.indicesCount):new Uint32Array(o.indicesCount);for(var i=0,B=0;B<e;B++){for(var E=0;E<r[B]-1;E++)A[6*(i-B+E)]=4*(i-B+E),A[6*(i-B+E)+1]=4*(i-B+E)+2,A[6*(i-B+E)+2]=4*(i-B+E)+1,A[6*(i-B+E)+3]=4*(i-B+E)+1,A[6*(i-B+E)+4]=4*(i-B+E)+2,A[6*(i-B+E)+5]=4*(i-B+E)+3;i+=r[B]}return o.indicesTypedArray=A,o}(d,i,h,u);return{vertexPackage:S,indexPackage:G}}}};var k,W,Z=Object.freeze({S3M:49,S3M4:1}),X=function(){var t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),e=new Uint8Array([32,0,65,2,1,106,34,33,3,128,11,4,13,64,6,253,10,7,15,116,127,5,8,12,40,16,19,54,20,9,27,255,113,17,42,67,24,23,146,148,18,14,22,45,70,69,56,114,101,21,25,63,75,136,108,28,118,29,73,115]);if("object"!=typeof WebAssembly)return{supported:!1};var r,a="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";WebAssembly.validate(t)&&(a="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");var n=WebAssembly.instantiate(function(t){for(var r=new Uint8Array(t.length),a=0;a<t.length;++a){var n=t.charCodeAt(a);r[a]=n>96?n-71:n>64?n-65:n>47?n+4:n>46?63:62}var A=0;for(a=0;a<t.length;++a)r[A++]=r[a]<60?e[r[a]]:64*(r[a]-60)+r[++a];return r.buffer.slice(0,A)}(a),{}).then((function(t){(r=t.instance).exports.__wasm_call_ctors()}));function A(t,e,a,n,A,o){var i=r.exports.sbrk,B=a+3&-4,E=i(B*n),C=i(A.length),s=new Uint8Array(r.exports.memory.buffer);s.set(A,C);var y=t(E,a,n,C,A.length);if(0==y&&o&&o(E,B,n),e.set(s.subarray(E,E+a*n)),i(E-i(0)),0!=y)throw new Error("Malformed buffer data: "+y)}var o={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},i={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return{ready:n,supported:!0,decodeVertexBuffer:function(t,e,a,n,i){A(r.exports.meshopt_decodeVertexBuffer,t,e,a,n,r.exports[o[i]])},decodeIndexBuffer:function(t,e,a,n){A(r.exports.meshopt_decodeIndexBuffer,t,e,a,n)},decodeIndexSequence:function(t,e,a,n){A(r.exports.meshopt_decodeIndexSequence,t,e,a,n)},decodeGltfBuffer:function(t,e,a,n,B,E){A(r.exports[i[B]],t,e,a,n,r.exports[o[E]])}}}(),Q=1,z=2,j={};j[0]=T.PixelFormat.RGB_DXT1,j[Q]=T.PixelFormat.RGBA_DXT3,j[z]=T.PixelFormat.RGBA_DXT5;var q,$=0,tt=!1;function et(t,e){var r=t.data,a=r.byteLength,A=new Uint8Array(r),o=q._malloc(a);!function(t,e,r,a){var n,A=r/4,o=a%4,i=new Uint32Array(t.buffer,0,(a-o)/4),B=new Uint32Array(e.buffer);for(n=0;n<i.length;n++)B[A+n]=i[n];for(n=a-o;n<a;n++)e[r+n]=t[n]}(A,q.HEAPU8,o,a);var i=q._crn_get_dxt_format(o,a),B=j[i];if(!n.defined(B))throw new d.RuntimeError("Unsupported compressed format.");var E,C=q._crn_get_levels(o,a),s=q._crn_get_width(o,a),y=q._crn_get_height(o,a),l=0;for(E=0;E<C;++E)l+=T.PixelFormat.compressedTextureSizeInBytes(B,s>>E,y>>E);if($<l&&(n.defined(k)&&q._free(k),k=q._malloc(l),W=new Uint8Array(q.HEAPU8.buffer,k,l),$=l),q._crn_decompress(o,a,k,l,0,C),q._free(o),n.defaultValue(t.bMipMap,!1)){var f=W.slice(0,l);return e.push(f.buffer),new p.CompressedTextureBuffer(B,s,y,f)}var u=T.PixelFormat.compressedTextureSizeInBytes(B,s,y),P=W.subarray(0,u),c=new Uint8Array(u);return c.set(P,0),e.push(c.buffer),new p.CompressedTextureBuffer(B,s,y,c)}var rt,at=1,nt=0,At=1,ot=2,it=3,Bt=0,Et=1,Ct=2;new B.Color;var st,yt=!1,lt=null;function dt(t,e,r,a,n,A){this.left=t,this.bottom=e,this.right=r,this.top=a,this.minHeight=n,this.maxHeight=A,this.width=r-t,this.length=a-e,this.height=A-n}function ft(t,e,r){var a=r,n=t.getUint32(a,!0),A=a+=Uint32Array.BYTES_PER_ELEMENT,o=new Uint8Array(e,a,n);return{dataViewByteOffset:A,byteOffset:a+=n*Uint8Array.BYTES_PER_ELEMENT,buffer:o}}function ut(t,e,r,a){var n=t.getUint32(a+e,!0);a+=Uint32Array.BYTES_PER_ELEMENT;var A=r.subarray(a,a+n);return{string:C.getStringFromTypedArray(A),bytesOffset:a+=n}}function Pt(t,e,r,a,n,A){var o=r,i=t.getUint16(r+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT,A||(o+=Uint16Array.BYTES_PER_ELEMENT);for(var B=0;B<i;B++){var C=t.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var s=t.getUint16(o+a,!0);if(o+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(o+a,!0),o+=Uint16Array.BYTES_PER_ELEMENT,20==s||35==s);else{var y=C*s*Float32Array.BYTES_PER_ELEMENT,l=e.subarray(o,o+y);o+=y;var d="aTexCoord"+B,f=n.vertexAttributes,u=n.attrLocation;u[d]=f.length,f.push({index:u[d],typedArray:l,componentsPerAttribute:s,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:s*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}}return{bytesOffset:o}}function ct(t,e,r,a,n){var A=r,o=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var i=n.vertexAttributes,B=n.attrLocation,C=0;C<o;C++){var s=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var y=t.getUint16(A+a,!0);if(A+=Uint16Array.BYTES_PER_ELEMENT,16===y){A-=Uint16Array.BYTES_PER_ELEMENT;var l=s*(y*Float32Array.BYTES_PER_ELEMENT+Uint16Array.BYTES_PER_ELEMENT),d=e.subarray(A,A+l);A+=l;var f=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*y*s);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1;for(var u=Float32Array.BYTES_PER_ELEMENT*y+Uint16Array.BYTES_PER_ELEMENT,P=0;P<s;P++){var c=P*u+Uint16Array.BYTES_PER_ELEMENT,p=d.subarray(c,c+u);f.set(p,P*(u-Uint16Array.BYTES_PER_ELEMENT))}T=16*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})}else{t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;l=s*y*Float32Array.BYTES_PER_ELEMENT;if(17===y||29===y){var T;f=e.subarray(A,A+l);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1,17===y?(T=17*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})):29===y&&(T=29*Float32Array.BYTES_PER_ELEMENT,B.uv1=i.length,i.push({index:B.uv1,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1,byteLength:l}),B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv5=i.length,i.push({index:B.uv5,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv7=i.length,i.push({index:B.uv7,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv9=i.length,i.push({index:B.uv9,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:28*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}))}else{var L=s*y;n.instanceBounds=new Float32Array(L);for(var D=0;D<L;D++)n.instanceBounds[D]=t.getFloat32(A+a+D*Float32Array.BYTES_PER_ELEMENT,!0)}A+=l}}return{bytesOffset:A}}var pt=new o.Cartesian3(40680631590769,40680631590769,40408299984661.445),Tt=new o.Cartesian3,Lt=new o.Cartesian3;function Dt(t,e,r,a){var A=Math.cos(e);Tt.x=A*Math.cos(t),Tt.y=A*Math.sin(t),Tt.z=Math.sin(e),Tt=o.Cartesian3.normalize(Tt,Tt),o.Cartesian3.multiplyComponents(pt,Tt,Lt);var i=Math.sqrt(o.Cartesian3.dot(Tt,Lt));return Lt=o.Cartesian3.divideByScalar(Lt,i,Lt),Tt=o.Cartesian3.multiplyByScalar(Tt,r,Tt),n.defined(a)||(a=new o.Cartesian3),o.Cartesian3.add(Lt,Tt,a)}var gt=new o.Cartesian3,Ft=new o.Cartographic,Mt=new a.Matrix4,mt=new a.Matrix4;function It(t,e,r,B,C,s,y,l,d){var f=B,u=e.getUint32(f+r,!0);if(C.verticesCount=u,f+=Uint32Array.BYTES_PER_ELEMENT,u<=0)return{bytesOffset:f};var P=e.getUint16(f+r,!0);f+=Uint16Array.BYTES_PER_ELEMENT;var c=e.getUint16(f+r,!0);c=P*Float32Array.BYTES_PER_ELEMENT,f+=Uint16Array.BYTES_PER_ELEMENT;var p=u*P*Float32Array.BYTES_PER_ELEMENT,T=t.subarray(f,f+p);f+=p;var L=C.vertexAttributes,D=C.attrLocation,g=void 0,F=void 0;if(y){g=new o.Cartographic,F=new o.Cartographic;var M=new Float32Array(2*u),m=new Float64Array(2*u);if(n.defined(l)){var I=new Float32Array(T.byteLength/4),v=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);k=3===P?o.Cartesian3.unpackArray(v):i.Cartesian4.unpackArray(v);var _=a.Matrix4.multiply(l.sphereMatrix,l.geoMatrix,Mt),S=a.Matrix4.multiply(l.ellipsoidMatrix,l.geoMatrix,mt);a.Matrix4.inverse(S,S);for(var O=new A.Ellipsoid(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){var G=k[h];if(14!==d){a.Matrix4.multiplyByPoint(_,G,gt);var x=Dt((W=O.cartesianToCartographic(gt,Ft)).longitude,W.latitude,W.height,gt);a.Matrix4.multiplyByPoint(S,x,G)}3===P?(o.Cartesian3.pack(G,I,N),N+=3):(i.Cartesian4.pack(G,I,N),N+=4),m[2*h]=W.longitude,m[2*h+1]=W.latitude,0===h?(g.longitude=W.longitude,g.latitude=W.latitude,F.longitude=W.longitude,F.latitude=W.latitude):(g.longitude=Math.max(W.longitude,g.longitude),g.latitude=Math.max(W.latitude,g.latitude),F.longitude=Math.min(W.longitude,F.longitude),F.latitude=Math.min(W.latitude,F.latitude))}T=I}else{var b=new o.Cartesian3,U=new o.Cartesian3,K=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4),H=new o.Cartographic;O=xt?new A.Ellipsoid(6378137,6378137,6356752.314245179):new A.Ellipsoid(6378137,6378137,6378137);for(var V=0;V<u;V++)a.Matrix4.multiplyByPoint(s,o.Cartesian3.fromElements(K[3*V],K[3*V+1],K[3*V+2],b),U),H=O.cartesianToCartographic(U,Ft),m[2*V]=H.longitude,m[2*V+1]=H.latitude,0===V?(g.longitude=H.longitude,g.latitude=H.latitude,F.longitude=H.longitude,F.latitude=H.latitude):(g.longitude=Math.max(H.longitude,g.longitude),g.latitude=Math.max(H.latitude,g.latitude),F.longitude=Math.min(H.longitude,F.longitude),F.latitude=Math.min(H.latitude,F.latitude))}for(V=0;V<u;V++)M[2*V]=m[2*V]-F.longitude,M[2*V+1]=m[2*V+1]-F.latitude;D.aPosition=L.length,L.push({index:D.aPosition,typedArray:T,componentsPerAttribute:P,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:c,normalize:!1}),D.img=L.length,L.push({index:D.img,typedArray:M,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}else{if(3===P&&n.defined(s)){U=new o.Cartesian3,K=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);for(var w=new Float32Array(T.byteLength/4+u),Y=K.length,J=(V=0,0);V<Y;V+=3,J+=4)w[J]=K[V],w[J+1]=K[V+1],w[J+2]=K[V+2],a.Matrix4.multiplyByPoint(s,o.Cartesian3.fromElements(w[J],w[J+1],w[J+2],b),U),w[J+3]=o.Cartographic.fromCartesian(U).height;T=w,c=(P=4)*Float32Array.BYTES_PER_ELEMENT}if(n.defined(l)){var k;I=new Float32Array(T.byteLength/4),v=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);k=3===P?o.Cartesian3.unpackArray(v):i.Cartesian4.unpackArray(v);_=a.Matrix4.multiply(l.sphereMatrix,l.geoMatrix,Mt),S=a.Matrix4.multiply(l.ellipsoidMatrix,l.geoMatrix,mt);a.Matrix4.inverse(S,S);for(O=new A.Ellipsoid(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){G=k[h];if(14!==d){a.Matrix4.multiplyByPoint(_,G,gt);var W;x=Dt((W=O.cartesianToCartographic(gt,Ft)).longitude,W.latitude,W.height,gt);a.Matrix4.multiplyByPoint(S,x,G)}3===P?(o.Cartesian3.pack(G,I,N),N+=3):(i.Cartesian4.pack(G,I,N),N+=4)}T=I}D.aPosition=L.length,L.push({index:D.aPosition,typedArray:T,componentsPerAttribute:P,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:c,normalize:!1})}return{bytesOffset:f,cartographicBounds:{max:g,min:F}}}function vt(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var C=o*i*Float32Array.BYTES_PER_ELEMENT,s=t.subarray(A,A+C);if(A+=C,!n.ignoreNormal){var y=n.vertexAttributes,l=n.attrLocation;l.aNormal=y.length,y.push({index:l.aNormal,typedArray:s,componentsPerAttribute:i,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:B,normalize:!1})}return{bytesOffset:A}}var _t={0:Uint32Array.BYTES_PER_ELEMENT,1:Float32Array.BYTES_PER_ELEMENT,2:Float64Array.BYTES_PER_ELEMENT};function St(t,e,r,a,n){var A,o=a,i=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,n.verticesCount,i>0){e.getUint16(o+r,!0),o+=Uint16Array.BYTES_PER_ELEMENT,o+=2*Uint8Array.BYTES_PER_ELEMENT;var B=i*Uint8Array.BYTES_PER_ELEMENT*4;A=F(t,o,o+B),o+=B;var C=n.vertexAttributes,s=n.attrLocation;s.aColor=C.length,C.push({index:s.aColor,typedArray:A,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0})}return{bytesOffset:o}}function Ot(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);return A+=Uint32Array.BYTES_PER_ELEMENT,o<=0?{bytesOffset:A}:(e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,A+=2*Uint8Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*Uint8Array.BYTES_PER_ELEMENT*4})}function Nt(t,e,r,a,n){var A=n,o=[],i=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E={};3===t&&(r.getUint32(A+a,!0),A+=Uint32Array.BYTES_PER_ELEMENT);var C=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var s=r.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,r.getUint8(A+a,!0),A+=Uint8Array.BYTES_PER_ELEMENT;var y=r.getUint8(A+a,!0);if(A+=Uint8Array.BYTES_PER_ELEMENT,A+=Uint8Array.BYTES_PER_ELEMENT,C>0){var l=0,d=null;1===s||3===s?(l=C*Uint32Array.BYTES_PER_ELEMENT,d=e.subarray(A,A+l)):(l=C*Uint16Array.BYTES_PER_ELEMENT,d=e.subarray(A,A+l),C%2!=0&&(l+=2)),E.indicesTypedArray=d,A+=l}E.indicesCount=C,E.indexType=s,E.primitiveType=y;var f=[],u=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var P=0;P<u;P++){var c=ut(r,a,e,A),p=c.string;A=c.bytesOffset,f.push(p),E.materialCode=p}if(0===u&&(E.materialCode="OSGBEmpty"),o.push(E),0!==A%4)A+=4-A%4}return{bytesOffset:A,arrIndexPackage:o}}function ht(t,e,r,B,C,s,l,d,f,u){var P,c,p=B,T=e.getUint32(p+r,!0);return C.nCompressOptions=T,p+=Uint32Array.BYTES_PER_ELEMENT,(T&y.VertexCompressOption.SVC_Vertex)==y.VertexCompressOption.SVC_Vertex?(P=function(t,e,r,B,C,s,y){var l=B,d=e.getUint32(l+r,!0);if(C.verticesCount=d,l+=Uint32Array.BYTES_PER_ELEMENT,d<=0)return{bytesOffset:l};var f=e.getUint16(l+r,!0);l+=Uint16Array.BYTES_PER_ELEMENT;var u=e.getUint16(l+r,!0);u=f*Int16Array.BYTES_PER_ELEMENT,l+=Uint16Array.BYTES_PER_ELEMENT;var P=e.getFloat32(l+r,!0);l+=Float32Array.BYTES_PER_ELEMENT;var c=new i.Cartesian4;c.x=e.getFloat32(l+r,!0),l+=Float32Array.BYTES_PER_ELEMENT,c.y=e.getFloat32(l+r,!0),l+=Float32Array.BYTES_PER_ELEMENT,c.z=e.getFloat32(l+r,!0),l+=Float32Array.BYTES_PER_ELEMENT,c.w=e.getFloat32(l+r,!0),l+=Float32Array.BYTES_PER_ELEMENT,C.vertCompressConstant=P,C.minVerticesValue=c;var p=d*f*Int16Array.BYTES_PER_ELEMENT,T=t.subarray(l,l+p);if(l+=p,n.defined(s)){var L=new Uint16Array(T.byteLength/2),D=new Uint16Array(T.buffer,T.byteOffset,T.byteLength/2),g=i.Cartesian4.unpackArray(D);for(let t=0,e=g.length;t<e;t++){let e=g[t];o.Cartesian3.multiplyByScalar(e,P,e),o.Cartesian3.add(e,c,e)}var F=a.Matrix4.multiply(s.sphereMatrix,s.geoMatrix,Mt),M=a.Matrix4.multiply(s.ellipsoidMatrix,s.geoMatrix,mt);a.Matrix4.inverse(M,M);var m=new A.Ellipsoid(6378137,6378137,6378137),I=0;for(let t=0,e=g.length;t<e;t++){let e=g[t];if(14!==y){a.Matrix4.multiplyByPoint(F,e,gt);let t=m.cartesianToCartographic(gt,Ft),r=Dt(t.longitude,t.latitude,t.height,gt);a.Matrix4.multiplyByPoint(M,r,e)}o.Cartesian3.subtract(e,c,e),o.Cartesian3.divideByScalar(e,P,e),i.Cartesian4.pack(e,L,I),I+=4}T=L}var v=C.vertexAttributes,_=C.attrLocation;return _.aPosition=v.length,v.push({index:_.aPosition,typedArray:T,componentsPerAttribute:f,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:u,normalize:!1}),{bytesOffset:l}}(t,e,r,p,C,f,u),p=P.bytesOffset):(p=(P=It(t,e,r,p,C,l,d,f,u)).bytesOffset,c=P.cartographicBounds),(T&y.VertexCompressOption.SVC_Normal)==y.VertexCompressOption.SVC_Normal?(P=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=2*o*Int16Array.BYTES_PER_ELEMENT,C=t.subarray(A,A+B);if(A+=B,!n.ignoreNormal){var s=n.vertexAttributes,y=n.attrLocation;y.aNormal=s.length,s.push({index:y.aNormal,typedArray:C,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:i,normalize:!1})}return{bytesOffset:A}}(t,e,r,p,C),p=P.bytesOffset):p=(P=vt(t,e,r,p,C)).bytesOffset,p=(P=Ot(0,e,r,p=(P=St(t,e,r,p,C)).bytesOffset)).bytesOffset,(T&y.VertexCompressOption.SVC_TexutreCoord)==y.VertexCompressOption.SVC_TexutreCoord?(P=function(t,e,r,a,n){n.texCoordCompressConstant=[],n.minTexCoordValue=[];var A=r,o=t.getUint16(r+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var B=0,C=0;C<o;C++){var s=t.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,A+=3*Uint8Array.BYTES_PER_ELEMENT;var y=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var l=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var d=t.getFloat32(A+a,!0);A+=Float32Array.BYTES_PER_ELEMENT,n.texCoordCompressConstant.push(d);var f=new i.Cartesian4;f.x=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.y=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.z=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.w=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,n.minTexCoordValue.push(f);var u=y*l*Int16Array.BYTES_PER_ELEMENT,P=e.subarray(A,A+u),c=(A+=u)%4;0!==c&&(A+=4-c);var p="aTexCoord"+B,T=n.vertexAttributes,L=n.attrLocation;if(L[p]=T.length,T.push({index:L[p],typedArray:P,componentsPerAttribute:l,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:l*Int16Array.BYTES_PER_ELEMENT,normalize:!1}),s){u=y*Float32Array.BYTES_PER_ELEMENT;var D=e.subarray(A,A+u);A+=u,n.texCoordZMatrix=!0,L[p="aTexCoordZ"+B]=T.length,T.push({index:L[p],typedArray:D,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:Float32Array.BYTES_PER_ELEMENT,normalize:!1})}B++}return{bytesOffset:A}}(e,t,p,r,C),p=P.bytesOffset):p=(P=Pt(e,t,p,r,C,s)).bytesOffset,(T&y.VertexCompressOption.SVC_TexutreCoordIsW)==y.VertexCompressOption.SVC_TexutreCoordIsW&&(C.textureCoordIsW=!0),{bytesOffset:p=(P=ct(e,t,p,r,C)).bytesOffset,cartographicBounds:c}}function Rt(t,e,r,a,A,o,i,B,C,s,y){3===t&&(r.getUint32(A,!0),A+=Uint32Array.BYTES_PER_ELEMENT,n.defined(C)&&C||(B=void 0));var l,d=A;d=(l=It(e,r,a,d,o,B,C,s,y)).bytesOffset;var f=l.cartographicBounds;if(d=(l=St(e,r,a,d=(l=vt(e,r,a,d,o)).bytesOffset,o)).bytesOffset,3!==t&&(d=(l=Ot(0,r,a,d)).bytesOffset),d=(l=ct(r,e,d=(l=Pt(r,e,d,a,o,i)).bytesOffset,a,o)).bytesOffset,3===t&&(l=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var i=0;i<o;i++){var B=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var C=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var y=B*C*_t[s],l=t.subarray(A,A+y);A+=y;var d=n.vertexAttributes,f=n.attrLocation,u="aCustom"+i;f[u]=d.length,d.push({index:f[u],typedArray:l,componentsPerAttribute:C,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1})}return{bytesOffset:A}}(e,r,a,d,o),d=l.bytesOffset),3==t){var u=ut(r,a,e,d);d=u.bytesOffset,o.customVertexAttribute=JSON.parse(u.string);var P="aCustom"+o.customVertexAttribute.TextureCoordMatrix,c="aCustom"+o.customVertexAttribute.VertexWeight,p="aCustom"+o.customVertexAttribute.VertexWeight_1;n.defined(o.attrLocation[P])&&(o.attrLocation.aTextureCoordMatrix=o.attrLocation[P],delete o.attrLocation[P]),n.defined(o.attrLocation[c])&&(o.attrLocation.aVertexWeight=o.attrLocation[c],delete o.attrLocation[c]),n.defined(o.attrLocation[p])&&(o.attrLocation.aVertexWeight_1=o.attrLocation[p],delete o.attrLocation[p]);for(var T=Object.keys(o.attrLocation),L=T.length,D=0;D<L;++D){var g=T[D];-1!==g.indexOf("aCustom")&&delete o.attrLocation[g]}var F=(d+a)%4;F&&(F=4-F),d+=F}return 3===t&&(l=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);return A+=Uint16Array.BYTES_PER_ELEMENT,e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*i*Float32Array.BYTES_PER_ELEMENT}}(0,r,a,d),d=l.bytesOffset),{bytesOffset:d,cartographicBounds:f}}function Gt(t){return 0!==t.length&&"ClampGroundAndObjectLinePass"===t[0].materialCode}var xt,bt=1,Ut=4,Kt=16,Ht=32,Vt=64,wt=128,Yt=512,Jt=1024;function kt(t,e,r,B,C,s,l,d,f,u,P,c,p){var T=t,L=0,D=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,u=n.defaultValue(u,n.defaultValue.EMPTY_OBJECT);for(var g=void 0,m=0;m<D;m++){3===d&&(e.getUint32(L+r,!0),L+=Uint32Array.BYTES_PER_ELEMENT);var I=(kt=ut(e,r,T,L)).string;if(n.defined(f)){var v=n.defaultValue(u[I],a.Matrix4.IDENTITY);g=new a.Matrix4,a.Matrix4.multiply(f,v,g)}n.defined(p)&&(p.geoMatrix=n.defaultValue(u[I],a.Matrix4.IDENTITY));var _=(L=kt.bytesOffset)%4;0!==_&&(L+=4-_);var S=nt;if(S=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,(fe={vertexAttributes:[],attrLocation:{},instanceCount:0,instanceMode:0,instanceIndex:-1}).ignoreNormal=B.ignoreNormal,3===d)switch(S){case Bt:S=At;break;case Et:S=it;break;case Ct:S=ot}if(S===it){3===d&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT),d>=2&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var O,N={};N.posUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.normalUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.colorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.secondColorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,3===d?(O=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT):(O=e.getUint16(L+r,!0),L+=Int16Array.BYTES_PER_ELEMENT);for(var h=[],R=0;R<O;R++){var G=e.getInt32(L+r,!0);h.push(G),L+=Int32Array.BYTES_PER_ELEMENT}N.texCoordUniqueIDs=h;var x=[];if(3===d){var b=e.getUint32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var U=0;U<b;U++){var K=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,x.push(K)}}N.vertexAttrUniqueIDs=x;var H=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var V=[],w={};if(H>0){var Y=(J=ut(e,r,T,L)).string;L=J.bytesOffset,w.materialCode=Y,V.push(w)}3===d&&((zt=(L+r)%4)&&(zt=4-zt),L+=zt);var J,k=new Object,W=e.getUint32(L+r,!0),Z=F(T,L+=Int32Array.BYTES_PER_ELEMENT,L+W);if(H>0?M.dracoDecodeMesh(rt,Z,W,fe,w,N,g,P,k,p):M.dracoDecodePointCloud(rt,Z,W,fe,N),n.defined(k.min)&&n.defined(k.max)||(k=void 0),L+=W,3===d)(zt=(L+r)%4)&&(zt=4-zt),(zt=((L=(J=ut(e,r,T,L+=zt)).bytesOffset)+r)%4)&&(zt=4-zt),L+=zt;B[I]={vertexPackage:fe,arrIndexPackage:V,cartographicBounds:k}}else if(S==ot&&3==d){var Q=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var z=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.minVerticesValue=new i.Cartesian4,fe.minTexCoordValue=[new A.Cartesian2,new A.Cartesian2],fe.texCoordCompressConstant=[new o.Cartesian3,new o.Cartesian3];V=[];for(var j=0;j<z;j++){var q=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.vertCompressConstant=e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT,fe.minVerticesValue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var $=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var tt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var et=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var at=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var st=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var yt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var lt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var dt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT,fe.minTexCoordValue[0].x=et,fe.minTexCoordValue[0].y=at,fe.minTexCoordValue[1].x=lt,fe.minTexCoordValue[1].y=dt,fe.texCoordCompressConstant[0].x=$,fe.texCoordCompressConstant[0].y=tt,fe.texCoordCompressConstant[1].x=st,fe.texCoordCompressConstant[1].y=yt;var ft=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;k=new Object;for(var Pt=0;Pt<ft;Pt++){var ct=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var pt=ct,Tt=0;pt!=Yt&&pt!=Jt||(Tt=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var Lt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var Dt=new Uint8Array(e.buffer,L+r,Lt);(zt=((L+=Uint8Array.BYTES_PER_ELEMENT*Lt)+r)%4)&&(zt=4-zt),L+=zt,Zt(q,pt,Tt,Dt,fe,Q,p,P,k,g)}L=(Qt=ut(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],j===z-1&&delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],j===z-1&&delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],j===z-1&&delete fe.attrLocation[Mt]);for(var mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt;var vt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var _t=0;_t<vt;_t++){w={};var St=e.getInt32(L+r,!0);if(L+=Int32Array.BYTES_PER_ELEMENT,St>0){var Ot=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var xt=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var bt,Ut,Kt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,13!==xt?(bt=new Uint8Array(e.buffer,L+r,Kt),L+=Uint8Array.BYTES_PER_ELEMENT*Kt):(bt=new Uint32Array(e.buffer,L+r,Kt),L+=Uint32Array.BYTES_PER_ELEMENT*Kt),(zt=(L+r)%4)&&(zt=4-zt),L+=zt,13!==xt?(Ut=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,St*Uint32Array.BYTES_PER_ELEMENT),X.decodeIndexBuffer(Ut,St,Uint32Array.BYTES_PER_ELEMENT,bt)):Ut=bt;var Ht,Vt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,w.indexType=Ot,0===Ot?Ht=new Uint16Array(St):1===Ot&&(Ht=new Uint32Array(St)),w.indicesCount=St;var wt=new Uint32Array(Ut.buffer,Ut.byteOffset,Ut.byteLength/4);Ht.set(wt,0),w.indicesTypedArray=Ht,w.primitiveType=xt;for(Pt=0;Pt<Vt;Pt++){var kt;Y=(kt=ut(e,r,T,L)).string;L=kt.bytesOffset,w.materialCode=Y}if(V.length>0&&13!==xt){var Wt=fe.preVertexCount;w.indicesTypedArray=wt.map((function(t){return t+Wt})),w.indexType=1}V.push(w),(zt=(L+r)%4)&&(zt=4-zt),L+=zt}}}fe.nCompressOptions=Q,2===V.length&&13===V[1].primitiveType&&V[1].indicesCount>=3&&(Xt=y.S3MEdgeProcessor.createEdgeDataByIndices(fe,V[1],s)),n.defined(k.min)&&n.defined(k.max)||(k=void 0),B[I]={vertexPackage:fe,arrIndexPackage:V,edgeGeometry:Xt,cartographicBounds:k}}else{var Xt;if(S===At||S===nt)L=(kt=Rt(d,T,e,r,L,fe,C,g,P,p,c)).bytesOffset,k=kt.cartographicBounds;else if(S===ot&&(L=(kt=ht(T,e,r,L,fe,C,g,P,p,c)).bytesOffset,k=kt.cartographicBounds,3==d)){var Qt;L=(Qt=ut(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var zt;gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],delete fe.attrLocation[Mt]);var jt;for(mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){var qt;-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt}Gt(V=(kt=Nt(d,T,e,r,L)).arrIndexPackage)&&(fe.clampRegionEdge=!0),2===V.length&&13===V[1].primitiveType&&V[1].indicesCount>=3&&(Xt=y.S3MEdgeProcessor.createEdgeDataByIndices(fe,V[1],s)),L=kt.bytesOffset,n.defined(k)&&n.defined(k.min)&&n.defined(k.max)||(k=void 0),B[I]={vertexPackage:fe,arrIndexPackage:V,edgeGeometry:Xt,cartographicBounds:k}}if(3!==d&&n.defined(l)&&l){var $t=e.getUint16(L+r,!0);if(L+=Uint16Array.BYTES_PER_ELEMENT,1===$t){var te=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var ee,re=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT;var ae=new Array(te),ne=new Array(te),Ae=new Array(te),oe=new Array(te);for(ee=0;ee<te;ee++){var ie=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,ae[ee]=ie;var Be=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,ne[ee]=Be;var Ee=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,Ae[ee]=Ee;for(var Ce=Ee*re,se=new Array(Ce),ye=0;ye<Ce;ye++){gt=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,se[ye]=gt}oe[ee]=se}}var le=new o.Cartesian3,de=new o.Cartesian3;le.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,B[I].min=le,B[I].max=de;var fe=B[I].vertexPackage;n.defined(fe.instanceBuffer)&&2===d&&(fe.instanceBounds=new Float32Array(6),o.Cartesian3.pack(le,fe.instanceBounds,0),o.Cartesian3.pack(de,fe.instanceBounds,3))}if(3===d){var ue=new o.Cartesian3;ue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var Pe=new o.Cartesian3;Pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var ce=new o.Cartesian3;ce.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var pe=new o.Cartesian3;pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT}}}function Wt(t,e,r){var a=t.typedArray,n=new r(a.length+e.length);n.set(a,0),n.set(e,a.length),t.typedArray=n}function Zt(t,e,r,i,B,C,s,y,l,d){var f,u=0,P=B.vertexAttributes,c=B.attrLocation;switch(e){case Ut:case Kt:case Ht:u=2*Uint16Array.BYTES_PER_ELEMENT,0!=(16&C)||e!==Kt&&e!==Ht||(u=2*Float32Array.BYTES_PER_ELEMENT),f=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*u);break;case Vt:case wt:u=4*Uint8Array.BYTES_PER_ELEMENT,f=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,4*t);break;case Yt:case Jt:u=Float32Array.BYTES_PER_ELEMENT*r,f=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*r*4);break;default:u=4*Uint16Array.BYTES_PER_ELEMENT,f=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*u)}switch(X.decodeVertexBuffer(f,t,u,i,i.length),e){case bt:var p=new Uint16Array(f.buffer,0,f.length/2),T=E.ComponentDatatype.SHORT;if(n.defined(s)){var L=o.Cartesian3.unpackArray(p);for(let t=0,e=L.length;t<e;t++){let e=L[t];o.Cartesian3.multiplyByScalar(e,B.vertCompressConstant,e),o.Cartesian3.add(e,B.minVerticesValue,e)}var D=a.Matrix4.multiply(s.sphereMatrix,s.geoMatrix,Mt),g=a.Matrix4.multiply(s.ellipsoidMatrix,s.geoMatrix,mt);a.Matrix4.inverse(g,g);var F=new A.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=L.length;t<e;t++){let e=L[t];a.Matrix4.multiplyByPoint(D,e,gt);let r=F.cartesianToCartographic(gt,Ft),n=Dt(r.longitude,r.latitude,r.height,gt);a.Matrix4.multiplyByPoint(g,n,e)}var M=new Array(3*L.length);o.Cartesian3.packArray(L,M),p=new Float32Array(M),T=E.ComponentDatatype.FLOAT}if(void 0!==(R=c.aPosition)?(Wt(P[R],p,Uint16Array),B.preVertexCount=B.verticesCount,B.verticesCount+=t):(c.aPosition=P.length,P.push({index:c.aPosition,typedArray:p,componentsPerAttribute:4,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}),B.verticesCount=t),!n.defined(s)&&y){var m=new o.Cartographic,I=new o.Cartographic,v=new Float32Array(2*t),_=new Float64Array(2*t),S=new o.Cartesian3,O=new o.Cartesian3,N=new o.Cartographic;F=xt?new A.Ellipsoid(6378137,6378137,6356752.314245179):new A.Ellipsoid(6378137,6378137,6378137);for(var h=0;h<t;h++)a.Matrix4.multiplyByPoint(d,o.Cartesian3.fromElements(p[4*h]*B.vertCompressConstant+B.minVerticesValue.x,p[4*h+1]*B.vertCompressConstant+B.minVerticesValue.y,p[4*h+2]*B.vertCompressConstant+B.minVerticesValue.z,S),O),N=F.cartesianToCartographic(O,Ft),_[2*h]=N.longitude,_[2*h+1]=N.latitude,0===h?(m.longitude=N.longitude,m.latitude=N.latitude,I.longitude=N.longitude,I.latitude=N.latitude):(m.longitude=Math.max(N.longitude,m.longitude),m.latitude=Math.max(N.latitude,m.latitude),I.longitude=Math.min(N.longitude,I.longitude),I.latitude=Math.min(N.latitude,I.latitude));for(h=0;h<t;h++)v[2*h]=_[2*h]-I.longitude,v[2*h+1]=_[2*h+1]-I.latitude;c.img=P.length,P.push({index:c.img,typedArray:v,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),l.max=m,l.min=I}break;case Ut:var R=c.aNormal,G=new Int16Array(f.buffer,0,f.length/2);void 0!==R?Wt(P[R],G,Uint16Array):(c.aNormal=P.length,P.push({index:c.aNormal,typedArray:G,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Kt:var x=new Uint16Array(f.buffer,0,f.length/2),b=(R=c.aTexCoord0,T=E.ComponentDatatype.SHORT,Uint16Array);0==(16&C)&&(T=E.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(f.buffer,0,f.length/4)),void 0!==R?Wt(P[R],x,b):(c.aTexCoord0=P.length,P.push({index:c.aTexCoord0,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Ht:x=new Uint16Array(f.buffer,0,f.length/2),R=c.aTexCoord1,T=E.ComponentDatatype.SHORT,b=Uint16Array;0==(16&C)&&(T=E.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(f.buffer,0,f.length/4)),void 0!==R?Wt(P[R],x,b):(c.aTexCoord1=P.length,P.push({index:c.aTexCoord1,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Vt:void 0!==(R=c.aColor)?Wt(P[R],f,Uint8Array):(c.aColor=P.length,P.push({index:c.aColor,typedArray:f,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:0,normalize:!0}));break;case wt:void 0!==(R=c.aSecondColor)?Wt(P[R],f,Uint8Array):(c.aSecondColor=P.length,P.push({index:c.aSecondColor,typedArray:f,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Yt:x=new Float32Array(f.buffer,0,f.length/4);void 0!==(R=c.aCustom0||c.aVertexWeight)?Wt(P[R],x,Float32Array):(c.aCustom0=P.length,P.push({index:c.aCustom0,typedArray:x,componentsPerAttribute:r,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Jt:var U=new Float32Array(f.buffer,0,f.length/4);void 0!==(R=c.aCustom1||c.aTextureCoordMatrix)?Wt(P[R],U,Float32Array):(c.aCustom1=P.length,P.push({index:c.aCustom1,typedArray:U,componentsPerAttribute:r,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}))}}function Xt(t,e,r,A,o){var i={},B=[],E=new a.Matrix4,C=t;o=n.defaultValue(o,{});for(var s=0;s<16;s++)E[s]=e.getFloat64(r+A,!0),r+=Float64Array.BYTES_PER_ELEMENT;i.matrix=E,i.skeletonNames=B;var y=e.getUint32(r+A,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var l=0;l<y;l++){var d=ut(e,A,C,r),f=d.string;r=d.bytesOffset,B.push(f),o[f]=E}return{byteOffset:r,geode:i}}function Qt(t){var e=t.indexOf("Geometry");if(-1===e)return t;var r=t.substring(e,t.length);return t.replace(r,"")}function zt(t,e,r,i,B,E,C,y){var l={},d=r.getFloat32(i+B,!0);i+=Float32Array.BYTES_PER_ELEMENT;var f=r.getUint16(i+B,!0);i+=Uint16Array.BYTES_PER_ELEMENT,l.rangeMode=f,l.rangeList=d;var u=new o.Cartesian3;u.x=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,u.y=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,u.z=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT;var P=r.getFloat64(i+B,!0);if(i+=Float64Array.BYTES_PER_ELEMENT,n.defined(C)&&14!==y){var c=a.Matrix4.clone(C.sphereMatrix,Mt),p=a.Matrix4.clone(C.ellipsoidMatrix,mt);a.Matrix4.inverse(p,p);var T=new A.Ellipsoid(6378137,6378137,6378137);a.Matrix4.multiplyByPoint(c,u,gt);let t=T.cartesianToCartographic(gt,Ft),e=Dt(t.longitude,t.latitude,t.height,gt);a.Matrix4.multiplyByPoint(p,e,u)}if(l.boundingSphere=new s.BoundingSphere(u,P),3===t){var L=new o.Cartesian3;L.x=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,L.y=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,L.z=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT;var D=new o.Cartesian3;D.x=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,D.y=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,D.z=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT;var g=new o.Cartesian3;g.x=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,g.y=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,g.z=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT;var F=new o.Cartesian3;F.x=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,F.y=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,F.z=r.getFloat64(i+B,!0),i+=Float64Array.BYTES_PER_ELEMENT,l._obb={xExtent:D,yExtent:g,zExtent:F,obbCenter:L}}var M=e,m=(_=ut(r,B,M,i)).string;i=_.bytesOffset,m=Qt(m=(m=m.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")).replace(/\\/gi,"/")),l.childTile=m,l.geodes=[];var I=r.getUint32(i+B,!0);i+=Uint32Array.BYTES_PER_ELEMENT;for(var v=0;v<I;v++){var _;i=(_=Xt(e,r,i,B,E)).byteOffset,l.geodes.push(_.geode)}return 3===t&&(i=(_=ut(r,B,M,i)).bytesOffset),{pageLOD:l,bytesOffset:i}}function jt(t,e,r,a,n,A,o){var i=0,B={},E=[],C=r.getUint32(i+a,!0);i+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<C;s++){var y=zt(t,e,r,i,a,n,A,o);i=y.bytesOffset,E.push(y.pageLOD)}return B.pageLods=E,B}function qt(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n["aTextureBatchId"+r]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0})}function $t(t,e,r,a){for(var A=r.length,o=0;o<A;o++)for(var i=r[o],B=i.subName.split("_")[0],E=i.subVertexOffsetArr,C=0;C<E.length;C++){var s=E[C],y=s.geoName,l=s.offset,d=s.count,f=s.texUnitIndex,u=e[y].vertexPackage.verticesCount,P=a[y];n.defined(P)||(P=a[y]={});var p=P[f];n.defined(p)||(p=P[f]=new Float32Array(u),c.arrayFill(p,-1));var T=n.defined(t)?t[B]:o;c.arrayFill(p,T,l,l+d)}}function te(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n[1===r?"instanceId":"batchId"]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:r})}function ee(t,e,r,a,A){var o=0,i=t,B=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<B;E++){var C=ut(e,r,i,o),s=C.string;o=C.bytesOffset;var l=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var d={};a[s].pickInfo=d;var f=a[s].edgeGeometry;if(-1==a[s].vertexPackage.instanceIndex){for(var u=new Float32Array(a[s].vertexPackage.verticesCount),P=0;P<l;P++){var p=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var L=0,D=0;d[p]={batchId:P};for(var g=0;g<T;g++)D=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,L=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,c.arrayFill(u,P,D,D+L);d[p].vertexColorOffset=D,d[p].vertexCount=L}te(a[s].vertexPackage,u,void 0)}else{var F=a[s].vertexPackage.instanceCount;a[s].vertexPackage.instanceBuffer,a[s].vertexPackage.instanceMode;var M=new Float32Array(F),m=0;for(P=0;P<l;P++){var I=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(g=0;g<T;g++){var v=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,M[m]=m,void 0===d[I]&&(d[I]={vertexColorCount:1,instanceIds:[],vertexColorOffset:m}),d[I].instanceIds.push(v),m++,3===A){L=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT}}}te(a[s].vertexPackage,M,1)}f=a[s].edgeGeometry;if(n.defined(f)){var _,S,O=f.regular.instancesData,N=y.S3MEdgeProcessor.RegularInstanceStride;if(n.defined(O))for(S=O.length,_=0;_<S;_+=N){var h=O[_+9];O[_+9]=u[h]}var R=f.silhouette.instancesData;if(N=y.S3MEdgeProcessor.SilhouetteInstanceStride,n.defined(R))for(S=R.length,_=0;_<S;_+=N){h=R[_+12];R[_+12]=u[h]}}}}function re(t){return t<1e-10&&t>-1e-10}function ae(t,e,r,a,A,o,i,B){var E=new DataView(t),s=new Uint8Array(t),y=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var l=C.getStringFromTypedArray(s,r,y);l=l.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,""),r+=y;var d=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var f=0;f<d;f++){var c={},p=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;var T=E.getUint16(r,!0);r+=Uint16Array.BYTES_PER_ELEMENT,c.rangeMode=T,c.rangeList=p;var L={};L.x=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.y=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.z=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT;var D=E.getFloat64(r,!0);r+=Float64Array.BYTES_PER_ELEMENT,c.boundingSphere={center:L,radius:D},y=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var g=C.getStringFromTypedArray(s,r,y);r+=y,g=Qt(g=g.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),c.childTile=g}var F={},M=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;M>=3&&(E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var m=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var I=new Uint8Array(t,r,m),v=r+m,_=P.pako.inflate(I).buffer;B.push(_),E=new DataView(_);s=new Uint8Array(_);r=0;var S=E.getUint32(r,!0),O=ft(E,_,r+=Uint32Array.BYTES_PER_ELEMENT),N=O.buffer;r=O.byteOffset;var h=jt(M,N,E,O.dataViewByteOffset),R=r%4;0!==R&&(r+=4-R),kt((O=ft(E,_,r)).buffer,E,O.dataViewByteOffset,F,!1,void 0,void 0,M),r=O.byteOffset,3!==M&&((O=ft(E,_,r)).buffer,r=O.byteOffset);var G={};!function(t,e,r,a,A,o,i,B,E,s){var y=B,l=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var d={},f=0;f<l;f++){var P=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var c=C.getStringFromTypedArray(o,y-B,P),p=(y+=P)%4;0!==p&&(y+=4-p),i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var T=i.getUint8(y,!0);y+=Uint8Array.BYTES_PER_ELEMENT;var L=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var D=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var g=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var F=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var M,m=i.getUint32(y,!0);if(y+=Uint32Array.BYTES_PER_ELEMENT,a&&T){var I=y-B;M=o.subarray(I,I+F),y+=F}var v=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var _=[],S=0;S<v;S++){P=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var O=C.getStringFromTypedArray(o,y-B,P);y+=P,_.push(O),r[O]=c}var N=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var h=[];for(S=0;S<N;S++){P=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var R=C.getStringFromTypedArray(o,y-B,P);y+=P,h.push(R)}var G=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var x=[],b=void 0,U=c;if(a)b=e[c]={};else{var K=r[c];for(U=K;n.defined(K)&&K!==c;)U=K,K=r[K];n.defined(U)&&(b=e[U])}var H=0;for(S=0;S<G;S++){P=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var V=C.getStringFromTypedArray(o,y-B,P);if(y+=P,a){var w=V.split("_")[0];n.defined(b[w])?H++:b[w]=S-H}var Y=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var J=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var k=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var W=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var Z=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var X=[],Q=0;Q<Z;Q++){P=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var z=C.getStringFromTypedArray(o,y-B,P);y+=P;var j=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var q=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var $=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT,X.push({geoName:z,offset:j,count:q,texUnitIndex:$})}x.push({subName:V,offsetX:Y,offsetY:J,width:k,height:W,subVertexOffsetArr:X})}$t(b,t,x,d);var rt=!1;n.defined(M)&&g===u.S3MPixelFormat.CRN_DXT5&&tt&&(M=et({data:M},s).bufferView,rt=!0),E[c]={id:c,rootTextureName:U,width:L,height:D,compressType:g,size:F,format:m,textureData:M,subTexInfos:x,requestNames:h,isDXT:rt}}for(var z in d)if(d.hasOwnProperty(z)){var at=t[z].vertexPackage,nt=d[z];for(var $ in nt)nt.hasOwnProperty($)&&qt(at,nt[$],$)}}(F,a,A,o,0,(O=ft(E,_,r)).buffer,E,O.dataViewByteOffset,G,B),r=O.byteOffset;var x=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var b=s.subarray(r,r+x),K=C.getStringFromTypedArray(b);r+=x;var H=JSON.parse(K);(3===M&&(S=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),(S&at)==at)&&(ee((O=ft(E,_,r)).buffer,E,O.dataViewByteOffset,F,M),r=O.byteOffset);if(1==M){var V=h.pageLods,w=!0;for(f=0;f<V.length;f++){var Y=V[f];w=""===Y.childTile;for(var J=Y.geodes,k=0;k<J.length;k++)for(var W=J[k].skeletonNames,X=0;X<W.length;X++){var Q=W[X];if(w){var z=F[Q].vertexPackage;z.boundingSphere=U.calcBoundingSphereInWorker(1,z)}}}}i[l]={result:!0,groupNode:h,geoPackage:F,matrials:H,texturePackage:G,version:Z.S3M4,dataVersion:M,rootBatchIdMap:a,ancestorMap:A},v<e&&ae(t,e,v,a,A,!1,i,B)}function ne(e,r){var a=e.buffer;if(xt=e.ellipsoid,e.isOSGB){if(n.defined(st)||new Promise((function(e,r){t(["./OSGBToS3M-caf1aa52"],e,r)})).then((function(t){(st=t.default).onRuntimeInitialized=function(){yt=!0},lt=st.cwrap("OSGBToS3MB","number",["number","number","number","number"])})),!yt)return null;var A;switch(e.suffix){case"dae":case"DAE":A=4;break;case"x":case"X":A=2;break;default:A=0}a=function(t,e,r){var a=st._malloc(20*e),n=st._malloc(Uint8Array.BYTES_PER_ELEMENT*e);st.HEAPU8.set(t,n/Uint8Array.BYTES_PER_ELEMENT);var A=lt(n,e,a,r),o=new Uint8Array(st.HEAPU8.buffer,a,A);return t=null,t=new Uint8Array(o).buffer,st._free(a),st._free(n),t}(new Uint8Array(a),a.byteLength,A)}var o=e.isS3MZ,i=e.fileType,B=e.supportCompressType,E=e.bVolume,s=e.isS3MBlock,l=e.modelMatrix,d=e.materialType,f=e.isCoverImageryLayer,c=e.transformPar,p=null,T=null,L=null;if(E&&e.volbuffer.byteLength<8&&(E=!1),E){var D=e.volbuffer,g=new Uint8Array(D,8),F=P.pako.inflate(g).buffer,M=new Float64Array(F,0,1),m=new Uint32Array(F,48,1);if(0===M[0]||3200===m[0]||3201===m[0]){var I=0;0==M[0]&&(I=8),r.push(F);var v=new Float64Array(F,I,6),_=v[0],S=v[1],O=v[2],N=v[3],h=v[4]<v[5]?v[4]:v[5],R=v[4]>v[5]?v[4]:v[5];T={left:_,top:S,right:O,bottom:N,minHeight:h,maxHeight:R,width:(p=new dt(_,N,O,S,h,R)).width,length:p.length,height:p.height};var G=new Uint32Array(F,48+I,7),x=G[0],b=G[1],K=G[2],H=G[3];L={nFormat:x,nSideBlockCount:b,nBlockLength:K,nLength:H,nWidth:G[4],nHeight:G[5],nDepth:G[6],imageArray:new Uint8Array(F,76+I,H*H*4)}}}var V=0,w={};w.ignoreNormal=e.ignoreNormal;var Y=e.rootBatchIdMap||{},J=e.ancestorMap||{},k={},W=new DataView(a),X=W.getFloat32(V,!0);if(V+=Float32Array.BYTES_PER_ELEMENT,s)return W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT,ae(a,a.byteLength,V,Y,J,e.isRoot,k,r),k;var Q=!1;if(X>=3&&(W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),X>=2&&(W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),re(X-1)||re(X-2)||re(X-3)||X>2.09&&X<2.11){var z=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT;var j=new Uint8Array(a,V,z);a=P.pako.inflate(j).buffer,r.push(a),W=new DataView(a),V=0}else if(X>1.199&&X<1.201){z=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT,r.push(a)}else{Q=!0,V=0;z=W.getInt32(V,!0);if(V+=Int32Array.BYTES_PER_ELEMENT,V+=Uint8Array.BYTES_PER_ELEMENT*z,o){W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT;g=new Uint8Array(a,V);a=P.pako.inflate(g).buffer,r.push(a),W=new DataView(a),V=0}}var q=W.getUint32(V,!0),$=ft(W,a,V+=Uint32Array.BYTES_PER_ELEMENT),tt=$.buffer;V=$.byteOffset;var et={},rt=jt(X,tt,W,$.dataViewByteOffset,et,c,i),nt=V%4;0!==nt&&(V+=4-nt);var At=X>2.09&&3!==X;if(kt(($=ft(W,a,V)).buffer,W,$.dataViewByteOffset,w,Q,r,At,X,l,et,f,e.fileType,c),V=$.byteOffset,At)for(var ot=0;ot<rt.pageLods.length;ot++)for(var it=rt.pageLods[ot],Bt=it.geodes,Et=0;Et<Bt.length;Et++)for(var Ct=Bt[Et].skeletonNames,Pt=0;Pt<Ct.length;Pt++){var ct=Ct[Pt];n.defined(w[ct].max)&&(n.defined(it.max)?(it.max.x=Math.max(w[ct].max.x,it.max.x),it.max.y=Math.max(w[ct].max.y,it.max.y),it.max.z=Math.max(w[ct].max.z,it.max.z),it.min.x=Math.min(w[ct].min.x,it.min.x),it.min.y=Math.min(w[ct].min.y,it.min.y),it.min.z=Math.min(w[ct].min.z,it.min.z)):(it.max=w[ct].max,it.min=w[ct].min))}3!==X&&(($=ft(W,a,V)).buffer,V=$.byteOffset);var pt={};!function(t,e,r,a,n,A){var o=0,i=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E=ut(r,a,e,o),C=E.string,s=(o=E.bytesOffset)%4;0!==s&&(o+=4-s);var l=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var d=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var P=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var c=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.subarray(o,o+c);o+=c;var L=null,D=P;P===y.S3MCompressType.enrS3TCDXTN&&1!=t?(y.DXTTextureDecode.decode(L,d,f,T,p),L=p>u.S3MPixelFormat.BGR||p===u.S3MPixelFormat.LUMINANCE_ALPHA?new Uint8Array(d*f*4):new Uint16Array(d*f),y.DXTTextureDecode.decode(L,d,f,T,p),A.push(L.buffer),P=0):L=T,n[C]={id:C,width:d,height:f,compressType:P,oriCompressType:D,nFormat:p,imageBuffer:L,mipmapLevel:l}}}(B,($=ft(W,a,V)).buffer,W,$.dataViewByteOffset,pt,r),V=$.byteOffset;var Tt=W.getUint32(V,!0);V+=Uint32Array.BYTES_PER_ELEMENT;var Lt=new Uint8Array(a).subarray(V,V+Tt),Dt=C.getStringFromTypedArray(Lt);V+=Tt,Dt=Dt.replace(/\n\0/,"");var gt=JSON.parse(Dt);(3===X&&(q=W.getUint32(V,!0),V+=Uint32Array.BYTES_PER_ELEMENT),(q&at)==at)&&ee(($=ft(W,a,V)).buffer,W,$.dataViewByteOffset,w,X);if(1==X){var Ft=rt.pageLods,Mt=!0;for(ot=0;ot<Ft.length;ot++){var mt=Ft[ot];Mt=""===mt.childTile;for(var It=mt.geodes,vt=0;vt<It.length;vt++){Ct=It[vt].skeletonNames;for(var _t=0;_t<Ct.length;_t++){var St=Ct[_t];if(Mt){var Ot=w[St].vertexPackage;Ot.boundingSphere=U.calcBoundingSphereInWorker(i,Ot)}}}}}return"BatchPBR"===d&&function(t,e,r){for(var a in delete t.ignoreNormal,t)if(t.hasOwnProperty(a)){var A=t[a],o=A.arrIndexPackage;if(o.length<1)continue;if(1===o.length||2===o.length&&13===o[1].primitiveType){var i=A.vertexPackage.attrLocation.aTextureCoordMatrix;if(void 0!==i){if((B=(O=A.vertexPackage.vertexAttributes[i]).typedArray)[0]<0)continue}else if(void 0!==(i=A.vertexPackage.attrLocation.aTextureCoordMatrix||A.vertexPackage.attrLocation.aTexCoord0)){O=A.vertexPackage.vertexAttributes[i];var B=new Float32Array(O.typedArray.buffer,O.typedArray.byteOffset,O.typedArray.byteLength/4);if(3===O.componentsPerAttribute&&B[2]<0)continue}}var E,C,s=0,l={},d=void 0;for(E=0,C=o.length;E<C;E++)13!==o[E].primitiveType?s+=o[E].indicesTypedArray.byteLength:d=o[E],0===E&&(l.indicesCount=0,l.indexType=o[E].indexType,l.primitiveType=o[E].primitiveType,l.materialCode=o[E].materialCode);l.indicesCount=s/2;var f=A.vertexPackage.verticesCount>65535?new Uint32Array(s/2):new Uint16Array(s/2),u=0;for(E=0,C=o.length;E<C;E++)if(13!==(N=o[E]).primitiveType){var P=0===N.indexType?Uint16Array:Uint32Array,c=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4,p=new P(N.indicesTypedArray.buffer,N.indicesTypedArray.byteOffset,c);f.set(p,u),u+=p.length}l.indicesTypedArray=f,A.arrIndexPackage=[l],n.defined(d)&&(A.arrIndexPackage.push(d),A.edgeGeometry=y.S3MEdgeProcessor.createEdgeDataByIndices(A.vertexPackage,d));var T=2*o.length*4,L=new Float32Array(T),D={};for(E=0,C=r.material.length;E<C;E++)D[(h=r.material[E].material).id]=h;for(E=0,C=o.length;E<C;E++)if(h=D[(N=o[E]).materialCode]){var g=h.pbrMetallicRoughness;if(g){L[8*E]=g.metallicFactor,L[8*E+1]=g.roughnessFactor,L[8*E+2]=h.alphaCutoff;var F=""===h.alphaMode?0:1,M="none"===h.cullMode?0:1;L[8*E+3]=M|F<<16,L[8*E+4]=g.emissiveFactor.x,L[8*E+5]=g.emissiveFactor.y,L[8*E+6]=g.emissiveFactor.z,L[8*E+7]=0,h.pbrIndex=E}}var m="PBRMaterialParam_"+a;for(E=0,C=r.material.length;E<C;E++)if((h=r.material[E].material).id===l.materialCode){h.textureunitstates.push({textureunitstate:{addressmode:{u:0,v:0,w:0},filteringoption:0,filtermax:2,filtermin:2,id:m,texmodmatrix:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],url:""}});break}var I,v,_=A.vertexPackage,S=_.attrLocation.aTexCoord1;if(void 0!==S){var O=_.vertexAttributes[S];I=new Float32Array(2*_.verticesCount),O.typedArray=I}else I=new Float32Array(2*_.verticesCount),S=_.vertexAttributes.length,_.attrLocation.aTexCoord1=S,_.vertexAttributes.push({index:S,typedArray:I,componentsPerAttribute:2,componentDatatype:5126,offsetInBytes:0,strideInBytes:8,normalize:!1});for(void 0!==(S=_.attrLocation.aColor)&&(v=(O=_.vertexAttributes[S]).typedArray),E=0,C=o.length;E<C;E++){var N,h;if((h=D[(N=o[E]).materialCode])&&h.pbrMetallicRoughness)for(var R=h.pbrMetallicRoughness.baseColor,G=void 0!==v,x=h.pbrIndex,b=(f=N.indicesTypedArray,0),U=(f=0===N.indexType?new Uint16Array(f.buffer,f.byteOffset,f.byteLength/2):new Uint32Array(f.buffer,f.byteOffset,f.byteLength/4)).length;b<U;b++){var K=f[b];I[2*K]=x,G&&(v[4*K]=255*R.x,v[4*K+1]=255*R.y,v[4*K+2]=255*R.z,v[4*K+3]=255*R.w)}}e[m]={id:m,width:2*o.length,height:1,compressType:0,nFormat:25,imageBuffer:L,mipmapLevel:0}}}(w,pt,gt),{result:!0,groupNode:rt,geoPackage:w,matrials:gt,texturePackage:pt,version:Z.S3M4,dataVersion:X,volImageBuffer:L,volBounds:T}}function Ae(){n.defined(q)&&n.defined(rt)&&(q.onRuntimeInitialized=function(){tt=!0},self.onmessage=e(ne),self.postMessage(!0))}return function(r){if("undefined"==typeof WebAssembly)return self.onmessage=e(ne),void self.postMessage(!0);var A=r.data.webAssemblyConfig;return n.defined(A)?a.FeatureDetection.isInternetExplorer()?t([s.buildModuleUrl("ThirdParty/Workers/ie-webworker-promise-polyfill.js")],(function(e){return self.Promise=e,-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),q=t,Ae()):(q=t,Ae())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){rt=t,Ae()}))):(rt=t(),Ae())}))})):-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),q=t,Ae()):(q=t,Ae())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){rt=t,Ae()}))):(rt=t(),Ae())})):void 0}}));
