export default {
  url: /\/api\/dialog\/.+/,
  method: "get",
  response: () => {
    return {
      title: "某能源项目",
      introduce:
        "本项目采用“交叉融合引领、应用实践驱动”的人才培养理念，定位于能源动力和新一代信息技术交叉技术领域。 本项目依托浙江大学工程师学院平台建设，同时结合相关专业学院的学科优势，打造产、学、研、用相结合的工程教育创新机制，整合优质资源，积极开展技术创新研发与行业高端技术训练，深入工程实践，通过承担多学科交叉的重大工程项目带动人才培养。 项目针对新型能源系统的信息物理融合调控、工业物联网、工业大数据、人工智能等跨学科问题，在“互联网+”、新一代信息技术与能源环保产业深度融合的时代，着力培养一批智慧能源新技术、新模式、新业态所急需应用型、复合型、高层次的高级工程专业人才。",
      poster: "./mock/project1.jpeg",
      stage: 2,
      plans: ["立项审批", "招投标", "里程碑1", "里程碑2", "结项"],
    }
  },
};
