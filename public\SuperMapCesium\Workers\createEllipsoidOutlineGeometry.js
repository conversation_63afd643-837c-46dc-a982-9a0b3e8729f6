define(["./when-b60132fc","./EllipsoidOutlineGeometry-6312b9fc","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,o,d,u,b,c,f,l,s,y,m,p,G){"use strict";return function(a,r){return e.defined(a.buffer)&&(a=t.EllipsoidOutlineGeometry.unpack(a,r)),t.EllipsoidOutlineGeometry.createGeometry(a)}}));
