define(["./AttributeCompression-90851096","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./when-b60132fc","./TerrainEncoding-895d4619","./IndexDatatype-8a5eead4","./Check-7b2a090c","./Math-119be1a3","./OrientedBoundingBox-08964f84","./createTaskProcessorWorker","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipsoidTangentPlane-ce9a1fbb","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./GeometryAttribute-06a41648","./PolygonPipeline-d328cdf1","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-ed1a6bf4"],(function(e,i,t,n,s,r,h,u,o,a,d,p,l,f,g,c,m,x,v,C,w,B,y,b){"use strict";var A={clipTriangleAtAxisAlignedThreshold:function(e,i,t,n,r,h){var u,o,a;s.defined(h)?h.length=0:h=[],i?(u=t<e,o=n<e,a=r<e):(u=t>e,o=n>e,a=r>e);var d,p,l,f,g,c,m=u+o+a;return 1===m?u?(d=(e-t)/(n-t),p=(e-t)/(r-t),h.push(1),h.push(2),1!==p&&(h.push(-1),h.push(0),h.push(2),h.push(p)),1!==d&&(h.push(-1),h.push(0),h.push(1),h.push(d))):o?(l=(e-n)/(r-n),f=(e-n)/(t-n),h.push(2),h.push(0),1!==f&&(h.push(-1),h.push(1),h.push(0),h.push(f)),1!==l&&(h.push(-1),h.push(1),h.push(2),h.push(l))):a&&(g=(e-r)/(t-r),c=(e-r)/(n-r),h.push(0),h.push(1),1!==c&&(h.push(-1),h.push(2),h.push(1),h.push(c)),1!==g&&(h.push(-1),h.push(2),h.push(0),h.push(g))):2===m?u||t===e?o||n===e?a||r===e||(p=(e-t)/(r-t),l=(e-n)/(r-n),h.push(2),h.push(-1),h.push(0),h.push(2),h.push(p),h.push(-1),h.push(1),h.push(2),h.push(l)):(c=(e-r)/(n-r),d=(e-t)/(n-t),h.push(1),h.push(-1),h.push(2),h.push(1),h.push(c),h.push(-1),h.push(0),h.push(1),h.push(d)):(f=(e-n)/(t-n),g=(e-r)/(t-r),h.push(0),h.push(-1),h.push(1),h.push(0),h.push(f),h.push(-1),h.push(2),h.push(0),h.push(g)):3!==m&&(h.push(0),h.push(1),h.push(2)),h},computeBarycentricCoordinates:function(e,i,t,r,h,u,o,a,d){var p=t-o,l=o-h,f=u-a,g=r-a,c=1/(f*p+l*g),m=i-a,x=e-o,v=(f*x+l*m)*c,C=(-g*x+p*m)*c,w=1-v-C;return s.defined(d)?(d.x=v,d.y=C,d.z=w,d):new n.Cartesian3(v,C,w)},computeLineSegmentLineSegmentIntersection:function(e,i,n,r,h,u,o,a,d){var p=(a-u)*(n-e)-(o-h)*(r-i);if(0!==p){var l=((o-h)*(i-u)-(a-u)*(e-h))/p,f=((n-e)*(i-u)-(r-i)*(e-h))/p;return l>=0&&l<=1&&f>=0&&f<=1?(s.defined(d)||(d=new t.Cartesian2),d.x=e+l*(n-e),d.y=i+l*(r-i),d):void 0}}},I=32767,M=16383,H=[],T=[],z=[],E=new n.Cartographic,N=new n.Cartesian3,V=[],R=[],O=[],U=[],P=[],F=new n.Cartesian3,S=new i.BoundingSphere,D=new a.OrientedBoundingBox,L=new t.Cartesian2,X=new n.Cartesian3;function k(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}k.prototype.clone=function(e){return s.defined(e)||(e=new k),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},k.prototype.initializeIndexed=function(e,i,t,n,s){this.uBuffer=e,this.vBuffer=i,this.heightBuffer=t,this.normalBuffer=n,this.index=s,this.first=void 0,this.second=void 0,this.ratio=void 0},k.prototype.initializeFromClipResult=function(e,i,t){var n=i+1;return-1!==e[i]?t[e[i]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=t[e[n]],++n,this.second=t[e[n]],++n,this.ratio=e[n],++n),n},k.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},k.prototype.isIndexed=function(){return s.defined(this.index)},k.prototype.getH=function(e,i){if(s.defined(this.index))return this.heightBuffer[this.index];var t=this.first.getH(e,i),n=this.second.getH(e,i);return 0===i+t/I*e||0===i+n/I*e?0:o.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},k.prototype.getU=function(){return s.defined(this.index)?this.uBuffer[this.index]:o.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},k.prototype.getV=function(){return s.defined(this.index)?this.vBuffer[this.index]:o.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};var W=new t.Cartesian2,K=-1,Y=[new n.Cartesian3,new n.Cartesian3],_=[new n.Cartesian3,new n.Cartesian3];function G(i,t){++K;var s=Y[K],r=_[K];return s=e.AttributeCompression.octDecode(i.first.getNormalX(),i.first.getNormalY(),s),r=e.AttributeCompression.octDecode(i.second.getNormalX(),i.second.getNormalY(),r),N=n.Cartesian3.lerp(s,r,i.ratio,N),n.Cartesian3.normalize(N,N),e.AttributeCompression.octEncode(N,t),--K,t}k.prototype.getNormalX=function(){return s.defined(this.index)?this.normalBuffer[2*this.index]:(W=G(this,W)).x},k.prototype.getNormalY=function(){return s.defined(this.index)?this.normalBuffer[2*this.index+1]:(W=G(this,W)).y};var J=[];function Z(e,i,t,n,r,h,u,o,a,d,p){if(0!==u.length){for(var l=0,f=0;f<u.length;)f=J[l++].initializeFromClipResult(u,f,o);for(var g=0;g<l;++g){var c=J[g];if(c.isIndexed())c.newIndex=h[c.index],c.uBuffer=e,c.vBuffer=i,c.heightBuffer=t,a&&(c.normalBuffer=n);else{var m=c.getKey();if(s.defined(h[m]))c.newIndex=h[m];else{var x=e.length;e.push(c.getU()),i.push(c.getV()),t.push(c.getH(d,p)),a&&(n.push(c.getNormalX()),n.push(c.getNormalY())),c.newIndex=x,h[m]=x}}}3===l?(r.push(J[0].newIndex),r.push(J[1].newIndex),r.push(J[2].newIndex)):4===l&&(r.push(J[0].newIndex),r.push(J[1].newIndex),r.push(J[2].newIndex),r.push(J[0].newIndex),r.push(J[2].newIndex),r.push(J[3].newIndex))}}return J.push(new k),J.push(new k),J.push(new k),J.push(new k),d((function(e,u){var d=e.isEastChild,p=e.isNorthChild,l=d?M:0,f=d?I:M,g=p?M:0,c=p?I:M,m=V,x=R,v=O,C=P;m.length=0,x.length=0,v.length=0,C.length=0;var w=U;w.length=0;var B={},y=e.vertices,b=e.indices;b=b.subarray(0,e.indexCountWithoutSkirts);var W,K,Y,_,G,J=r.TerrainEncoding.clone(e.encoding),j=J.hasVertexNormals,q=e.exaggeration,Q=0,$=e.vertexCountWithoutSkirts,ee=e.minimumHeight,ie=e.maximumHeight,te=s.defined(e.validMinimumHeight)?e.validMinimumHeight:e.minimumHeight,ne=s.defined(e.validMaximumHeight)?e.validMaximumHeight:e.maximumHeight,se=new Array($),re=new Array($),he=new Array($),ue=j?new Array(2*$):void 0;for(K=0,Y=0;K<$;++K,Y+=2){var oe=J.decodeTextureCoordinates(y,K,L);if(W=J.decodeHeight(y,K)/q,_=o.CesiumMath.clamp(oe.x*I|0,0,I),G=o.CesiumMath.clamp(oe.y*I|0,0,I),he[K]=o.CesiumMath.clamp((W-ee)/(ie-ee)*I|0,0,I),_<20&&(_=0),G<20&&(G=0),I-_<20&&(_=I),I-G<20&&(G=I),se[K]=_,re[K]=G,j){var ae=J.getOctEncodedNormal(y,K,X);ue[Y]=ae.x,ue[Y+1]=ae.y}(d&&_>=M||!d&&_<=M)&&(p&&G>=M||!p&&G<=M)&&(B[K]=Q,m.push(_),x.push(G),v.push(he[K]),j&&(C.push(ue[Y]),C.push(ue[Y+1])),++Q)}var de=[];de.push(new k),de.push(new k),de.push(new k);var pe,le=[];for(le.push(new k),le.push(new k),le.push(new k),K=0;K<b.length;K+=3){var fe=b[K],ge=b[K+1],ce=b[K+2],me=se[fe],xe=se[ge],ve=se[ce];de[0].initializeIndexed(se,re,he,ue,fe),de[1].initializeIndexed(se,re,he,ue,ge),de[2].initializeIndexed(se,re,he,ue,ce);var Ce=A.clipTriangleAtAxisAlignedThreshold(M,d,me,xe,ve,H);(pe=0)>=Ce.length||((pe=le[0].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[1].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[2].initializeFromClipResult(Ce,pe,de),Z(m,x,v,C,w,B,A.clipTriangleAtAxisAlignedThreshold(M,p,le[0].getV(),le[1].getV(),le[2].getV(),T),le,j,ie,ee),pe<Ce.length&&(le[2].clone(le[1]),le[2].initializeFromClipResult(Ce,pe,de),Z(m,x,v,C,w,B,A.clipTriangleAtAxisAlignedThreshold(M,p,le[0].getV(),le[1].getV(),le[2].getV(),T),le,j,ie,ee))))}var we=d?-32767:0,Be=p?-32767:0,ye=[],be=[],Ae=[],Ie=[],Me=Number.MAX_VALUE,He=-Me,Te=Number.MAX_VALUE,ze=-Me,Ee=z;Ee.length=0;var Ne=t.Ellipsoid.clone(e.ellipsoid),Ve=t.Rectangle.clone(e.childRectangle),Re=Ve.north,Oe=Ve.south,Ue=Ve.east,Pe=Ve.west;for(Ue<Pe&&(Ue+=o.CesiumMath.TWO_PI),K=0;K<m.length;++K){(_=Math.round(m[K]))<=l?(ye.push(K),_=0):_>=f?(Ae.push(K),_=I):_=2*_+we,m[K]=_,(G=Math.round(x[K]))<=g?(be.push(K),G=0):G>=c?(Ie.push(K),G=I):G=2*G+Be,x[K]=G,(W=o.CesiumMath.lerp(ee,ie,v[K]/I))<Me&&(Me=W),W>He&&(He=W);var Fe=W;(Fe=o.CesiumMath.clamp(Fe,te,ne))<Te&&(Te=Fe),Fe>ze&&(ze=Fe),v[K]=W,E.longitude=o.CesiumMath.lerp(Pe,Ue,_/I),E.latitude=o.CesiumMath.lerp(Oe,Re,G/I),E.height=W,Ne.cartographicToCartesian(E,N),Ee.push(N.x),Ee.push(N.y),Ee.push(N.z)}var Se=i.BoundingSphere.fromVertices(Ee,n.Cartesian3.ZERO,3,S),De=a.OrientedBoundingBox.fromRectangle(Ve,Me,He,Ne,D),Le=a.OrientedBoundingBox.fromRectangle(Ve,Te,ze,Ne,D),Xe=new r.EllipsoidalOccluder(Ne).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(Se.center,Ee,3,Se.center,Me,F),ke=He-Me,We=new Uint16Array(m.length+x.length+v.length);for(K=0;K<m.length;++K)We[K]=m[K];var Ke=m.length;for(K=0;K<x.length;++K)We[Ke+K]=x[K];for(Ke+=x.length,K=0;K<v.length;++K)We[Ke+K]=I*(v[K]-Me)/ke;var Ye,_e=h.IndexDatatype.createTypedArray(m.length,w);if(j){var Ge=new Uint8Array(C);u.push(We.buffer,_e.buffer,Ge.buffer),Ye=Ge.buffer}else u.push(We.buffer,_e.buffer);return{vertices:We.buffer,encodedNormals:Ye,indices:_e.buffer,minimumHeight:Me,maximumHeight:He,westIndices:ye,southIndices:be,eastIndices:Ae,northIndices:Ie,boundingSphere:Se,orientedBoundingBox:De,horizonOcclusionPoint:Xe,validMinimumHeight:Te,validMaximumHeight:ze,validOrientedBoundingBox:Le}}))}));
