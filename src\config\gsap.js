const ANIMATIONS = {
  //模块载入动画
  ////////////////////////////////////
  //从左侧滑入
  LeftSlide: {
    from: {
      x: -200,
      opacity: 0,
    },
    to: {
      duration: 0.5,
      x: 0,
      opacity: 1,
    },
  },
  //从右侧滑入
  RightSlide: {
    from: {
      x: 200,
      opacity: 0,
    },
    to: {
      duration: 0.5,
      x: 0,
      opacity: 1,
    },
  },
  //从上滑入
  TopSlide: {
    from: {
      y: -200,
      opacity: 0,
    },
    to: {
      duration: 0.5,
      y: 0,
      opacity: 1,
    },
  },
  //从下滑入
  BottomSlide: {
    from: {
      y: 200,
      opacity: 0,
    },
    to: {
      duration: 0.5,
      y: 0,
      opacity: 1,
    },
  },
  //逐渐显示
  HideDisplay: {
    from: {
      opacity: 0,
    },
    to: {
      duration: 0.8,
      opacity: 1,
    },
  },
  //中心扩大
  CenterGrow: {
    from: {
      scale: 0,
    },
    to: {
      duration: 0.5,
      scale: 1,
    },
  },
  //文字显示动画
  ////////////////////////////////////
  //旋转显示
  RotateShow: {
    from: {
      rotateZ: 360,
      scale: 0,
    },
    to: {
      rotateZ: 0,
      duration: 0.5,
      stagger: 0.1,
      scale: 1,
    },
  },
  //缩小显示
  ShrinkShow: {
    from: {
      scale: 2,
      opacity: 0,
      x: -20,
    },
    to: {
      opacity: 1,
      scale: 1,
      x: 0,
      stagger: 0.1,
    },
  },
};

export default ANIMATIONS;
