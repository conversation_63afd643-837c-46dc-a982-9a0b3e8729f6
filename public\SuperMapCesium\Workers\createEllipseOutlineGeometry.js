define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseOutlineGeometry-2d607e3a","./Cartesian2-47311507","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-79deae95","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,l,o,d,c,s,u,b,f,p,y,m,G,C){"use strict";return function(i,n){return t.defined(n)&&(i=a.EllipseOutlineGeometry.unpack(i,n)),i._center=e.Cartesian3.clone(i._center),i._ellipsoid=r.Ellipsoid.clone(i._ellipsoid),a.EllipseOutlineGeometry.createGeometry(i)}}));
