import "./materials/TrailWall.js";
import "./materials/ImageTrailWall.js";
import "./materials/LineTrailWall.js";
import "./materials/DiffuseCircle.js";
import "./materials/ScanCircle.js";
import "./materials/WaveCircle.js";
export const ImageStyle = {
  default: {
    width: 24, //宽度
    height: 24, //高度
    pixelOffset: new Cesium.Cartesian2(0, 0), //偏移
  },
  label: {
    width: 37, //宽度
    height: 37, //高度
    pixelOffset: new Cesium.Cartesian2(0, 0), //偏移
  },
  gif: {
    width: 200, //宽度
    height: 200, //高度
    pixelOffset: new Cesium.Cartesian2(0, 0), //偏移
  },
};
export const TextStyle = {
  default: {
    font: "normal 12px 微软雅黑",
    showBackground: true,
    backgroundColor: Cesium.Color.fromCssColorString("#0000007a"),
    backgroundPadding: new Cesium.Cartesian2(7, 5), //左右7，上下5
    fillColor: Cesium.Color.SPRINGGREEN, //字体填充颜色
    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
    verticalOrigin: Cesium.VerticalOrigin.CENTER,
    pixelOffset: new Cesium.Cartesian2(0, -30), //偏移
  },
  label: {
    font: "normal 12px 微软雅黑",
    showBackground: true,
    backgroundColor: Cesium.Color.fromCssColorString("#0000007a"),
    backgroundPadding: new Cesium.Cartesian2(7, 5), //左右7，上下5
    fillColor: Cesium.Color.SALMON, //字体填充颜色
    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
    verticalOrigin: Cesium.VerticalOrigin.CENTER,
    pixelOffset: new Cesium.Cartesian2(0, -30), //偏移
  },
  polygon: {
    font: "normal 12px 微软雅黑",
    showBackground: true,
    backgroundColor: Cesium.Color.fromCssColorString("#0000007a"),
    backgroundPadding: new Cesium.Cartesian2(7, 5), //左右7，上下5
    fillColor: Cesium.Color.SALMON, //字体填充颜色
    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
    verticalOrigin: Cesium.VerticalOrigin.CENTER,
    pixelOffset: new Cesium.Cartesian2(0, -30), //偏移
  },
};
export const PolygonStyle = {
  default: {
    height: 199, //离地高度
    extrudedHeight: 200, //上层高度,与height差值是实体高度
    fill: true, //是否填充
    material: Cesium.Color.fromCssColorString("rgba(255, 255, 255, 0.2)"), //填充素材
    outline: true, //是否有轮廓
    outlineColor: Cesium.Color.fromCssColorString("#3399CC"), //轮廓颜色
    outlineWidth: 2, //轮廓宽度
    closeTop: true,
    closeBottom: true,
  },
  wall: {
    height: 200, //离地高度
    extrudedHeight: 301, //上层高度,与height差值是实体高度
    fill: true, //是否填充
    material: Cesium.Color.DEEPPINK, //填充素材
    outline: true, //是否有轮廓
    outlineColor: Cesium.Color.fromCssColorString("#3399CC"), //轮廓颜色
    outlineWidth: 2, //轮廓宽度
    closeTop: false,
    closeBottom: false,
  },
};
export const WallStyle = {
  default: {
    minimumHeights: 5, //离地高度
    maximumHeights: 150, //墙顶高度
    fill: true, //是否填充
    material: new Cesium.TrailWallMaterialProperty({ color: Cesium.Color.DARKORANGE, speed: 3 }), //填充素材
    outline: false, //是否有轮廓
    outlineColor: Cesium.Color.fromCssColorString("#3399CC"), //轮廓颜色
    outlineWidth: 2, //轮廓宽度
    shadows: Cesium.ShadowMode.DISABLED, //阴影设置
  },
  arrow: {
    minimumHeights: 5, //离地高度
    maximumHeights: 150, //墙顶高度
    fill: true, //是否填充
    material: new Cesium.ImageTrailWallMaterialProperty({ color: Cesium.Color.DARKORANGE, speed: 20 }), //填充素材
    outline: false, //是否有轮廓
    outlineColor: Cesium.Color.fromCssColorString("#3399CC"), //轮廓颜色
    outlineWidth: 2, //轮廓宽度
    shadows: Cesium.ShadowMode.DISABLED, //阴影设置
  },
  line: {
    minimumHeights: 5, //离地高度
    maximumHeights: 150, //墙顶高度
    fill: true, //是否填充
    material: new Cesium.LineTrailWallMaterialProperty({ color: Cesium.Color.DARKORANGE, speed: 4 }), //填充素材
    outline: false, //是否有轮廓
    outlineColor: Cesium.Color.fromCssColorString("#3399CC"), //轮廓颜色
    outlineWidth: 2, //轮廓宽度
    shadows: Cesium.ShadowMode.DISABLED, //阴影设置
  },
};
export const CircleStyle = {
  default: {
    fill: true, //是否填充
    material: new Cesium.DiffuseCircleMaterialProperty({ color: Cesium.Color.RED, speed: 3 }), //填充素材
  },
  scan: {
    fill: true, //是否填充
    material: new Cesium.ScanCircleMaterialProperty({ color: Cesium.Color.RED }), //填充素材
  },
  wave: {
    fill: true, //是否填充
    material: new Cesium.WaveCircleMaterialProperty({ color: Cesium.Color.RED }), //填充素材
  },
};
export const GeoJsonStyle = {
  default: {
    stroke: Cesium.Color.GHOSTWHITE, //轮廓颜色
    fill: Cesium.Color.CORNFLOWERBLUE, //填充颜色
    strokeWidth: 1, //线条宽度
  },
};

// 洪水淹没样式
export const FloodStyle = {
  default: {
    material: Cesium.Color.BLUE.withAlpha(0.6),
    height: 0,
    extrudedHeight: 10,
    outline: true,
    outlineColor: Cesium.Color.DARKBLUE,
  },
};

// 路径漫游样式
export const PathStyle = {
  default: {
    width: 5,
    material: Cesium.Color.YELLOW,
    clampToGround: true,
  },
  highlight: {
    width: 8,
    material: Cesium.Color.RED,
    clampToGround: true,
  },
};
