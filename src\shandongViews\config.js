import { getDynamic1, getDynamic2 } from "./api";

//key即是组件中使用store的key值，属性是对应的api
const ApiConfig = {
  ceshi1: getDynamic1,
  ceshi2: getDynamic2,
};

// key：组件路径，用于在Modules中使用组件
// name：组件名称，用于编辑面板显示
// aspectRatio：宽高比，用于组件调整尺寸时保持比例
// storeKey：用于匹配api获取数据，也是在store中读取数据时使用的key值
// 多个组件可使用相同storeKey，即使用同一个api获取数据
// type：默认（不填）、fullScreen（全屏组件）、navigation（导航组件）、map（地图组件）
// 全屏组件、导航组件、地图组件，会自动设置位置在左上角。
// 页面第一层只能放置全屏组件和地图组件
const CompsConfig = {
  "/src/shandongViews/BlockOne.vue": {
    name: "测试模块一",
    aspectRatio: 5 / 4,
    storeKey: ["ceshi1"],
  },
  "/src/shandongViews/BlockTwo.vue": {
    name: "测试模块二",
    aspectRatio: 4 / 5,
    storeKey: "ceshi2",
  },
  "/src/shandongViews/BlockThree.vue": {
    name: "测试模块三",
    aspectRatio: 1,
    storeKey: "ceshi1",
  },
  "/src/shandongViews/Background.vue": {
    name: "背景色",
    type: "fullScreen",
  },
  "/src/shandongViews/DynamicHeader.vue": {
    name: "导航头",
    type: "navigation",
  },
  "/src/components/cesium/index.vue": {
    name: "cesium",
    type: "map",
  },
  "/src/components/olMap/index.vue": {
    name: "openlayers",
    type: "map",
  },
};

// 用到的组件需从此处引入，默认引入shandongViews目录下文件，其他需单独配置
const Modules = import.meta.glob([
  "@/shandongViews/*.vue",
  "@/shandongViews/**/index.vue",
  "/src/components/cesium/index.vue",
  "/src/components/olMap/index.vue",
]);

export { CompsConfig, Modules, ApiConfig };
