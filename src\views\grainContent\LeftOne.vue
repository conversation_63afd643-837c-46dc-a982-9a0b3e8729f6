<template>
  <div class="box">
    <GrainPartTitle title="人居环境" :position="0.5" />
    <div class="line-left" v-if="pageData.orderRank">
      <div v-for="(item, index) in pageData.orderRank"
        :class="['type-button', index === typeIndex ? 'type-active' : 'type-passive']" @click="typeClick(index)">{{
          item.type }}</div>
    </div>
    <div class="line" v-if="pageData.orderRank" style="margin-top: -10px;">
      <div class="rank-item"
        v-for="(item, index) in pageData.orderRank[typeIndex] && pageData.orderRank[typeIndex].rankData">
        <div class="name">{{ item.name }}</div>
        <GrowNumber class="sales" :value="item.sales" :position="0.5" />
        <div class="number">TOP{{ index + 1 }}</div>
      </div>
    </div>
    <div class="line-left" style="margin-top: 4px;">
      <div :class="['table-button', tableType === 'order' ? 'table-active' : 'table-passive']"
        @click="tableClick('order')">订单</div>
      <div :class="['table-button', tableType === 'custom' ? 'table-active' : 'table-passive']"
        @click="tableClick('custom')">客户信用</div>
    </div>
    <div class="line">
      <div class="title" style="margin-right: 36px;margin-left: 9px;">订单编号</div>
      <div class="title">商品名称</div>
      <div class="title">采购数量</div>
      <div class="title">省份</div>
      <div class="title" style="margin-right: 9px;">下单时间</div>
    </div>
    <swiper-container class="swiper" direction="vertical" :autoplay="true" :autoplay-delay="3000" :speed="1000"
      loop="true" slidesPerView='auto'>
      <swiper-slide class="item" v-for="(item, index) in pageData.orderDatas" :style="slideStyle(index)">
        <div class="content">{{ item.number }}</div>
        <div class="content">{{ item.name }}</div>
        <div class="content">{{ item.count }}</div>
        <div class="content">{{ item.province }}</div>
        <div class="content">{{ item.date }}</div>
      </swiper-slide>
    </swiper-container>
  </div>
</template>
<script setup>
import { ref } from "vue";
import GrainPartTitle from "../../components/common/GrainPartTitle.vue"
import { useGrainStore } from "../../stores/modules/grain";
import GrowNumber from "../../components/common/GrowNumber.vue"

const { pageData } = useGrainStore()

const typeIndex = ref(0)
function typeClick(index) {
  typeIndex.value = index
}

const tableType = ref("order")
function tableClick(value) {
  tableType.value = value
}

function slideStyle(index) {
  if (index % 2 === 0) {
    return {
      backgroundColor: "rgba(74,132,240,0.22)"
    }
  } else {
    return {
      backgroundColor: "rgba(74,132,240,0.12)"
    }
  }
}

</script>
<style scoped lang="scss">
.box {
  height: 389px;
  width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.line-left {
  width: 406px;
  display: flex;
  align-items: center;
}

.type-button {
  width: 114px;
  height: 39px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  text-align: center;
  line-height: 39px;
  cursor: pointer;
}

.type-active {
  background-image: url('../../assets/images/button1_bg_active.png');
  background-size: 100% 100%;
  color: #FFFFFF;
}

.type-passive {
  background-image: url('../../assets/images/button1_bg.png');
  background-size: 100% 100%;
  color: #497EC0;
}

.line {
  width: 406px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rank-item {
  width: 121px;
  height: 110px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background-image: url('../../assets/images/item_bg.png');
  background-size: 100% 100%;

  .name {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #44C1EF;
    line-height: 32px;
  }

  .sales {
    font-size: 25px;
    line-height: 25px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;

    background: linear-gradient(179deg, rgba(40, 216, 255, 0.989) 0%, rgb(255, 255, 255) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .number {
    font-size: 14px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #EDFBFF;
    line-height: 32px;
  }
}

.table-button {
  width: 108px;
  height: 33px;
  font-size: 15px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  line-height: 30px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.table-active {
  background-image: url('../../assets/images/button2_bg_active.png');
  background-size: 100% 100%;
  color: #A6EFFF;
}

.table-passive {
  background-image: url('../../assets/images/button2_bg.png');
  background-size: 108px 30px;
  background-repeat: no-repeat;
  color: #249ED4;
}

.title {
  font-size: 15px;
  line-height: 29px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #E6F0FE;
}

.swiper {
  width: 406px;
  height: 140px;

  .item {
    width: 406px;
    height: 35px;
    padding: 0px 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .content {
      font-size: 14px;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      color: #BDCBDD;
      line-height: 35px;
      white-space: nowrap;
    }
  }
}
</style>