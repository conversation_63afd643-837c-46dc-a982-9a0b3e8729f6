<template>
  <div class="weather-box">
    <span class="temperature">{{ weatherData.temperature }}℃</span>
    <div class="icon-box">
      <img class="icon" :src="icon" alt="icon">
    </div>
    <span class="weather">{{ weatherData.weather }}</span>
  </div>
</template>
<script setup>
import { onMounted, computed, ref, onUnmounted } from "vue";
import axios from "axios";
import { useSystemStore } from "../../stores/system";
import { AMAP_CONFIG } from "../../config";
import cloudy from "../../assets/weather/cloudy.svg"
import fog from "../../assets/weather/fog.svg"
import rain from "../../assets/weather/rain.svg"
import sandstorm from "../../assets/weather/sandstorm.svg?url"
import snow from "../../assets/weather/snow.svg"
import sun from "../../assets/weather/sun.svg"
import sunny from "../../assets/weather/sunny.svg"
import thunderstorm from "../../assets/weather/thunderstorm.svg"
import wind from "../../assets/weather/wind.svg"

let intervalkey

const weatherData = ref({})

const icon = ref("")

const { locationData } = useSystemStore();

function getWeather() {
  axios
    .get(`https://restapi.amap.com/v3/weather/weatherInfo?&key=${AMAP_CONFIG.key}&city=${locationData.adcode}&extensions=base`)
    .then((res) => {
      if (res.data && res.data.status === "1" && res.data.lives) {
        console.log("weather success");
        console.log(res)
        weatherData.value = res.data.lives[0]
        icon.value = matchIcon(res.data.lives[0].weather)
      } else {
        console.log("weather fail");
        console.log(res);
      }
    })
    .catch((err) => {
      console.log("weather fail");
      console.log(err);
    });
}

function matchIcon(weather) {
  if (!weather) {
    return sun
  }
  if (/雪/i.test(weather)) {
    return snow
  }
  if (/雷/i.test(weather)) {
    return thunderstorm
  }
  if (/雨/i.test(weather)) {
    return rain
  }
  if (/[沙尘]/i.test(weather)) {
    return sandstorm
  }
  if (/[雾霾]/i.test(weather)) {
    return fog
  }
  if (/[阴]/i.test(weather)) {
    return cloudy
  }
  if (/[风]/i.test(weather)) {
    return wind
  }
  if (/[云]/i.test(weather)) {
    return sunny
  }
  return sun
}

function updateWeather() {
  intervalkey = setInterval(() => {
    getWeather()
  }, 1000 * 60 * 60 * 8)
}
onMounted(() => {
  getWeather()
  updateWeather()
})
onUnmounted(() => {
  clearInterval(intervalkey)
})
</script>
<style scoped>
.weather-box {
  display: flex;
  align-items: center;
  justify-content: end;
  min-width: 120px;
}

.temperature {
  font-size: 16px;
  margin: 0px 5px;
}

.icon-box {
  width: 30px;
  height: 30px;
  overflow: hidden;
  margin: 0px 5px;
}

.icon {
  width: 30px;
  height: 30px;
  translate: -30px 0px;
  filter: drop-shadow(30px 0px #000000);
}

.weather {
  font-size: 16px;
  margin: 0px 5px;
}
</style>
