define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseGeometry-462fe80a","./Cartesian2-47311507","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-79deae95","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-44c6c124","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./VertexFormat-6446fca0"],(function(e,t,a,r,n,i,o,d,c,l,s,b,f,m,p,u,y,G,C,E,h,A,_,D,F,I){"use strict";return function(n,i){return t.defined(i)&&(n=a.EllipseGeometry.unpack(n,i)),n._center=e.Cartesian3.clone(n._center),n._ellipsoid=r.Ellipsoid.clone(n._ellipsoid),a.EllipseGeometry.createGeometry(n)}}));
