define(["./createTaskProcessorWorker","./pako_inflate-f73548c4","./when-b60132fc"],(function(e,t,n){"use strict";if("undefined"!=typeof WebAssembly&&"object"!=typeof window){var r,i=void 0!==i?i:{},o={};for(r in i)i.hasOwnProperty(r)&&(o[r]=i[r]);i.arguments=[],i.thisProgram="./this.program",i.quit=function(e,t){throw t},i.preRun=[],i.postRun=[];var a=!1,s=!1,u=!1,l=!1;if(a="object"==typeof window,s="function"==typeof importScripts,u="object"==typeof process&&"function"==typeof require&&!a&&!s,l=!a&&!u&&!s,i.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var d,c,f="";if(u)f=__dirname+"/",i.read=function(e,t){var n;return d||(d=require("fs")),c||(c=require("path")),e=c.normalize(e),n=d.readFileSync(e),t?n:n.toString()},i.readBinary=function(e){var t=i.read(e,!0);return t.buffer||(t=new Uint8Array(t)),Se(t.buffer),t},process.argv.length>1&&(i.thisProgram=process.argv[1].replace(/\\/g,"/")),i.arguments=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=i),process.on("uncaughtException",(function(e){if(!(e instanceof ct))throw e})),process.on("unhandledRejection",Et),i.quit=function(e){process.exit(e)},i.inspect=function(){return"[Emscripten Module object]"};else if(l)"undefined"!=typeof read&&(i.read=function(e){return read(e)}),i.readBinary=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(Se("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs?i.arguments=scriptArgs:void 0!==arguments&&(i.arguments=arguments),"function"==typeof quit&&(i.quit=function(e){quit(e)});else{if(!a&&!s)throw new Error("environment detection error");s?f=self.location.href:document.currentScript&&(f=document.currentScript.src),f=0!==f.indexOf("blob:")?f.substr(0,f.indexOf("/Workers")+1):"",i.read=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},s&&(i.readBinary=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),i.readAsync=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)},i.setWindowTitle=function(e){document.title=e}}var E=i.print||("undefined"!=typeof console?console.log.bind(console):"undefined"!=typeof print?print:null),T=i.printErr||("undefined"!=typeof printErr?printErr:"undefined"!=typeof console&&console.warn.bind(console)||E);for(r in o)o.hasOwnProperty(r)&&(i[r]=o[r]);function Me(e){Me.shown||(Me.shown={}),Me.shown[e]||(Me.shown[e]=1)}o=void 0,Se(void 0===i.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),Se(void 0===i.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),Se(void 0===i.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),Se(void 0===i.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),_e=Te=Ee=function(){Et("cannot use the stack before compiled code is ready to run, and has provided stack access")};var _={"f64-rem":function(e,t){return e%t},debugger:function(){}};new Array(0);var p,h=0,m=function(e){h=e},y=function(){return h};function be(e,t,n){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":return g[e>>0];case"i16":return M[e>>1];case"i32":case"i64":return b[e>>2];case"float":return F[e>>2];case"double":return v[e>>3];default:Et("invalid type for getValue: "+t)}return null}"object"!=typeof WebAssembly&&Et("No WebAssembly support found. Build with -s WASM=0 to target JavaScript instead.");var R=!1;function Se(e,t){e||Et("Assertion failed: "+t)}function Fe(e){var t=i["_"+e];return Se(t,"Cannot call unknown function "+e+", make sure it is exported"),t}function ve(e,t,n,r,i){var o={string:function(e){var t=0;if(null!=e&&0!==e){var n=1+(e.length<<2);De(e,t=Ee(n),n)}return t},array:function(e){var t=Ee(e.length);return Xe(e,t),t}};var a=Fe(e),s=[],u=0;if(Se("array"!==t,'Return type should not be "array".'),r)for(var l=0;l<r.length;l++){var d=o[n[l]];d?(0===u&&(u=_e()),s[l]=d(r[l])):s[l]=r[l]}var c=a.apply(null,s);return c=function(e){return"string"===t?Ue(e):"boolean"===t?Boolean(e):e}(c),0!==u&&Te(u),c}function Ie(e,t,n,r){return function(){return ve(e,t,n,arguments)}}var w="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function Ne(e,t,n){for(var r=t+n,i=t;e[i]&&!(i>=r);)++i;if(i-t>16&&e.subarray&&w)return w.decode(e.subarray(t,i));for(var o="";t<i;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var u=63&e[t++];if(224==(240&a)?a=(15&a)<<12|s<<6|u:(240!=(248&a)&&Me("Invalid UTF-8 leading byte 0x"+a.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),a=(7&a)<<18|s<<12|u<<6|63&e[t++]),a<65536)o+=String.fromCharCode(a);else{var l=a-65536;o+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else o+=String.fromCharCode((31&a)<<6|s)}else o+=String.fromCharCode(a)}return o}function Ue(e,t){return e?Ne(O,e,t):""}function xe(e,t,n,r){if(!(r>0))return 0;for(var i=n,o=n+r-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a);if(s<=127){if(n>=o)break;t[n++]=s}else if(s<=2047){if(n+1>=o)break;t[n++]=192|s>>6,t[n++]=128|63&s}else if(s<=65535){if(n+2>=o)break;t[n++]=224|s>>12,t[n++]=128|s>>6&63,t[n++]=128|63&s}else{if(n+3>=o)break;s>=2097152&&Me("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),t[n++]=240|s>>18,t[n++]=128|s>>12&63,t[n++]=128|s>>6&63,t[n++]=128|63&s}}return t[n]=0,n-i}function De(e,t,n){return Se("number"==typeof n,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),xe(e,O,t,n)}function Xe(e,t){Se(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),g.set(e,t)}function Pe(){var e=new Error;if(!e.stack){try{throw new Error(0)}catch(t){e=t}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var A,g,O,M,b,S,F,v,I=65536;function Le(e,t){return e%t>0&&(e+=t-e%t),e}function ke(){i.HEAP8=g=new Int8Array(A),i.HEAP16=M=new Int16Array(A),i.HEAP32=b=new Int32Array(A),i.HEAPU8=O=new Uint8Array(A),i.HEAPU16=new Uint16Array(A),i.HEAPU32=S=new Uint32Array(A),i.HEAPF32=F=new Float32Array(A),i.HEAPF64=v=new Float64Array(A)}var N=15104,U=5257984,x=5257984,D=15072;Se(N%16==0,"stack must start aligned"),Se(x%16==0,"heap must start aligned");var X=5242880;i.TOTAL_STACK&&Se(X===i.TOTAL_STACK,"the stack size can no longer be determined at runtime");var P=i.TOTAL_MEMORY||16777216;function He(){34821223==S[(U>>2)-1]&&2310721022==S[(U>>2)-2]||Et("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x02135467, but received 0x"+S[(U>>2)-2].toString(16)+" "+S[(U>>2)-1].toString(16)),1668509029!==b[0]&&Et("Runtime error: The application has corrupted its heap memory area (address zero)!")}function Ce(e){Et("Stack overflow! Attempted to allocate "+e+" bytes on the stack, but stack has only "+(U-_e()+e)+" bytes available!")}if(P<X&&T("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+P+"! (TOTAL_STACK="+X+")"),Se("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),i.buffer?Se((A=i.buffer).byteLength===P,"provided buffer should be "+P+" bytes, but it is "+A.byteLength):("object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(Se(P%I==0),p=new WebAssembly.Memory({initial:P/I}),A=p.buffer):A=new ArrayBuffer(P),Se(A.byteLength===P)),ke(),b[D>>2]=x,b[0]=1668509029,M[1]=25459,115!==O[2]||99!==O[3])throw"Runtime error: expected the system to be little-endian!";function Qe(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?i.dynCall_v(n):i.dynCall_vi(n,t.arg):n(void 0===t.arg?null:t.arg)}else t()}}var L=[],k=[],H=[],C=[],Q=!1,B=!1;function Be(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)e=i.preRun.shift(),L.unshift(e);var e;Qe(L)}function Ye(){if(He(),i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)e=i.postRun.shift(),C.unshift(e);var e;Qe(C)}Se(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),Se(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),Se(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),Se(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Y=0,W=null,z=null,j={};function We(e){if(Y--,i.monitorRunDependencies&&i.monitorRunDependencies(Y),e?(Se(j[e]),delete j[e]):T("warning: run dependency removed without ID"),0==Y&&(null!==W&&(clearInterval(W),W=null),z)){var t=z;z=null,t()}}i.preloadedImages={},i.preloadedAudios={};var V={error:function(){Et("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){V.error()},createDataFile:function(){V.error()},createPreloadedFile:function(){V.error()},createLazyFile:function(){V.error()},open:function(){V.error()},mkdev:function(){V.error()},registerDevice:function(){V.error()},analyzePath:function(){V.error()},loadFilesFromDB:function(){V.error()},ErrnoError:function(){V.error()}};i.FS_createDataFile=V.createDataFile,i.FS_createPreloadedFile=V.createPreloadedFile;var G="data:application/octet-stream;base64,";function ze(e){return String.prototype.startsWith?e.startsWith(G):0===e.indexOf(G)}var q="ThirdParty/unzip.wasm";function je(){try{if(i.wasmBinary)return new Uint8Array(i.wasmBinary);if(i.readBinary)return i.readBinary(q);throw"both async and sync fetching of the wasm failed"}catch(e){Et(e)}}function Ve(e){var t,n={env:e,global:{NaN:NaN,Infinity:1/0},"global.Math":Math,asm2wasm:_};function r(e,t){var n=e.exports;i.asm=n,We("wasm-instantiate")}t="wasm-instantiate",Y++,i.monitorRunDependencies&&i.monitorRunDependencies(Y),t&&(Se(!j[t]),j[t]=1,null===W&&"undefined"!=typeof setInterval&&(W=setInterval((function(){if(R)return clearInterval(W),void(W=null)}),1e4)));var o=i;function u(e){Se(i===o,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),o=null,r(e.instance)}function l(e){return(i.wasmBinary||!a&&!s||"function"!=typeof fetch?new Promise((function(e,t){e(je())})):fetch(q,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+q+"'";return e.arrayBuffer()})).catch((function(){return je()}))).then((function(e){return WebAssembly.instantiate(e,n)})).then(e,(function(e){}))}if(i.instantiateWasm)try{return i.instantiateWasm(n,r)}catch(e){return!1}return function(){if(i.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||ze(q)||"function"!=typeof fetch)return l(u);fetch(q,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,n).then(u,(function(e){l(u)}))}))}(),{}}ze(q)||(he=q,q=i.locateFile?i.locateFile(he,f):f+he),i.asm=function(e,t,n){t.memory=p,t.table=new WebAssembly.Table({initial:22,maximum:22,element:"anyfunc"}),t.__memory_base=1024,t.__table_base=0;var r=Ve(t);return Se(r,"binaryen setup failed (no wasm support?)"),r};var J=15088;function Ge(){}Se(J%8==0);var K={buffers:[null,[],[]],printChar:function(e,t){var n=K.buffers[e];Se(n),0===t||10===t?((1===e?E:T)(Ne(n,0)),n.length=0):n.push(t)},varargs:0,get:function(e){return K.varargs+=4,b[K.varargs-4>>2]},getStr:function(){return Ue(K.get())},get64:function(){var e=K.get(),t=K.get();return Se(e>=0?0===t:-1===t),e},getZero:function(){Se(0===K.get())}};function qe(e,t){K.varargs=t;try{K.getStreamFromFD(),K.get(),K.get(),K.get(),K.get();return Et("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return void 0!==V&&e instanceof V.ErrnoError||Et(e),-e.errno}}function Je(){var e=i._fflush;e&&e(0);var t=K.buffers;t[1].length&&K.printChar(1,10),t[2].length&&K.printChar(2,10)}function Ke(e,t){K.varargs=t;try{for(var n=K.get(),r=K.get(),i=K.get(),o=0,a=0;a<i;a++){for(var s=b[r+8*a>>2],u=b[r+(8*a+4)>>2],l=0;l<u;l++)K.printChar(n,O[s+l]);o+=u}return o}catch(e){return void 0!==V&&e instanceof V.ErrnoError||Et(e),-e.errno}}function Ze(e,t){K.varargs=t;try{return 0}catch(e){return void 0!==V&&e instanceof V.ErrnoError||Et(e),-e.errno}}function $e(e,t){K.varargs=t;try{K.getStreamFromFD();return Et("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(e){return void 0!==V&&e instanceof V.ErrnoError||Et(e),-e.errno}}function et(){}function tt(){return g.length}function nt(e,t,n){O.set(O.subarray(t,t+n),e)}function rt(e){return i.___errno_location?b[i.___errno_location()>>2]=e:T("failed to set errno from JS"),e}function it(e){Et("Cannot enlarge memory arrays to size "+e+" bytes (OOM). Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+g.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function ot(e){e=Le(e,65536);var t=A.byteLength;try{return-1!==p.grow((e-t)/65536)&&(A=p.buffer,!0)}catch(n){return console.error("emscripten_realloc_buffer: Attempted to grow from "+t+" bytes to "+e+" bytes, but got error: "+n),!1}}function at(e){var t=tt();Se(e>t);var n=65536,r=2147418112;if(e>r)return T("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+r+" bytes!"),!1;for(var i=Math.max(t,16777216);i<e;)(i=i<=536870912?Le(2*i,n):Math.min(Le((3*i+2147483648)/4,n),r))===t&&Me("Cannot ask for more memory since we reached the practical limit in browsers (which is just below 2GB), so the request would have failed. Requesting only "+g.length);return ot(i)?(ke(),!0):(T("Failed to grow the heap from "+t+" bytes to "+i+" bytes, not enough memory!"),!1)}function st(e){T("Invalid function pointer called with signature 'ii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),Et(e)}function ut(e){T("Invalid function pointer called with signature 'iiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),Et(e)}function lt(e){T("Invalid function pointer called with signature 'jiji'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),Et(e)}function dt(e){T("Invalid function pointer called with signature 'vii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),T("Build with ASSERTIONS=2 for more info."),Et(e)}var Z={},$={abort:Et,setTempRet0:m,getTempRet0:y,abortStackOverflow:Ce,nullFunc_ii:st,nullFunc_iiii:ut,nullFunc_jiji:lt,nullFunc_vii:dt,___lock:Ge,___setErrNo:rt,___syscall140:qe,___syscall146:Ke,___syscall54:Ze,___syscall6:$e,___unlock:et,_emscripten_get_heap_size:tt,_emscripten_memcpy_big:nt,_emscripten_resize_heap:at,abortOnCannotGrowMemory:it,emscripten_realloc_buffer:ot,flush_NO_FILESYSTEM:Je,tempDoublePtr:J,DYNAMICTOP_PTR:D},ee=i.asm(Z,$,A),te=ee.___errno_location;ee.___errno_location=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),te.apply(null,arguments)};var ne=ee._fflush;ee._fflush=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ne.apply(null,arguments)};var re=ee._free;ee._free=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),re.apply(null,arguments)};var ie=ee._freePointer;ee._freePointer=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ie.apply(null,arguments)};var oe=ee._llvm_bswap_i32;ee._llvm_bswap_i32=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),oe.apply(null,arguments)};var ae=ee._malloc;ee._malloc=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ae.apply(null,arguments)};var se=ee._sbrk;ee._sbrk=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),se.apply(null,arguments)};var ue=ee._unzip;ee._unzip=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ue.apply(null,arguments)};var le=ee.establishStackSpace;ee.establishStackSpace=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),le.apply(null,arguments)};var de=ee.stackAlloc;ee.stackAlloc=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),de.apply(null,arguments)};var ce=ee.stackRestore;ee.stackRestore=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),ce.apply(null,arguments)};var fe=ee.stackSave;ee.stackSave=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),fe.apply(null,arguments)},i.asm=ee,i.___errno_location=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.___errno_location.apply(null,arguments)},i._emscripten_replace_memory=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._emscripten_replace_memory.apply(null,arguments)},i._fflush=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._fflush.apply(null,arguments)},i._free=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._free.apply(null,arguments)},i._freePointer=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._freePointer.apply(null,arguments)},i._llvm_bswap_i32=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._llvm_bswap_i32.apply(null,arguments)},i._malloc=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._malloc.apply(null,arguments)},i._memcpy=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._memcpy.apply(null,arguments)},i._memset=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._memset.apply(null,arguments)},i._sbrk=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._sbrk.apply(null,arguments)},i._unzip=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm._unzip.apply(null,arguments)},i.establishStackSpace=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.establishStackSpace.apply(null,arguments)};var Ee=i.stackAlloc=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackAlloc.apply(null,arguments)},Te=i.stackRestore=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackRestore.apply(null,arguments)},_e=i.stackSave=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.stackSave.apply(null,arguments)};function ct(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function ft(e){function t(){i.calledRun||(i.calledRun=!0,R||(He(),Q||(Q=!0,Qe(k)),He(),Qe(H),i.onRuntimeInitialized&&i.onRuntimeInitialized(),Se(!i._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),Ye()))}e=e||i.arguments,Y>0||(Se(0==(3&U)),S[(U>>2)-1]=34821223,S[(U>>2)-2]=2310721022,Be(),Y>0||i.calledRun||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),t()}),1)):t(),He()))}i.dynCall_ii=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_ii.apply(null,arguments)},i.dynCall_iiii=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_iiii.apply(null,arguments)},i.dynCall_jiji=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_jiji.apply(null,arguments)},i.dynCall_vii=function(){return Se(Q,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),Se(!B,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),i.asm.dynCall_vii.apply(null,arguments)},i.asm=ee,i.intArrayFromString||(i.intArrayFromString=function(){Et("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.intArrayToString||(i.intArrayToString=function(){Et("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.ccall=ve,i.cwrap=Ie,i.setValue||(i.setValue=function(){Et("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getValue=be,i.allocate||(i.allocate=function(){Et("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getMemory||(i.getMemory=function(){Et("'getMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.AsciiToString||(i.AsciiToString=function(){Et("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToAscii||(i.stringToAscii=function(){Et("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF8ArrayToString||(i.UTF8ArrayToString=function(){Et("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF8ToString||(i.UTF8ToString=function(){Et("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF8Array||(i.stringToUTF8Array=function(){Et("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF8||(i.stringToUTF8=function(){Et("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF8||(i.lengthBytesUTF8=function(){Et("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF16ToString||(i.UTF16ToString=function(){Et("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF16||(i.stringToUTF16=function(){Et("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF16||(i.lengthBytesUTF16=function(){Et("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.UTF32ToString||(i.UTF32ToString=function(){Et("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stringToUTF32||(i.stringToUTF32=function(){Et("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.lengthBytesUTF32||(i.lengthBytesUTF32=function(){Et("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.allocateUTF8||(i.allocateUTF8=function(){Et("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackTrace||(i.stackTrace=function(){Et("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPreRun||(i.addOnPreRun=function(){Et("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnInit||(i.addOnInit=function(){Et("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPreMain||(i.addOnPreMain=function(){Et("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnExit||(i.addOnExit=function(){Et("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addOnPostRun||(i.addOnPostRun=function(){Et("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeStringToMemory||(i.writeStringToMemory=function(){Et("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeArrayToMemory||(i.writeArrayToMemory=function(){Et("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.writeAsciiToMemory||(i.writeAsciiToMemory=function(){Et("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addRunDependency||(i.addRunDependency=function(){Et("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.removeRunDependency||(i.removeRunDependency=function(){Et("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.ENV||(i.ENV=function(){Et("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.FS||(i.FS=function(){Et("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.FS_createFolder||(i.FS_createFolder=function(){Et("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createPath||(i.FS_createPath=function(){Et("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createDataFile||(i.FS_createDataFile=function(){Et("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createPreloadedFile||(i.FS_createPreloadedFile=function(){Et("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createLazyFile||(i.FS_createLazyFile=function(){Et("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createLink||(i.FS_createLink=function(){Et("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_createDevice||(i.FS_createDevice=function(){Et("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.FS_unlink||(i.FS_unlink=function(){Et("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),i.GL||(i.GL=function(){Et("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.dynamicAlloc||(i.dynamicAlloc=function(){Et("'dynamicAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.warnOnce||(i.warnOnce=function(){Et("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.loadDynamicLibrary||(i.loadDynamicLibrary=function(){Et("'loadDynamicLibrary' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.loadWebAssemblyModule||(i.loadWebAssemblyModule=function(){Et("'loadWebAssemblyModule' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getLEB||(i.getLEB=function(){Et("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getFunctionTables||(i.getFunctionTables=function(){Et("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.alignFunctionTables||(i.alignFunctionTables=function(){Et("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.registerFunctions||(i.registerFunctions=function(){Et("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.addFunction||(i.addFunction=function(){Et("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.removeFunction||(i.removeFunction=function(){Et("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getFuncWrapper||(i.getFuncWrapper=function(){Et("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.prettyPrint||(i.prettyPrint=function(){Et("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.makeBigInt||(i.makeBigInt=function(){Et("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.dynCall||(i.dynCall=function(){Et("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getCompilerSetting||(i.getCompilerSetting=function(){Et("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackSave||(i.stackSave=function(){Et("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackRestore||(i.stackRestore=function(){Et("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.stackAlloc||(i.stackAlloc=function(){Et("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.establishStackSpace||(i.establishStackSpace=function(){Et("'establishStackSpace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.print||(i.print=function(){Et("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.printErr||(i.printErr=function(){Et("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.getTempRet0||(i.getTempRet0=function(){Et("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.setTempRet0||(i.setTempRet0=function(){Et("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.Pointer_stringify||(i.Pointer_stringify=function(){Et("'Pointer_stringify' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),i.ALLOC_NORMAL||Object.defineProperty(i,"ALLOC_NORMAL",{get:function(){Et("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_STACK||Object.defineProperty(i,"ALLOC_STACK",{get:function(){Et("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_DYNAMIC||Object.defineProperty(i,"ALLOC_DYNAMIC",{get:function(){Et("'ALLOC_DYNAMIC' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),i.ALLOC_NONE||Object.defineProperty(i,"ALLOC_NONE",{get:function(){Et("'ALLOC_NONE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),ct.prototype=new Error,ct.prototype.constructor=ct,z=function e(){i.calledRun||ft(),i.calledRun||(z=e)},i.run=ft;var pe=[];function Et(e){i.onAbort&&i.onAbort(e),R=!0;var t,n="abort("+(e=void 0!==e?'"'+e+'"':"")+") at "+(t=Pe(),i.extraStackTrace&&(t+="\n"+i.extraStackTrace()),t.replace(/__Z[\w\d_]+/g,(function(e){return e==e?e:e+" ["+e+"]"})));throw pe&&pe.forEach((function(t){n=t(n,e)})),n}if(i.abort=Et,i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();i.noExitRuntime=!0,ft()}else i=null;var he,me=i,ye=!1;if("undefined"!=typeof WebAssembly){me.onRuntimeInitialized=function(){ye=!0};var Re=me.cwrap("unzip","number",["number","number","number","number"]),we=me.cwrap("freePointer",null,["number"])}function Ae(e){var t=4*e.length,n=me._malloc(Uint8Array.BYTES_PER_ELEMENT*t),r=new Uint8Array(t);me.HEAPU8.set(r,n/Uint8Array.BYTES_PER_ELEMENT);var i,o=me._malloc(Uint8Array.BYTES_PER_ELEMENT*e.length);for(me.HEAPU8.set(e,o/Uint8Array.BYTES_PER_ELEMENT);0==(i=Re(n,t,o,e.length));)we(n),t*=4,n=me._malloc(Uint8Array.BYTES_PER_ELEMENT*t),r=new Uint8Array(t),me.HEAPU8.set(r,n/Uint8Array.BYTES_PER_ELEMENT);var a=new Uint8Array(me.HEAPU8.buffer,n,i);e=null,r=null;var s=new Uint8Array(a);return we(n),we(o),s}function ge(e,n){var r,i=e.data,o=new Uint8Array(i);return!0===ye?{data:r=Ae(o)}:(r=t.pako.inflate(o).buffer,n.push(r),{data:new Uint8Array(r)})}var Oe=e(ge);return Oe}));
