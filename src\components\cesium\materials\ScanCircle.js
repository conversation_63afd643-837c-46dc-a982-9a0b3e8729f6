import scanPng from "../../../assets/images/circle_scan.png";
const defaultColor = Cesium.Color.RED;

function ScanCircleMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;

  this.color = options.color;
}

Object.defineProperties(ScanCircleMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
});

ScanCircleMaterialProperty.prototype.getType = function (time) {
  return "ScanCircle";
};

ScanCircleMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  return result;
};

ScanCircleMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof ScanCircleMaterialProperty && //
      Cesium.Property.equals(this._color, other._color))
  );
};

Cesium.ScanCircleMaterialProperty = ScanCircleMaterialProperty;

const type = "ScanCircle";

const source = `
uniform vec4 color;
uniform sampler2D image;
czm_material czm_getMaterial(czm_materialInput materialInput){
  czm_material material = czm_getDefaultMaterial(materialInput);
  vec2 st = materialInput.st;
  vec2 center = st - vec2(0.5,0.5);
  float time = -czm_frameNumber * 3.1415926 / 180.;
  float sin_t = sin(time);
  float cos_t = cos(time);
  vec2 center_rotate = vec2(center.s * cos_t - center.t * sin_t + 0.5,center.s * sin_t + center.t * cos_t + 0.5);
  vec4 colorImage = texture2D(image,center_rotate);
  vec3 temp = colorImage.rgb * color.rgb;
  temp *= color.a;
  material.diffuse = temp;
  float length = 2. - length(center) / 0.5;
  material.alpha = colorImage.a * pow(length, 0.5);
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      image: scanPng,
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
