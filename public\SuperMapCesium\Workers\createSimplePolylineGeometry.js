define(["./when-b60132fc","./Cartesian2-47311507","./ArcType-29cf2197","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Color-2a095a27","./ComponentDatatype-c140a87d","./Check-7b2a090c","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolylinePipeline-3454449c","./FeatureDetection-806b12f0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./EllipsoidGeodesic-0f19ac62","./EllipsoidRhumbLine-ed1a6bf4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2"],(function(e,o,r,t,a,l,i,n,s,p,d,c,y,u,f,h,C,g,T,m,v,b){"use strict";function P(e,o,r,t,a,i,n){var s,p=y.PolylinePipeline.numberOfPoints(e,o,a),d=r.red,c=r.green,u=r.blue,f=r.alpha,h=t.red,C=t.green,g=t.blue,T=t.alpha;if(l.Color.equals(r,t)){for(s=0;s<p;s++)i[n++]=l.Color.floatToByte(d),i[n++]=l.Color.floatToByte(c),i[n++]=l.Color.floatToByte(u),i[n++]=l.Color.floatToByte(f);return n}var m=(h-d)/p,v=(C-c)/p,b=(g-u)/p,P=(T-f)/p,_=n;for(s=0;s<p;s++)i[_++]=l.Color.floatToByte(d+s*m),i[_++]=l.Color.floatToByte(c+s*v),i[_++]=l.Color.floatToByte(u+s*b),i[_++]=l.Color.floatToByte(f+s*P);return _}function _(t){var i=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions,n=t.colors,s=e.defaultValue(t.colorsPerVertex,!1);this._positions=i,this._colors=n,this._colorsPerVertex=s,this._arcType=e.defaultValue(t.arcType,r.ArcType.GEODESIC),this._granularity=e.defaultValue(t.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(t.ellipsoid,o.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";var p=1+i.length*a.Cartesian3.packedLength;p+=e.defined(n)?1+n.length*l.Color.packedLength:1,this.packedLength=p+o.Ellipsoid.packedLength+3}_.pack=function(r,t,i){var n;i=e.defaultValue(i,0);var s=r._positions,p=s.length;for(t[i++]=p,n=0;n<p;++n,i+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[n],t,i);var d=r._colors;for(p=e.defined(d)?d.length:0,t[i++]=p,n=0;n<p;++n,i+=l.Color.packedLength)l.Color.pack(d[n],t,i);return o.Ellipsoid.pack(r._ellipsoid,t,i),i+=o.Ellipsoid.packedLength,t[i++]=r._colorsPerVertex?1:0,t[i++]=r._arcType,t[i]=r._granularity,t},_.unpack=function(r,t,i){var n;t=e.defaultValue(t,0);var s=r[t++],p=new Array(s);for(n=0;n<s;++n,t+=a.Cartesian3.packedLength)p[n]=a.Cartesian3.unpack(r,t);var d=(s=r[t++])>0?new Array(s):void 0;for(n=0;n<s;++n,t+=l.Color.packedLength)d[n]=l.Color.unpack(r,t);var c=o.Ellipsoid.unpack(r,t);t+=o.Ellipsoid.packedLength;var y=1===r[t++],u=r[t++],f=r[t];return e.defined(i)?(i._positions=p,i._colors=d,i._ellipsoid=c,i._colorsPerVertex=y,i._arcType=u,i._granularity=f,i):new _({positions:p,colors:d,ellipsoid:c,colorsPerVertex:y,arcType:u,granularity:f})};var B=new Array(2),E=new Array(2),A={positions:B,height:E,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return _.createGeometry=function(o){var n,f,h,C,g,T=o._positions,m=o._colors,v=o._colorsPerVertex,b=o._arcType,_=o._granularity,k=o._ellipsoid,D=c.CesiumMath.chordLength(_,k.maximumRadius),G=e.defined(m)&&!v,w=T.length,L=0;if(b===r.ArcType.GEODESIC||b===r.ArcType.RHUMB){var V,x,S;b===r.ArcType.GEODESIC?(V=c.CesiumMath.chordLength(_,k.maximumRadius),x=y.PolylinePipeline.numberOfPoints,S=y.PolylinePipeline.generateArc):(V=_,x=y.PolylinePipeline.numberOfPointsRhumbLine,S=y.PolylinePipeline.generateRhumbArc);var I=y.PolylinePipeline.extractHeights(T,k),R=A;if(b===r.ArcType.GEODESIC?R.minDistance=D:R.granularity=_,R.ellipsoid=k,G){var O=0;for(n=0;n<w-1;n++)O+=x(T[n],T[n+1],V)+1;f=new Float64Array(3*O),C=new Uint8Array(4*O),R.positions=B,R.height=E;var M=0;for(n=0;n<w-1;++n){B[0]=T[n],B[1]=T[n+1],E[0]=I[n],E[1]=I[n+1];var U=S(R);if(e.defined(m)){var N=U.length/3;g=m[n];for(var F=0;F<N;++F)C[M++]=l.Color.floatToByte(g.red),C[M++]=l.Color.floatToByte(g.green),C[M++]=l.Color.floatToByte(g.blue),C[M++]=l.Color.floatToByte(g.alpha)}f.set(U,L),L+=U.length}}else if(R.positions=T,R.height=I,f=new Float64Array(S(R)),e.defined(m)){for(C=new Uint8Array(f.length/3*4),n=0;n<w-1;++n){L=P(T[n],T[n+1],m[n],m[n+1],D,C,L)}var H=m[w-1];C[L++]=l.Color.floatToByte(H.red),C[L++]=l.Color.floatToByte(H.green),C[L++]=l.Color.floatToByte(H.blue),C[L++]=l.Color.floatToByte(H.alpha)}}else{h=G?2*w-2:w,f=new Float64Array(3*h),C=e.defined(m)?new Uint8Array(4*h):void 0;var W=0,Y=0;for(n=0;n<w;++n){var q=T[n];if(G&&n>0&&(a.Cartesian3.pack(q,f,W),W+=3,g=m[n-1],C[Y++]=l.Color.floatToByte(g.red),C[Y++]=l.Color.floatToByte(g.green),C[Y++]=l.Color.floatToByte(g.blue),C[Y++]=l.Color.floatToByte(g.alpha)),G&&n===w-1)break;a.Cartesian3.pack(q,f,W),W+=3,e.defined(m)&&(g=m[n],C[Y++]=l.Color.floatToByte(g.red),C[Y++]=l.Color.floatToByte(g.green),C[Y++]=l.Color.floatToByte(g.blue),C[Y++]=l.Color.floatToByte(g.alpha))}}var z=new p.GeometryAttributes;z.position=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f}),e.defined(m)&&(z.color=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:C,normalize:!0}));var J=2*((h=f.length/3)-1),j=d.IndexDatatype.createTypedArray(h,J),K=0;for(n=0;n<h-1;++n)j[K++]=n,j[K++]=n+1;return new s.Geometry({attributes:z,indices:j,primitiveType:u.PrimitiveType.LINES,boundingSphere:t.BoundingSphere.fromPoints(T)})},function(r,t){return e.defined(t)&&(r=_.unpack(r,t)),r._ellipsoid=o.Ellipsoid.clone(r._ellipsoid),_.createGeometry(r)}}));
