<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon.ico">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unifast大屏</title>
  <link rel="stylesheet" href="/Cesium/Widgets/widgets.css" />
  <script defer src="/Cesium/Cesium.js"></script>
  <script defer src="/utils/gifler.js"></script>
  <script>
    window.endLoading = () => {
      const loading = document.getElementById("loading");
      if (loading) {
        loading.style.display = "none";
      }
    };
  </script>
  <style>
    *,
    *::before,
    *::after {
      box-sizing: border-box;
      margin: 0;
      position: relative;
    }

    #loading {
      display: flex;
      flex-direction: column;
      position: absolute;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      background-color: #eff5f5;
      width: 100vw;
      height: 100vh;
    }

    #loading .text {
      font-size: 20px;
      color: #373737;
      position: absolute;
      bottom: calc(50% - 150px);
    }

    #loading .boxes {
      --size: 32px;
      --duration: 800ms;
      height: calc(var(--size) * 2);
      width: calc(var(--size) * 3);
      position: relative;
      transform-style: preserve-3d;
      transform-origin: 50% 50%;
      margin-top: calc(var(--size) * 1.5 * -1);
      transform: rotateX(60deg) rotateZ(45deg) rotateY(0deg) translateZ(0px);
    }

    #loading .boxes .box {
      width: var(--size);
      height: var(--size);
      top: 0;
      left: 0;
      position: absolute;
      transform-style: preserve-3d;
    }

    #loading .boxes .box:nth-child(1) {
      transform: translate(100%, 0);
      -webkit-animation: box1 var(--duration) linear infinite;
      animation: box1 var(--duration) linear infinite;
    }

    #loading .boxes .box:nth-child(2) {
      transform: translate(0, 100%);
      -webkit-animation: box2 var(--duration) linear infinite;
      animation: box2 var(--duration) linear infinite;
    }

    #loading .boxes .box:nth-child(3) {
      transform: translate(100%, 100%);
      -webkit-animation: box3 var(--duration) linear infinite;
      animation: box3 var(--duration) linear infinite;
    }

    #loading .boxes .box:nth-child(4) {
      transform: translate(200%, 0);
      -webkit-animation: box4 var(--duration) linear infinite;
      animation: box4 var(--duration) linear infinite;
    }

    #loading .boxes .box>div {
      --background: #5c8df6;
      --top: auto;
      --right: auto;
      --bottom: auto;
      --left: auto;
      --translateZ: calc(var(--size) / 2);
      --rotateY: 0deg;
      --rotateX: 0deg;
      position: absolute;
      width: 100%;
      height: 100%;
      background: var(--background);
      top: var(--top);
      right: var(--right);
      bottom: var(--bottom);
      left: var(--left);
      transform: rotateY(var(--rotateY)) rotateX(var(--rotateX)) translateZ(var(--translateZ));
    }

    #loading .boxes .box>div:nth-child(1) {
      --top: 0;
      --left: 0;
    }

    #loading .boxes .box>div:nth-child(2) {
      --background: #145af2;
      --right: 0;
      --rotateY: 90deg;
    }

    #loading .boxes .box>div:nth-child(3) {
      --background: #447cf5;
      --rotateX: -90deg;
    }

    #loading .boxes .box>div:nth-child(4) {
      --background: #dbe3f4;
      --top: 0;
      --left: 0;
      --translateZ: calc(var(--size) * 3 * -1);
    }

    @-webkit-keyframes box1 {

      0%,
      50% {
        transform: translate(100%, 0);
      }

      100% {
        transform: translate(200%, 0);
      }
    }

    @keyframes box1 {

      0%,
      50% {
        transform: translate(100%, 0);
      }

      100% {
        transform: translate(200%, 0);
      }
    }

    @-webkit-keyframes box2 {
      0% {
        transform: translate(0, 100%);
      }

      50% {
        transform: translate(0, 0);
      }

      100% {
        transform: translate(100%, 0);
      }
    }

    @keyframes box2 {
      0% {
        transform: translate(0, 100%);
      }

      50% {
        transform: translate(0, 0);
      }

      100% {
        transform: translate(100%, 0);
      }
    }

    @-webkit-keyframes box3 {

      0%,
      50% {
        transform: translate(100%, 100%);
      }

      100% {
        transform: translate(0, 100%);
      }
    }

    @keyframes box3 {

      0%,
      50% {
        transform: translate(100%, 100%);
      }

      100% {
        transform: translate(0, 100%);
      }
    }

    @-webkit-keyframes box4 {
      0% {
        transform: translate(200%, 0);
      }

      50% {
        transform: translate(200%, 100%);
      }

      100% {
        transform: translate(100%, 100%);
      }
    }

    @keyframes box4 {
      0% {
        transform: translate(200%, 0);
      }

      50% {
        transform: translate(200%, 100%);
      }

      100% {
        transform: translate(100%, 100%);
      }
    }
  </style>
</head>

<body>
  <div id="loading">
    <div class="boxes">
      <div class="box">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="box">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="box">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="box">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <div class="text">加载中，请稍后</div>
  </div>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>