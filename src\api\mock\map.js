const mapPointPng = "./mock/map_point.png";
const mapIconPng = "./mock/map_icon.png";
const mapGif = "./mock/map_gif.gif";
//地图标签模板数据
const markTemplate = [
  {
    markType: "type1", //点位数据分类，区分不同类别的点位，唯一
    name: "类别一", //数据名称，可选项
    visible: true, //此类点位的显隐控制
    styleType: "", //此类点位的样式类别，用于给此类点位设置统一样式
    height: 300, //离地高度
    datas: [
      {
        id: "x3", //点位id，唯一
        coordinate: [116.8236, 36.8821], //点位坐标
        text: "标签", //点位显示文本
        image: mapGif, //点位显示图片
        status: "normal", //状态不同时，会进行删除重建，以实现不同的显示效果
        styleType: "gif", //此点位的样式类别，用于设置独立样式
      },
      // {
      //   id: "x4",
      //   coordinate: [117.2303, 36.8943],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x5",
      //   coordinate: [117.3165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x6",
      //   coordinate: [117.4165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x7",
      //   coordinate: [117.5165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x8",
      //   coordinate: [117.6165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x9",
      //   coordinate: [117.7165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x10",
      //   coordinate: [117.8165, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x11",
      //   coordinate: [117.9365, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x12",
      //   coordinate: [117.9665, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x13",
      //   coordinate: [117.9765, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
      // {
      //   id: "x14",
      //   coordinate: [117.9865, 36.4293],
      //   text: "标签",
      //   image: mapGif,
      //   status: "normal",
      //   styleType: "gif",
      // },
    ],
  },
  {
    markType: "type2",
    name: "类别二",
    visible: true,
    styleType: "",
    datas: [
      {
        id: "s1",
        coordinate: [116.9886, 36.6664],
        text: "监控",
        image: mapIconPng,
        status: "normal",
        styleType: "label",
      },
    ],
  },
  {
    markType: "type3",
    name: "类别三",
    visible: true,
    styleType: "",
    datas: [
      {
        id: "e1",
        coordinate: [116.8974, 36.7038],
        text: "点位",
        radius: 500,
        image: mapPointPng,
        status: "normal",
        styleType: "scan",
      },
      {
        id: "e2",
        coordinate: [117.1073, 36.6829],
        text: "点位",
        image: mapPointPng,
        status: "normal",
        styleType: "default",
      },
    ],
  },
];
//多边形模板数据
const polygonTemplate = [
  {
    polygonType: "zax", //多边形数据分类，区分不同类别的多边形，唯一
    name: "zax", //数据名称，可选项
    visible: true, //此类多边形的显隐控制
    styleType: "default", //此类多边形的样式类别，用于给此类多边形设置统一样式
    height: 300, //离地高度
    datas: [
      {
        id: "xxxxf", //多边形id，唯一
        coordinates: [
          [116.955, 36.6482],
          [117.0083, 36.6482],
          [116.9952, 36.5961],
          [116.9302, 36.5902],
        ], //多边形坐标数组
        text: "标签", //多边形显示文本
        wall: false, //多边形是否使用墙结构进行渲染
        status: "normal", //状态不同时，会进行删除重建，以实现不同的显示效果
        styleType: "default", //此多边形的样式类别，用于设置独立样式
      },
    ],
  },
];
//地图视角模板数据
const viewTemplate = {
  licheng: { center: [117.023, 36.4266], zoom: 10, height: 126435 },
  lixia: { center: [117.0712, 36.6663], zoom: 14, height: 66435 },
  shizhong: { center: [116.9999, 36.6521], zoom: 14, height: 66435 },
  gaoxin: { center: [117.1385, 36.6829], zoom: 14, height: 66435 },
};
//建筑物模板数据（倾斜摄影、白模）
const buildingTemplate = [
  // {
  //   type: "geojson",
  //   id: "xxxxx1",
  //   url: "./mock/lx.json",
  //   styleType: "default",
  //   options: {},
  // },
  // {
  //   type: "S3MTiles",
  //   id: "xxxxx2",
  //   url: "http://zhengtusd.tpddns.cn:8085/tile-datas/rest/realspace/202209071526305/Config.scp",
  //   heightCorrect: -60,
  //   options: {},
  // },
  {
    type: "3DTiles", //对应不同的处理形式
    id: "xxxxx3", //用于区分不同数据
    url: "http://39.91.166.201:10001/llvdi/tileset.json", //数据加载url
    heightCorrect: -30, //高度校正，用于建筑物贴地
    options: {}, //其他自定义配置项
  },
  // {
  //   type: "3DTiles",
  //   id: "xxxxx4",
  //   url: "http://resource.dvgis.cn/data/3dtiles/ljz/tileset.json",
  //   customShader: true,
  //   options: {},
  // },
  // {
  //   type: "3DTiles",
  //   id: "xxxxx3",
  //   url: "/ypzlbm/map/map/tileset.json",
  //   customShader: true,
  //   options: {},
  // },
  // {
  //   type: "3DTiles",
  //   id: "xxxxx3",
  //   url: "/jinan3DTiles/tileset.json",
  //   customShader: true,
  //   options: {},
  // },
];
//单体化模板数据
const integrationTemplate = [
  {
    coordinates: [116.9672, 36.6544], //建筑中心点坐标，以此构建矩形
    floors: [
      {
        id: "cccc1", //单体化对象id
        dimensions: [100, 28, 120], //长、宽、高
        height: 60, //中心点离地高度
        rotationZ: 5, //Z轴旋转角度（调整单体化矩形的方向）
        metadata: {},
      },
    ],
  },
  {
    coordinates: [116.96621, 36.65435],
    floors: [
      {
        id: "cccc2",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 2,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc3",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 5.1,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc4",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 8.2,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc5",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 11.3,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc6",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 14.4,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc7",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 17.5,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc8",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 20.6,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc9",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 23.7,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc10",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 26.8,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc11",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 29.9,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc12",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 33.0,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc13",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 36.1,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc14",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 39.2,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc15",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 42.3,
        rotationZ: 0,
        metadata: {},
      },
      {
        id: "cccc16",
        dimensions: [34, 27, 3.1], //28 86长、宽、高
        height: 45.4,
        rotationZ: 0,
        metadata: {},
      },
    ],
  },
];
export default {
  url: "/api/map",
  method: "get",
  response: (options) => {
    return {
      placeList: [
        {
          value: "licheng",
          label: "历城区",
        },
        {
          value: "lixia",
          label: "历下区",
        },
        {
          value: "shizhong",
          label: "市中区",
        },
        {
          value: "gaoxin",
          label: "高新区",
        },
      ],
      mapView: viewTemplate[options.query.place],
      markData: markTemplate,
      polygonData: polygonTemplate,
      buildingData: buildingTemplate,
      integrationData: integrationTemplate,
    };
  },
};
