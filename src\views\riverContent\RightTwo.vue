<template>
  <div class="box-shadow">
    <div class="box">
      <VillagePartTitle title="森林防火" :position="1" />
      <div class="video-box">
        <VideoPlayer :width="405" :height="270" :src="testMp4"></VideoPlayer>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, shallowRef, inject, computed } from "vue";
import VillagePartTitle from "../../components/common/VillagePartTitle.vue";
import VideoPlayer from "../../components/video/index.vue";
import testMp4 from "../../assets/videos/test.mp4";

const completeRate = computed(() => {

})

onMounted(() => {

})

</script>
<style scoped lang="scss">
.box-shadow {
  width: 465px;
  height: 352px;
  background: linear-gradient(0deg, #f0fff5c3, rgba(240, 255, 245, 0));
  box-shadow: 0px 4px 3px 0px rgba(0, 255, 93, 0.3), 0px 3px 2px 0px rgba(2, 13, 6, 0.77);
}

.box {
  width: 465px;
  height: 352px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(270deg, #0A170F, #0A170F);
  box-shadow: 0px -11px 21px 0px rgba(81, 207, 127, 0.17);
  border-radius: 0px 0px 14px 14px;
}

.video-box {
  width: 405px;
  height: 270px;
  margin-top: 7px;
  border: 1px solid;
  border-image: linear-gradient(-35deg, #374F40, #588769, #93BAA1) 1 1;
}
</style>
