<template>
  <span class="num">{{ showNum }}</span>
</template>
<script setup>
import { computed, onMounted, inject, reactive } from "vue";
import gsap from "gsap";

const props = defineProps({
  value: {
    type: Number,
    default: 0
  },
  position: {
    type: Number
  },
  start: {
    type: Number,
    default: 0
  },
  decimal: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 3
  },
})
const number = reactive({ value: props.start })
const showNum = computed(() => number.value.toFixed(props.decimal))

const pageTl = inject('pageTl')

function initTween() {
  const baseVars = { value: props.value, duration: props.duration }
  if (pageTl && typeof props.position !== 'undefined') {
    const numTween = gsap.to(number, baseVars);
    pageTl.add(numTween, props.position)
  } else {
    const delay = typeof props.position === 'number' ? props.position : 0
    gsap.to(number, { ...baseVars, delay });
  }

}
onMounted(() => {
  initTween()
})
</script>
<style scoped>
.num {
  color: #000;
}
</style>
