import { ref, shallowRef } from "vue";
import { defineStore } from "pinia";
import { useRouter, useRoute } from "vue-router";
import { getGeoServerData } from "../../api";

export const useGeoJsonStore = defineStore("geoJson", () => {
  const rangeGeojson = shallowRef([]);
  const regionGeojson = shallowRef([]);
  const router = useRouter();
  const route = useRoute();

  async function getData(query) {
    if (!query.gridId || !query.gridLevel) {
      return;
    }
    const requests = [];
    requests.push(getRegionGeojson(query.gridId, query.gridLevel));
    requests.push(getRangeGeojson(query.gridId, query.gridLevel))
    // let matchRange;
    // if (regionGeojson.value && regionGeojson.value.length) {
    //   matchRange = regionGeojson.value.find((fr) => {
    //     return f?.properties?.grid_id === query.gridId;
    //   });
    // }
    // if (matchRange) {
    //   rangeGeojson.value = [matchRange];
    // } else {
    //   requests.push(getRangeGeojson(query.gridId, query.gridLevel));
    // }
    await Promise.allSettled(requests);
  }

  async function getRangeGeojson(gridId, gridLevel) {
    try {
      const data = await getGeoServerData(getParamsString("grid_id", gridId, gridLevel));
      if (data.features) {
        rangeGeojson.value = data.features;
      }
    } catch (error) {
      console.log(error);
    }
  }

  async function getRegionGeojson(gridId, gridLevel) {
    try {
      const data = await getGeoServerData(getParamsString("parent_id", gridId, +gridLevel + 1));
      if (data.features) {
        regionGeojson.value = data.features;
      }
    } catch (error) {
      console.log(error);
    }
  }

  function goRegion(id) {
    if (!id) {
      return;
    }
    const gridIds = id.match(/^[^\-]+/);
    if (gridIds && gridIds[0]) {
      router.push({ query: { gridId: gridIds[0], gridLevel: +route.query.gridLevel + 1 } });
    }
  }

  return {
    rangeGeojson,
    regionGeojson,
    getData,
    goRegion,
  };
});

function getParamsString(type, gridId, gridLevel) {
  return `<GetFeature xmlns="http://www.opengis.net/wfs" service="WFS" version="1.1.0" outputFormat="application/json" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd"><Query typeName="zhdd:v_gis_pn_grid_lev${gridLevel}" srsName="EPSG:4326" xmlns:zhdd="/geoserver/zhdd/"><Filter xmlns="http://www.opengis.net/ogc"><And><PropertyIsNotEqualTo><PropertyName>grid_id</PropertyName><Literal></Literal></PropertyIsNotEqualTo><PropertyIsEqualTo><PropertyName>${type}</PropertyName><Literal>${gridId}</Literal></PropertyIsEqualTo></And></Filter></Query></GetFeature>`;
}
