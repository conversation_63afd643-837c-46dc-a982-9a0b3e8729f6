import jiangtuan from "./jiangtuan.json";
const mapProject = "./mock/map_project.png";
const mapEnergy = "./mock/map_energy.png";
const mapMountain = "./mock/map_mountain.png";
const mapRiver = "./mock/map_river.png";
const mapScenic = "./mock/map_scenic.png";
const mapVillage = "./mock/map_village.png";

const jiangtuanView = {
  center: [120.7704, 36.8076],
  extent: [
    [114.7501, 34.1803],
    [122.9653, 38.676],
  ],
  resolution: 0.0002459,
  height: 126435,
};
const jiangtuanMark = [
  {
    markType: "project",
    name: "重点工程",
    visible: true,
    styleType: "project",
    datas: [
      {
        id: "project1",
        coordinate: [120.7153, 36.8318],
        image: mapProject,
        status: "normal",
      },
      {
        id: "project2",
        coordinate: [120.6934, 36.7758],
        image: mapEnergy,
        status: "normal",
      },
    ],
  },
  {
    markType: "river",
    name: "河流",
    visible: false,
    styleType: "river",
    datas: [
      {
        id: "river1",
        coordinate: [120.7077, 36.8483],
        text: "五龙河",
        image: mapRiver,
        status: "normal",
      },
      {
        id: "river2",
        coordinate: [120.7523, 36.7918],
        text: "玉带河",
        image: mapRiver,
        status: "normal",
      },
    ],
  },
  {
    markType: "mountain",
    name: "山川",
    visible: false,
    styleType: "mountain",
    datas: [
      {
        id: "mountain1",
        coordinate: [120.6732, 36.8111],
        text: "五顶山",
        image: mapMountain,
        status: "normal",
      },
      {
        id: "mountain2",
        coordinate: [120.6858, 36.8081],
        text: "高儿山",
        image: mapMountain,
        status: "normal",
      },
      {
        id: "mountain3",
        coordinate: [120.7357, 36.8149],
        text: "凤山",
        image: mapMountain,
        status: "normal",
      },
    ],
  },
  {
    markType: "scenic",
    name: "景区",
    visible: false,
    styleType: "scenic",
    datas: [
      {
        id: "scenic1",
        coordinate: [120.7151, 36.7757],
        text: "樱花阡陌",
        image: mapScenic,
        status: "normal",
      },
    ],
  },
  {
    markType: "village",
    name: "美丽乡村",
    visible: false,
    styleType: "village",
    datas: [
      {
        id: "village1",
        coordinate: [120.7064, 36.8127],
        text: "新庄村",
        image: mapVillage,
        status: "normal",
      },
      {
        id: "village2",
        coordinate: [120.68, 36.8184],
        text: "南姜格庄村",
        image: mapVillage,
        status: "normal",
      },
    ],
  },
];
const jiangtuanPolygon = [
  {
    polygonType: "range",
    name: "城镇范围",
    visible: true,
    styleType: "polygon",
    datas: [
      {
        id: "xxxxx",
        coordinates: [
          [114.7498, 38.6769],
          [122.9693, 38.6786],
          [122.9695, 34.1787],
          [114.7492, 34.1796],
        ],
        holesCoordinates: jiangtuan.geometry.coordinates,
        status: "normal",
        styleType: "jiangtuan",
      },
    ],
  },
];
export default {
  url: "/api/jiangtuanmap",
  method: "get",
  response: (options) => {
    return {
      mapView: jiangtuanView,
      markData: jiangtuanMark,
      polygonData: jiangtuanPolygon,
    };
  },
};
