<template>
  <div class="enhanced-cesium-container">
    <!-- 基础Cesium组件 -->
    <CesiumBase ref="cesiumBase" />
    
    <!-- 功能控制面板 -->
    <div class="control-panel">
      <div class="panel-section">
        <h3>场景漫游</h3>
        <div class="controls">
          <button @click="startPathTour" :disabled="!pathData.length">开始路径漫游</button>
          <button @click="stopPathTour" :disabled="!isTourActive">停止漫游</button>
          <button @click="showProfile" :disabled="!pathData.length">显示剖面</button>
        </div>
        <div class="tour-info" v-if="currentPosition">
          <p>当前坐标: {{ currentPosition.longitude.toFixed(6) }}, {{ currentPosition.latitude.toFixed(6) }}</p>
          <p>海拔高度: {{ currentPosition.height.toFixed(2) }}m</p>
        </div>
      </div>

      <div class="panel-section">
        <h3>数据标签</h3>
        <div class="controls">
          <button @click="toggleLabels">{{ showLabels ? '隐藏' : '显示' }}标签</button>
          <button @click="addSampleData">添加示例数据</button>
        </div>
      </div>

      <div class="panel-section">
        <h3>分屏比较</h3>
        <div class="controls">
          <button @click="toggleSplitScreen">{{ splitScreenMode ? '关闭' : '开启' }}分屏</button>
          <select v-model="leftSceneType" @change="updateLeftScene" v-if="splitScreenMode">
            <option value="current">当前方案</option>
            <option value="plan1">方案一</option>
            <option value="plan2">方案二</option>
          </select>
          <select v-model="rightSceneType" @change="updateRightScene" v-if="splitScreenMode">
            <option value="current">当前方案</option>
            <option value="plan1">方案一</option>
            <option value="plan2">方案二</option>
          </select>
        </div>
      </div>

      <div class="panel-section">
        <h3>洪水淹没</h3>
        <div class="controls">
          <button @click="startFloodSimulation" :disabled="isFloodActive">开始模拟</button>
          <button @click="stopFloodSimulation" :disabled="!isFloodActive">停止模拟</button>
          <div class="slider-container" v-if="isFloodActive">
            <label>水位高度: {{ floodHeight }}m</label>
            <input 
              type="range" 
              v-model="floodHeight" 
              min="0" 
              max="50" 
              step="0.5"
              @input="updateFloodHeight"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 剖面分析窗口 -->
    <div class="profile-window" v-if="showProfileWindow">
      <div class="profile-header">
        <h3>地形剖面分析</h3>
        <button @click="closeProfile">×</button>
      </div>
      <div class="profile-content">
        <canvas ref="profileCanvas" width="600" height="300"></canvas>
      </div>
    </div>

    <!-- 分屏遮罩 -->
    <div class="split-screen-divider" v-if="splitScreenMode"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import CesiumBase from '../index.vue'
import { FloodStyle, PathStyle } from '../style'

// 响应式数据
const cesiumBase = ref(null)
const profileCanvas = ref(null)

// 漫游相关
const pathData = ref([])
const isTourActive = ref(false)
const currentPosition = ref(null)
const tourEntity = ref(null)

// 标签相关
const showLabels = ref(true)
const labelEntities = ref([])

// 分屏相关
const splitScreenMode = ref(false)
const leftSceneType = ref('current')
const rightSceneType = ref('plan1')

// 洪水模拟相关
const isFloodActive = ref(false)
const floodHeight = ref(0)
const floodEntities = ref([])

// 剖面分析
const showProfileWindow = ref(false)
const profileData = ref([])

let viewer = null
let tourAnimation = null
let floodAnimation = null

onMounted(() => {
  // 等待基础组件加载完成
  setTimeout(() => {
    if (cesiumBase.value && cesiumBase.value.mapViewer) {
      viewer = cesiumBase.value.mapViewer
      initEnhancedFeatures()
    }
  }, 1000)
})

onUnmounted(() => {
  cleanup()
})

// 初始化增强功能
function initEnhancedFeatures() {
  if (!viewer) return
  
  // 初始化示例路径数据
  initSamplePath()
  
  // 初始化示例标签数据
  initSampleLabels()
  
  // 设置相机移动监听
  viewer.camera.moveEnd.addEventListener(updateCurrentPosition)
}

// 初始化示例路径
function initSamplePath() {
  // 示例路径坐标（可以根据实际需求修改）
  pathData.value = [
    [116.39, 39.90, 100],
    [116.40, 39.91, 120],
    [116.41, 39.92, 150],
    [116.42, 39.93, 180],
    [116.43, 39.94, 200],
    [116.44, 39.95, 160],
    [116.45, 39.96, 140]
  ]
}

// 初始化示例标签
function initSampleLabels() {
  const sampleLabels = [
    { position: [116.40, 39.91, 0], text: '水利工程点A', info: '流量: 100m³/s' },
    { position: [116.42, 39.93, 0], text: '监测站B', info: '水位: 2.5m' },
    { position: [116.44, 39.95, 0], text: '泵站C', info: '功率: 500kW' }
  ]
  
  sampleLabels.forEach(label => {
    addDataLabel(label)
  })
}

// 场景漫游功能
function startPathTour() {
  if (!viewer || !pathData.value.length) return
  
  isTourActive.value = true
  
  // 创建路径线
  const positions = pathData.value.map(point => 
    Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
  )
  
  tourEntity.value = viewer.entities.add({
    polyline: {
      positions: positions,
      ...PathStyle.default
    }
  })
  
  // 开始相机漫游
  startCameraTour(positions)
}

function stopPathTour() {
  isTourActive.value = false
  
  if (tourAnimation) {
    viewer.clock.onTick.removeEventListener(tourAnimation)
    tourAnimation = null
  }
  
  if (tourEntity.value) {
    viewer.entities.remove(tourEntity.value)
    tourEntity.value = null
  }
}

function startCameraTour(positions) {
  let currentIndex = 0
  const totalPoints = positions.length
  
  tourAnimation = () => {
    if (!isTourActive.value || currentIndex >= totalPoints - 1) {
      stopPathTour()
      return
    }
    
    const start = positions[currentIndex]
    const end = positions[currentIndex + 1]
    
    // 计算相机位置和朝向
    const direction = Cesium.Cartesian3.subtract(end, start, new Cesium.Cartesian3())
    const heading = Math.atan2(direction.y, direction.x)
    
    viewer.camera.setView({
      destination: start,
      orientation: {
        heading: heading,
        pitch: Cesium.Math.toRadians(-15),
        roll: 0
      }
    })
    
    // 更新当前位置信息
    updateCurrentPosition()
    
    currentIndex++
    
    // 延迟到下一个点
    setTimeout(() => {
      if (tourAnimation && isTourActive.value) {
        tourAnimation()
      }
    }, 2000)
  }
  
  viewer.clock.onTick.addEventListener(tourAnimation)
  tourAnimation()
}

// 更新当前位置信息
function updateCurrentPosition() {
  if (!viewer) return
  
  const position = viewer.camera.position
  const cartographic = Cesium.Cartographic.fromCartesian(position)
  
  currentPosition.value = {
    longitude: Cesium.Math.toDegrees(cartographic.longitude),
    latitude: Cesium.Math.toDegrees(cartographic.latitude),
    height: cartographic.height
  }
}

// 显示地形剖面
function showProfile() {
  if (!pathData.value.length) return
  
  showProfileWindow.value = true
  
  // 获取路径上的高程数据
  getElevationProfile()
}

function closeProfile() {
  showProfileWindow.value = false
}

function getElevationProfile() {
  // 模拟获取高程数据（实际应用中需要调用地形服务）
  profileData.value = pathData.value.map((point, index) => ({
    distance: index * 1000, // 距离（米）
    elevation: point[2] + Math.random() * 50 // 高程（米）
  }))
  
  // 绘制剖面图
  drawProfile()
}

function drawProfile() {
  if (!profileCanvas.value || !profileData.value.length) return
  
  const canvas = profileCanvas.value
  const ctx = canvas.getContext('2d')
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制坐标轴和剖面线
  ctx.strokeStyle = '#333'
  ctx.lineWidth = 2
  
  // 绘制剖面线
  ctx.beginPath()
  profileData.value.forEach((point, index) => {
    const x = (point.distance / Math.max(...profileData.value.map(p => p.distance))) * (canvas.width - 40) + 20
    const y = canvas.height - 20 - ((point.elevation - Math.min(...profileData.value.map(p => p.elevation))) / 
      (Math.max(...profileData.value.map(p => p.elevation)) - Math.min(...profileData.value.map(p => p.elevation)))) * (canvas.height - 40)
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()
}

// 数据标签功能
function toggleLabels() {
  showLabels.value = !showLabels.value
  
  labelEntities.value.forEach(entity => {
    entity.show = showLabels.value
  })
}

function addSampleData() {
  const randomPos = [
    116.39 + Math.random() * 0.1,
    39.90 + Math.random() * 0.1,
    0
  ]
  
  addDataLabel({
    position: randomPos,
    text: `监测点${labelEntities.value.length + 1}`,
    info: `数据: ${Math.random() * 100}`
  })
}

function addDataLabel(labelData) {
  if (!viewer) return
  
  const entity = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(...labelData.position),
    billboard: {
      image: '/src/assets/images/marker.png', // 需要添加标记图标
      width: 32,
      height: 32,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
    },
    label: {
      text: labelData.text,
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -40)
    },
    properties: {
      info: labelData.info
    }
  })
  
  labelEntities.value.push(entity)
}

// 分屏比较功能
function toggleSplitScreen() {
  splitScreenMode.value = !splitScreenMode.value
  
  if (splitScreenMode.value) {
    enableSplitScreen()
  } else {
    disableSplitScreen()
  }
}

function enableSplitScreen() {
  if (!viewer) return
  
  // 设置分屏模式
  viewer.scene.splitPosition = 0.5
  viewer.scene.imagerySplitDirection = Cesium.SplitDirection.LEFT
}

function disableSplitScreen() {
  if (!viewer) return
  
  viewer.scene.splitPosition = 0
}

function updateLeftScene() {
  // 更新左侧场景内容
  console.log('更新左侧场景:', leftSceneType.value)
}

function updateRightScene() {
  // 更新右侧场景内容
  console.log('更新右侧场景:', rightSceneType.value)
}

// 洪水淹没模拟
function startFloodSimulation() {
  isFloodActive.value = true
  floodHeight.value = 0
  
  // 创建洪水区域（示例）
  createFloodArea()
  
  // 开始动画
  animateFlood()
}

function stopFloodSimulation() {
  isFloodActive.value = false
  
  if (floodAnimation) {
    clearInterval(floodAnimation)
    floodAnimation = null
  }
  
  // 清除洪水实体
  floodEntities.value.forEach(entity => {
    viewer.entities.remove(entity)
  })
  floodEntities.value = []
}

function createFloodArea() {
  if (!viewer) return
  
  // 示例洪水区域坐标
  const floodArea = [
    116.38, 39.89,
    116.46, 39.89,
    116.46, 39.97,
    116.38, 39.97
  ]
  
  const entity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(floodArea),
      material: Cesium.Color.BLUE.withAlpha(0.6),
      height: 0,
      extrudedHeight: 0,
      outline: true,
      outlineColor: Cesium.Color.DARKBLUE
    }
  })
  
  floodEntities.value.push(entity)
}

function animateFlood() {
  floodAnimation = setInterval(() => {
    if (floodHeight.value < 30) {
      floodHeight.value += 0.5
      updateFloodHeight()
    } else {
      stopFloodSimulation()
    }
  }, 200)
}

function updateFloodHeight() {
  floodEntities.value.forEach(entity => {
    if (entity.polygon) {
      entity.polygon.extrudedHeight = floodHeight.value
    }
  })
}

// 清理资源
function cleanup() {
  stopPathTour()
  stopFloodSimulation()
  
  if (viewer) {
    viewer.camera.moveEnd.removeEventListener(updateCurrentPosition)
  }
}
</script>

<style scoped>
.enhanced-cesium-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 15px;
  color: white;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.panel-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #444;
  padding-bottom: 15px;
}

.panel-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 16px;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.controls button {
  padding: 8px 12px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:hover {
  background: #45a049;
}

.controls button:disabled {
  background: #666;
  cursor: not-allowed;
}

.controls select {
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #666;
  background: #333;
  color: white;
}

.tour-info {
  margin-top: 10px;
  font-size: 12px;
  color: #ccc;
}

.tour-info p {
  margin: 4px 0;
}

.slider-container {
  margin-top: 10px;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
}

.slider-container input[type="range"] {
  width: 100%;
}

.profile-window {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 640px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  z-index: 1000;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #444;
  color: white;
}

.profile-header h3 {
  margin: 0;
  color: #4CAF50;
}

.profile-header button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
}

.profile-content {
  padding: 15px;
}

.profile-content canvas {
  background: white;
  border-radius: 4px;
}

.split-screen-divider {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background: #ff0000;
  z-index: 999;
  pointer-events: none;
}
</style>
