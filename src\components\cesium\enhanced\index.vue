<template>
  <div class="enhanced-cesium-container">
    <!-- 基础Cesium组件 -->
    <CesiumBase ref="cesiumBase" />
    
    <!-- 功能控制面板 -->
    <div class="control-panel">
      <div class="panel-section">
        <h3>场景漫游</h3>
        <div class="controls">
          <button @click="startPathTour" :disabled="!pathData.length">开始路径漫游</button>
          <button @click="stopPathTour" :disabled="!isTourActive">停止漫游</button>
          <button @click="showProfile" :disabled="!pathData.length">显示剖面</button>
        </div>
        <div class="tour-info" v-if="currentPosition">
          <p>当前坐标: {{ currentPosition.longitude.toFixed(6) }}, {{ currentPosition.latitude.toFixed(6) }}</p>
          <p>海拔高度: {{ currentPosition.height.toFixed(2) }}m</p>
        </div>
      </div>

      <div class="panel-section">
        <h3>数据标签</h3>
        <div class="controls">
          <button @click="toggleLabels">{{ showLabels ? '隐藏' : '显示' }}标签</button>
          <button @click="addSampleData">添加示例数据</button>
        </div>
      </div>

      <div class="panel-section">
        <h3>分屏比较</h3>
        <div class="controls">
          <button @click="toggleSplitScreen">{{ splitScreenMode ? '关闭' : '开启' }}分屏</button>
          <select v-model="leftSceneType" @change="updateLeftScene" v-if="splitScreenMode">
            <option value="vector">影像图</option>
            <option value="raster">卫星图</option>
            <option value="terrain">地势图</option>
            <option value="dem">高程图</option>
          </select>
          <select v-model="rightSceneType" @change="updateRightScene" v-if="splitScreenMode">
            <option value="vector">影像图</option>
            <option value="raster">卫星图</option>
            <option value="terrain">地势图</option>
            <option value="dem">高程图</option>
          </select>
        </div>
      </div>

      <div class="panel-section">
        <h3>洪水淹没模拟</h3>
        <div class="controls">
          <button @click="startFloodSimulation" :disabled="isFloodActive">开始3D模拟</button>
          <button @click="stopFloodSimulation" :disabled="!isFloodActive">停止模拟</button>
          <div class="flood-controls" v-if="isFloodActive">
            <div class="slider-container">
              <label>水位高度: {{ floodHeight.toFixed(1) }}m</label>
              <input
                type="range"
                v-model="floodHeight"
                min="0"
                max="50"
                step="0.5"
                @input="updateFloodHeight"
              />
            </div>
            <div class="slider-container">
              <label>波浪强度: {{ waveIntensity.toFixed(2) }}</label>
              <input
                type="range"
                v-model="waveIntensity"
                min="0"
                max="1"
                step="0.1"
                @input="updateWaveIntensity"
              />
            </div>
            <div class="slider-container">
              <label>水体透明度: {{ waterTransparency.toFixed(2) }}</label>
              <input
                type="range"
                v-model="waterTransparency"
                min="0.1"
                max="1"
                step="0.1"
                @input="updateWaterTransparency"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 剖面分析窗口 -->
    <div class="profile-window" v-if="showProfileWindow">
      <div class="profile-header">
        <h3>地形剖面分析</h3>
        <button @click="closeProfile">×</button>
      </div>
      <div class="profile-content">
        <canvas ref="profileCanvas" width="600" height="300"></canvas>
      </div>
    </div>

    <!-- 分屏遮罩 -->
    <div class="split-screen-divider" v-if="splitScreenMode"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import CesiumBase from '../index.vue'
import { FloodStyle, PathStyle } from '../style'
import { useEnhancedCesiumStore } from '@/stores/modules/enhancedCesium'

// 响应式数据
const cesiumBase = ref(null)
const profileCanvas = ref(null)
const store = useEnhancedCesiumStore()

// 漫游相关
const pathData = ref([])
const isTourActive = ref(false)
const currentPosition = ref(null)
const tourEntity = ref(null)

// 标签相关
const showLabels = ref(true)
const labelEntities = ref([])

// 分屏相关
const splitScreenMode = ref(false)
const leftSceneType = ref('vector')
const rightSceneType = ref('raster')

// 洪水模拟相关
const isFloodActive = ref(false)
const floodHeight = ref(0)
const floodEntities = ref([])
const waveIntensity = ref(0.5)
const waterTransparency = ref(0.7)

// 剖面分析
const showProfileWindow = ref(false)
const profileData = ref([])

let viewer = null
let tourAnimation = null
let floodAnimation = null

onMounted(() => {
  // 等待基础组件加载完成
  setTimeout(() => {
    if (cesiumBase.value && cesiumBase.value.mapViewer) {
      viewer = cesiumBase.value.mapViewer
      initEnhancedFeatures()
    }
  }, 1000)
})

onUnmounted(() => {
  cleanup()
})

// 初始化增强功能
function initEnhancedFeatures() {
  if (!viewer) return
  
  // 初始化示例路径数据
  initSamplePath()
  
  // 初始化示例标签数据
  initSampleLabels()
  
  // 设置相机移动监听
  viewer.camera.moveEnd.addEventListener(updateCurrentPosition)
}

// 初始化示例路径
function initSamplePath() {
  // 从store获取路径数据
  const routes = store.engineeringData.pathRoutes
  if (routes.length > 0) {
    pathData.value = routes[0].points
  }
}

// 初始化示例标签
function initSampleLabels() {
  // 从store获取工程数据
  const projects = store.engineeringData.waterProjects

  projects.forEach(project => {
    addDataLabel({
      position: project.position,
      text: project.name,
      info: `类型: ${project.type}, 状态: ${project.status}, 进度: ${project.progress}%`
    })
  })
}

// 场景漫游功能
function startPathTour() {
  if (!viewer || !pathData.value.length) return
  
  isTourActive.value = true
  
  // 创建路径线
  const positions = pathData.value.map(point => 
    Cesium.Cartesian3.fromDegrees(point[0], point[1], point[2])
  )
  
  tourEntity.value = viewer.entities.add({
    polyline: {
      positions: positions,
      ...PathStyle.default
    }
  })
  
  // 开始相机漫游
  startCameraTour(positions)
}

function stopPathTour() {
  isTourActive.value = false
  
  if (tourAnimation) {
    viewer.clock.onTick.removeEventListener(tourAnimation)
    tourAnimation = null
  }
  
  if (tourEntity.value) {
    viewer.entities.remove(tourEntity.value)
    tourEntity.value = null
  }
}

function startCameraTour(positions) {
  let currentIndex = 0
  const totalPoints = positions.length
  
  tourAnimation = () => {
    if (!isTourActive.value || currentIndex >= totalPoints - 1) {
      stopPathTour()
      return
    }
    
    const start = positions[currentIndex]
    const end = positions[currentIndex + 1]
    
    // 计算相机位置和朝向
    const direction = Cesium.Cartesian3.subtract(end, start, new Cesium.Cartesian3())
    const heading = Math.atan2(direction.y, direction.x)
    
    viewer.camera.setView({
      destination: start,
      orientation: {
        heading: heading,
        pitch: Cesium.Math.toRadians(-15),
        roll: 0
      }
    })
    
    // 更新当前位置信息
    updateCurrentPosition()
    
    currentIndex++
    
    // 延迟到下一个点
    setTimeout(() => {
      if (tourAnimation && isTourActive.value) {
        tourAnimation()
      }
    }, 2000)
  }
  
  viewer.clock.onTick.addEventListener(tourAnimation)
  tourAnimation()
}

// 更新当前位置信息
function updateCurrentPosition() {
  if (!viewer) return
  
  const position = viewer.camera.position
  const cartographic = Cesium.Cartographic.fromCartesian(position)
  
  currentPosition.value = {
    longitude: Cesium.Math.toDegrees(cartographic.longitude),
    latitude: Cesium.Math.toDegrees(cartographic.latitude),
    height: cartographic.height
  }
}

// 显示地形剖面
function showProfile() {
  if (!pathData.value.length) return

  showProfileWindow.value = true
  store.showProfile('route001') // 使用第一个路径

  // 获取路径上的高程数据
  getElevationProfile()
}

function closeProfile() {
  showProfileWindow.value = false
  store.hideProfile()
}

function getElevationProfile() {
  // 从store获取剖面数据
  profileData.value = store.profileState.data.length > 0
    ? store.profileState.data
    : pathData.value.map((point, index) => ({
        distance: index * 1000, // 距离（米）
        elevation: point[2] + Math.random() * 50 // 高程（米）
      }))

  // 绘制剖面图
  drawProfile()
}

function drawProfile() {
  if (!profileCanvas.value || !profileData.value.length) return
  
  const canvas = profileCanvas.value
  const ctx = canvas.getContext('2d')
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制坐标轴和剖面线
  ctx.strokeStyle = '#333'
  ctx.lineWidth = 2
  
  // 绘制剖面线
  ctx.beginPath()
  profileData.value.forEach((point, index) => {
    const x = (point.distance / Math.max(...profileData.value.map(p => p.distance))) * (canvas.width - 40) + 20
    const y = canvas.height - 20 - ((point.elevation - Math.min(...profileData.value.map(p => p.elevation))) / 
      (Math.max(...profileData.value.map(p => p.elevation)) - Math.min(...profileData.value.map(p => p.elevation)))) * (canvas.height - 40)
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()
}

// 数据标签功能
function toggleLabels() {
  showLabels.value = !showLabels.value
  
  labelEntities.value.forEach(entity => {
    entity.show = showLabels.value
  })
}

function addSampleData() {
  const randomPos = [
    116.39 + Math.random() * 0.1,
    39.90 + Math.random() * 0.1,
    0
  ]
  
  addDataLabel({
    position: randomPos,
    text: `监测点${labelEntities.value.length + 1}`,
    info: `数据: ${Math.random() * 100}`
  })
}

function addDataLabel(labelData) {
  if (!viewer) return

  // 创建简单的圆形标记
  const canvas = document.createElement('canvas')
  canvas.width = 32
  canvas.height = 32
  const ctx = canvas.getContext('2d')

  // 绘制圆形标记
  ctx.fillStyle = '#4CAF50'
  ctx.beginPath()
  ctx.arc(16, 16, 14, 0, 2 * Math.PI)
  ctx.fill()

  ctx.strokeStyle = '#ffffff'
  ctx.lineWidth = 2
  ctx.stroke()

  ctx.fillStyle = '#ffffff'
  ctx.beginPath()
  ctx.arc(16, 16, 6, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#4CAF50'
  ctx.beginPath()
  ctx.arc(16, 16, 3, 0, 2 * Math.PI)
  ctx.fill()

  const entity = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(...labelData.position),
    billboard: {
      image: canvas,
      width: 32,
      height: 32,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
    },
    label: {
      text: labelData.text,
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -40)
    },
    properties: {
      info: labelData.info
    }
  })

  labelEntities.value.push(entity)
}

// 分屏比较功能
function toggleSplitScreen() {
  splitScreenMode.value = !splitScreenMode.value

  if (splitScreenMode.value) {
    enableSplitScreen()
  } else {
    disableSplitScreen()
  }
}

function enableSplitScreen() {
  if (!viewer) return

  // 设置分屏模式
  viewer.scene.splitPosition = 0.5
  viewer.scene.imagerySplitDirection = Cesium.SplitDirection.LEFT

  // 初始化左右两侧的图层
  updateLeftScene()
  updateRightScene()
}

function disableSplitScreen() {
  if (!viewer) return

  viewer.scene.splitPosition = 0

  // 重置所有图层的分屏设置
  viewer.imageryLayers._layers.forEach(layer => {
    layer.splitDirection = Cesium.SplitDirection.NONE
  })
}

function updateLeftScene() {
  if (!viewer || !splitScreenMode.value) return

  // 更新左侧场景内容
  console.log('更新左侧场景:', leftSceneType.value)

  // 移除现有的左侧图层
  removeLayersByType('left')

  // 添加新的左侧图层
  addImageryLayer(leftSceneType.value, Cesium.SplitDirection.LEFT)
}

function updateRightScene() {
  if (!viewer || !splitScreenMode.value) return

  // 更新右侧场景内容
  console.log('更新右侧场景:', rightSceneType.value)

  // 移除现有的右侧图层
  removeLayersByType('right')

  // 添加新的右侧图层
  addImageryLayer(rightSceneType.value, Cesium.SplitDirection.RIGHT)
}

function removeLayersByType(side) {
  const targetDirection = side === 'left' ? Cesium.SplitDirection.LEFT : Cesium.SplitDirection.RIGHT

  const layersToRemove = []
  viewer.imageryLayers._layers.forEach(layer => {
    if (layer.splitDirection === targetDirection) {
      layersToRemove.push(layer)
    }
  })

  layersToRemove.forEach(layer => {
    viewer.imageryLayers.remove(layer)
  })
}

function addImageryLayer(type, splitDirection) {
  let provider

  switch (type) {
    case 'vector':
      // 影像图 - 天地图影像
      provider = new Cesium.WebMapTileServiceImageryProvider({
        url: `https://t0.tianditu.gov.cn/img_w/wmts?tk=${store.systemStore?.mapKey || 'your-key'}`,
        layer: 'img',
        style: 'default',
        format: 'tiles',
        tileMatrixSetID: 'w',
        maximumLevel: 18
      })
      break

    case 'raster':
      // 卫星图 - 天地图卫星影像
      provider = new Cesium.WebMapTileServiceImageryProvider({
        url: `https://t0.tianditu.gov.cn/img_w/wmts?tk=${store.systemStore?.mapKey || 'your-key'}`,
        layer: 'img',
        style: 'default',
        format: 'tiles',
        tileMatrixSetID: 'w',
        maximumLevel: 18
      })
      break

    case 'terrain':
      // 地势图 - 地形晕渲图
      provider = new Cesium.WebMapTileServiceImageryProvider({
        url: `https://t0.tianditu.gov.cn/ter_w/wmts?tk=${store.systemStore?.mapKey || 'your-key'}`,
        layer: 'ter',
        style: 'default',
        format: 'tiles',
        tileMatrixSetID: 'w',
        maximumLevel: 14
      })
      break

    case 'dem':
      // 高程图 - 使用Cesium的地形数据
      provider = new Cesium.SingleTileImageryProvider({
        url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        rectangle: Cesium.Rectangle.MAX_VALUE
      })
      break

    default:
      return
  }

  const layer = new Cesium.ImageryLayer(provider)
  layer.splitDirection = splitDirection
  viewer.imageryLayers.add(layer)
}

// 洪水淹没模拟
function startFloodSimulation() {
  isFloodActive.value = true
  floodHeight.value = 0

  // 从store获取洪水场景数据
  const scenario = store.engineeringData.floodScenarios[0]
  if (scenario) {
    store.startFloodSimulation(scenario.id)
    createFloodArea(scenario.affectedArea)
    animateFlood()
  }
}

function stopFloodSimulation() {
  isFloodActive.value = false
  store.stopFloodSimulation()

  if (floodAnimation) {
    clearInterval(floodAnimation)
    floodAnimation = null
  }

  // 清除洪水实体
  floodEntities.value.forEach(entity => {
    viewer.entities.remove(entity)
  })
  floodEntities.value = []
}

function createFloodArea(areaCoords) {
  if (!viewer || !areaCoords) return

  // 将区域坐标转换为Cesium格式
  const floodArea = []
  areaCoords.forEach(coord => {
    floodArea.push(coord[0], coord[1])
  })

  // 创建主要的水体区域
  const waterEntity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(floodArea),
      material: createWaterMaterial(),
      height: 0,
      extrudedHeight: 0,
      outline: false,
      shadows: Cesium.ShadowMode.ENABLED
    }
  })

  // 创建水面反射效果
  const reflectionEntity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(floodArea),
      material: new Cesium.CallbackProperty(() => {
        const time = Date.now() * 0.001
        const alpha = 0.3 + 0.2 * Math.sin(time * 2)
        return Cesium.Color.WHITE.withAlpha(alpha)
      }, false),
      height: 0.05,
      outline: false
    }
  })

  // 创建边缘泡沫效果
  const foamEntity = viewer.entities.add({
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArray(floodArea.concat(floodArea.slice(0, 2))),
      width: 3,
      material: new Cesium.CallbackProperty(() => {
        const time = Date.now() * 0.002
        const alpha = 0.5 + 0.3 * Math.sin(time * 3)
        return Cesium.Color.WHITE.withAlpha(alpha)
      }, false),
      clampToGround: true
    }
  })

  floodEntities.value.push(waterEntity, reflectionEntity, foamEntity)
}

// 创建水体材质
function createWaterMaterial() {
  // 使用Cesium内置的颜色，添加透明度效果
  return new Cesium.CallbackProperty(() => {
    if (!viewer) return Cesium.Color.BLUE.withAlpha(0.7)

    const time = viewer.clock.currentTime
    const seconds = Cesium.JulianDate.secondsDifference(time, viewer.clock.startTime)

    // 创建波动的蓝色效果
    const alpha = 0.6 + 0.2 * Math.sin(seconds * 0.5)
    const blue = 0.3 + 0.2 * Math.sin(seconds * 0.3)

    return new Cesium.Color(0.0, 0.2, blue, alpha)
  }, false)
}

// 创建波纹纹理
function createWaveTexture() {
  const canvas = document.createElement('canvas')
  canvas.width = 256
  canvas.height = 256
  const ctx = canvas.getContext('2d')

  // 创建径向渐变波纹
  const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128)
  gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)')
  gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.4)')
  gradient.addColorStop(0.6, 'rgba(255, 255, 255, 0.1)')
  gradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, 256, 256)

  return canvas
}

function animateFlood() {
  const maxLevel = store.floodState.maxLevel
  floodAnimation = setInterval(() => {
    if (floodHeight.value < maxLevel) {
      floodHeight.value += 0.5
      store.updateFloodLevel(floodHeight.value)
      updateFloodHeight()
    } else {
      stopFloodSimulation()
    }
  }, 200)
}

function updateFloodHeight() {
  floodEntities.value.forEach(entity => {
    if (entity.polygon) {
      entity.polygon.extrudedHeight = floodHeight.value
    }
  })
}

function updateWaveIntensity() {
  // 波浪强度影响动画速度和颜色变化
  console.log('更新波浪强度:', waveIntensity.value)
  // 这里可以通过修改材质回调函数中的参数来实现
}

function updateWaterTransparency() {
  // 更新水体透明度
  console.log('更新水体透明度:', waterTransparency.value)

  floodEntities.value.forEach(entity => {
    if (entity.polygon && entity.polygon.material) {
      // 重新创建材质以应用新的透明度
      entity.polygon.material = new Cesium.CallbackProperty(() => {
        if (!viewer) return Cesium.Color.BLUE.withAlpha(waterTransparency.value)

        const time = viewer.clock.currentTime
        const seconds = Cesium.JulianDate.secondsDifference(time, viewer.clock.startTime)

        // 创建波动的蓝色效果，使用当前透明度设置
        const alpha = waterTransparency.value * (0.8 + 0.2 * Math.sin(seconds * 0.5 * waveIntensity.value))
        const blue = 0.3 + 0.2 * Math.sin(seconds * 0.3 * waveIntensity.value)

        return new Cesium.Color(0.0, 0.2, blue, alpha)
      }, false)
    }
  })
}

// 清理资源
function cleanup() {
  stopPathTour()
  stopFloodSimulation()
  
  if (viewer) {
    viewer.camera.moveEnd.removeEventListener(updateCurrentPosition)
  }
}
</script>

<style scoped>
.enhanced-cesium-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 15px;
  color: white;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.panel-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #444;
  padding-bottom: 15px;
}

.panel-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 16px;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.controls button {
  padding: 8px 12px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:hover {
  background: #45a049;
}

.controls button:disabled {
  background: #666;
  cursor: not-allowed;
}

.controls select {
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #666;
  background: #333;
  color: white;
}

.tour-info {
  margin-top: 10px;
  font-size: 12px;
  color: #ccc;
}

.tour-info p {
  margin: 4px 0;
}

.flood-controls {
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.slider-container {
  margin-bottom: 15px;
}

.slider-container:last-child {
  margin-bottom: 0;
}

.slider-container label {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}

.slider-container input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #333;
  outline: none;
  -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.profile-window {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 640px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  z-index: 1000;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #444;
  color: white;
}

.profile-header h3 {
  margin: 0;
  color: #4CAF50;
}

.profile-header button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
}

.profile-content {
  padding: 15px;
}

.profile-content canvas {
  background: white;
  border-radius: 4px;
}

.split-screen-divider {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background: #ff0000;
  z-index: 999;
  pointer-events: none;
}
</style>
