define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Cartesian2-47311507","./Math-119be1a3","./FeatureDetection-806b12f0","./Event-16a2dfbf","./RuntimeError-4a5c8994"],(function(e,t,r,n,a,i,s,o,u){"use strict";function c(e){this._ellipsoid=n.defaultValue(e,a.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(c.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),c.prototype.project=function(e,r){var a=this._semimajorAxis,i=e.longitude*a,s=e.latitude*a,o=e.height;return n.defined(r)?(r.x=i,r.y=s,r.z=o,r):new t.Cartesian3(i,s,o)},c.prototype.unproject=function(e,r){var a=this._oneOverSemimajorAxis,i=e.x*a,s=e.y*a,o=e.z;return n.defined(r)?(r.longitude=i,r.latitude=s,r.height=o,r):new t.Cartographic(i,s,o)};var d=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function f(e,t){this.start=n.defaultValue(e,0),this.stop=n.defaultValue(t,0)}function l(e,r){this.center=t.Cartesian3.clone(n.defaultValue(e,t.Cartesian3.ZERO)),this.radius=n.defaultValue(r,0)}var p=new t.Cartesian3,h=new t.Cartesian3,m=new t.Cartesian3,y=new t.Cartesian3,v=new t.Cartesian3,C=new t.Cartesian3,g=new t.Cartesian3,q=new t.Cartesian3,w=new t.Cartesian3,b=new t.Cartesian3,R=new t.Cartesian3,x=new t.Cartesian3,A=4/3*i.CesiumMath.PI;l.fromPoints=function(e,r){if(n.defined(r)||(r=new l),!n.defined(e)||0===e.length)return r.center=t.Cartesian3.clone(t.Cartesian3.ZERO,r.center),r.radius=0,r;var a,i=t.Cartesian3.clone(e[0],g),s=t.Cartesian3.clone(i,p),o=t.Cartesian3.clone(i,h),u=t.Cartesian3.clone(i,m),c=t.Cartesian3.clone(i,y),d=t.Cartesian3.clone(i,v),f=t.Cartesian3.clone(i,C),A=e.length;for(a=1;a<A;a++){t.Cartesian3.clone(e[a],i);var O=i.x,E=i.y,S=i.z;O<s.x&&t.Cartesian3.clone(i,s),O>c.x&&t.Cartesian3.clone(i,c),E<o.y&&t.Cartesian3.clone(i,o),E>d.y&&t.Cartesian3.clone(i,d),S<u.z&&t.Cartesian3.clone(i,u),S>f.z&&t.Cartesian3.clone(i,f)}var _=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(c,s,q)),I=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(d,o,q)),T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(f,u,q)),P=s,k=c,U=_;I>U&&(U=I,P=o,k=d),T>U&&(U=T,P=u,k=f);var B=w;B.x=.5*(P.x+k.x),B.y=.5*(P.y+k.y),B.z=.5*(P.z+k.z);var D=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(k,B,q)),z=Math.sqrt(D),V=b;V.x=s.x,V.y=o.y,V.z=u.z;var j=R;j.x=c.x,j.y=d.y,j.z=f.z;var L=t.Cartesian3.midpoint(V,j,x),M=0;for(a=0;a<A;a++){t.Cartesian3.clone(e[a],i);var K=t.Cartesian3.magnitude(t.Cartesian3.subtract(i,L,q));K>M&&(M=K);var F=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(i,B,q));if(F>D){var N=Math.sqrt(F);D=(z=.5*(z+N))*z;var H=N-z;B.x=(z*B.x+H*i.x)/N,B.y=(z*B.y+H*i.y)/N,B.z=(z*B.z+H*i.z)/N}}return z<M?(t.Cartesian3.clone(B,r.center),r.radius=z):(t.Cartesian3.clone(L,r.center),r.radius=M),r};var O=new c,E=new t.Cartesian3,S=new t.Cartesian3,_=new t.Cartographic,I=new t.Cartographic;l.fromRectangle2D=function(e,t,r){return l.fromRectangleWithHeights2D(e,t,0,0,r)},l.fromRectangleWithHeights2D=function(e,r,i,s,o){if(n.defined(o)||(o=new l),!n.defined(e))return o.center=t.Cartesian3.clone(t.Cartesian3.ZERO,o.center),o.radius=0,o;r=n.defaultValue(r,O),a.Rectangle.southwest(e,_),_.height=i,a.Rectangle.northeast(e,I),I.height=s;var u=r.project(_,E),c=r.project(I,S),d=c.x-u.x,f=c.y-u.y,p=c.z-u.z;o.radius=.5*Math.sqrt(d*d+f*f+p*p);var h=o.center;return h.x=u.x+.5*d,h.y=u.y+.5*f,h.z=u.z+.5*p,o};var T=[];l.fromRectangle3D=function(e,r,i,s){if(r=n.defaultValue(r,a.Ellipsoid.WGS84),i=n.defaultValue(i,0),n.defined(s)||(s=new l),!n.defined(e))return s.center=t.Cartesian3.clone(t.Cartesian3.ZERO,s.center),s.radius=0,s;var o=a.Rectangle.subsample(e,r,i,T);return l.fromPoints(o,s)},l.fromVertices=function(e,r,a,i){if(n.defined(i)||(i=new l),!n.defined(e)||0===e.length)return i.center=t.Cartesian3.clone(t.Cartesian3.ZERO,i.center),i.radius=0,i;r=n.defaultValue(r,t.Cartesian3.ZERO),a=n.defaultValue(a,3);var s=g;s.x=e[0]+r.x,s.y=e[1]+r.y,s.z=e[2]+r.z;var o,u=t.Cartesian3.clone(s,p),c=t.Cartesian3.clone(s,h),d=t.Cartesian3.clone(s,m),f=t.Cartesian3.clone(s,y),A=t.Cartesian3.clone(s,v),O=t.Cartesian3.clone(s,C),E=e.length;for(o=0;o<E;o+=a){var S=e[o]+r.x,_=e[o+1]+r.y,I=e[o+2]+r.z;s.x=S,s.y=_,s.z=I,S<u.x&&t.Cartesian3.clone(s,u),S>f.x&&t.Cartesian3.clone(s,f),_<c.y&&t.Cartesian3.clone(s,c),_>A.y&&t.Cartesian3.clone(s,A),I<d.z&&t.Cartesian3.clone(s,d),I>O.z&&t.Cartesian3.clone(s,O)}var T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(f,u,q)),P=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(A,c,q)),k=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(O,d,q)),U=u,B=f,D=T;P>D&&(D=P,U=c,B=A),k>D&&(D=k,U=d,B=O);var z=w;z.x=.5*(U.x+B.x),z.y=.5*(U.y+B.y),z.z=.5*(U.z+B.z);var V=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(B,z,q)),j=Math.sqrt(V),L=b;L.x=u.x,L.y=c.y,L.z=d.z;var M=R;M.x=f.x,M.y=A.y,M.z=O.z;var K=t.Cartesian3.midpoint(L,M,x),F=0;for(o=0;o<E;o+=a){s.x=e[o]+r.x,s.y=e[o+1]+r.y,s.z=e[o+2]+r.z;var N=t.Cartesian3.magnitude(t.Cartesian3.subtract(s,K,q));N>F&&(F=N);var H=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(s,z,q));if(H>V){var Y=Math.sqrt(H);V=(j=.5*(j+Y))*j;var J=Y-j;z.x=(j*z.x+J*s.x)/Y,z.y=(j*z.y+J*s.y)/Y,z.z=(j*z.z+J*s.z)/Y}}return j<F?(t.Cartesian3.clone(z,i.center),i.radius=j):(t.Cartesian3.clone(K,i.center),i.radius=F),i},l.fromEncodedCartesianVertices=function(e,r,a){if(n.defined(a)||(a=new l),!n.defined(e)||!n.defined(r)||e.length!==r.length||0===e.length)return a.center=t.Cartesian3.clone(t.Cartesian3.ZERO,a.center),a.radius=0,a;var i=g;i.x=e[0]+r[0],i.y=e[1]+r[1],i.z=e[2]+r[2];var s,o=t.Cartesian3.clone(i,p),u=t.Cartesian3.clone(i,h),c=t.Cartesian3.clone(i,m),d=t.Cartesian3.clone(i,y),f=t.Cartesian3.clone(i,v),A=t.Cartesian3.clone(i,C),O=e.length;for(s=0;s<O;s+=3){var E=e[s]+r[s],S=e[s+1]+r[s+1],_=e[s+2]+r[s+2];i.x=E,i.y=S,i.z=_,E<o.x&&t.Cartesian3.clone(i,o),E>d.x&&t.Cartesian3.clone(i,d),S<u.y&&t.Cartesian3.clone(i,u),S>f.y&&t.Cartesian3.clone(i,f),_<c.z&&t.Cartesian3.clone(i,c),_>A.z&&t.Cartesian3.clone(i,A)}var I=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(d,o,q)),T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(f,u,q)),P=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(A,c,q)),k=o,U=d,B=I;T>B&&(B=T,k=u,U=f),P>B&&(B=P,k=c,U=A);var D=w;D.x=.5*(k.x+U.x),D.y=.5*(k.y+U.y),D.z=.5*(k.z+U.z);var z=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(U,D,q)),V=Math.sqrt(z),j=b;j.x=o.x,j.y=u.y,j.z=c.z;var L=R;L.x=d.x,L.y=f.y,L.z=A.z;var M=t.Cartesian3.midpoint(j,L,x),K=0;for(s=0;s<O;s+=3){i.x=e[s]+r[s],i.y=e[s+1]+r[s+1],i.z=e[s+2]+r[s+2];var F=t.Cartesian3.magnitude(t.Cartesian3.subtract(i,M,q));F>K&&(K=F);var N=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(i,D,q));if(N>z){var H=Math.sqrt(N);z=(V=.5*(V+H))*V;var Y=H-V;D.x=(V*D.x+Y*i.x)/H,D.y=(V*D.y+Y*i.y)/H,D.z=(V*D.z+Y*i.z)/H}}return V<K?(t.Cartesian3.clone(D,a.center),a.radius=V):(t.Cartesian3.clone(M,a.center),a.radius=K),a},l.fromCornerPoints=function(e,r,a){n.defined(a)||(a=new l);var i=t.Cartesian3.midpoint(e,r,a.center);return a.radius=t.Cartesian3.distance(i,r),a},l.fromEllipsoid=function(e,r){return n.defined(r)||(r=new l),t.Cartesian3.clone(t.Cartesian3.ZERO,r.center),r.radius=e.maximumRadius,r};var P=new t.Cartesian3;l.fromBoundingSpheres=function(e,r){if(n.defined(r)||(r=new l),!n.defined(e)||0===e.length)return r.center=t.Cartesian3.clone(t.Cartesian3.ZERO,r.center),r.radius=0,r;var a=e.length;if(1===a)return l.clone(e[0],r);if(2===a)return l.union(e[0],e[1],r);var i,s=[];for(i=0;i<a;i++)s.push(e[i].center);var o=(r=l.fromPoints(s,r)).center,u=r.radius;for(i=0;i<a;i++){var c=e[i];u=Math.max(u,t.Cartesian3.distance(o,c.center,P)+c.radius)}return r.radius=u,r};var k=new t.Cartesian3,U=new t.Cartesian3,B=new t.Cartesian3;l.fromOrientedBoundingBox=function(e,r){n.defined(r)||(r=new l);var a=e.halfAxes,i=s.Matrix3.getColumn(a,0,k),o=s.Matrix3.getColumn(a,1,U),u=s.Matrix3.getColumn(a,2,B);return t.Cartesian3.add(i,o,i),t.Cartesian3.add(i,u,i),r.center=t.Cartesian3.clone(e.center,r.center),r.radius=t.Cartesian3.magnitude(i),r},l.clone=function(e,r){if(n.defined(e))return n.defined(r)?(r.center=t.Cartesian3.clone(e.center,r.center),r.radius=e.radius,r):new l(e.center,e.radius)},l.packedLength=4,l.pack=function(e,t,r){r=n.defaultValue(r,0);var a=e.center;return t[r++]=a.x,t[r++]=a.y,t[r++]=a.z,t[r]=e.radius,t},l.unpack=function(e,t,r){t=n.defaultValue(t,0),n.defined(r)||(r=new l);var a=r.center;return a.x=e[t++],a.y=e[t++],a.z=e[t++],r.radius=e[t],r};var D=new t.Cartesian3,z=new t.Cartesian3;l.union=function(e,r,a){n.defined(a)||(a=new l);var i=e.center,s=e.radius,o=r.center,u=r.radius,c=t.Cartesian3.subtract(o,i,D),d=t.Cartesian3.magnitude(c);if(s>=d+u)return e.clone(a),a;if(u>=d+s)return r.clone(a),a;var f=.5*(s+d+u),p=t.Cartesian3.multiplyByScalar(c,(-s+f)/d,z);return t.Cartesian3.add(p,i,p),t.Cartesian3.clone(p,a.center),a.radius=f,a};var V=new t.Cartesian3;l.expand=function(e,r,n){n=l.clone(e,n);var a=t.Cartesian3.magnitude(t.Cartesian3.subtract(r,n.center,V));return a>n.radius&&(n.radius=a),n},l.intersectPlane=function(e,r){var n=e.center,a=e.radius,i=r.normal,s=t.Cartesian3.dot(i,n)+r.distance;return s<-a?d.OUTSIDE:s<a?d.INTERSECTING:d.INSIDE},l.transform=function(e,t,r){return n.defined(r)||(r=new l),r.center=s.Matrix4.multiplyByPoint(t,e.center,r.center),r.radius=s.Matrix4.getMaximumScale(t)*e.radius,r};var j=new t.Cartesian3;l.distanceSquaredTo=function(e,r){var n=t.Cartesian3.subtract(e.center,r,j);return t.Cartesian3.magnitudeSquared(n)-e.radius*e.radius},l.transformWithoutScale=function(e,t,r){return n.defined(r)||(r=new l),r.center=s.Matrix4.multiplyByPoint(t,e.center,r.center),r.radius=e.radius,r};var L=new t.Cartesian3;l.computePlaneDistances=function(e,r,a,i){n.defined(i)||(i=new f);var s=t.Cartesian3.subtract(e.center,r,L),o=t.Cartesian3.dot(a,s);return i.start=o-e.radius,i.stop=o+e.radius,i};for(var M=new t.Cartesian3,K=new t.Cartesian3,F=new t.Cartesian3,N=new t.Cartesian3,H=new t.Cartesian3,Y=new t.Cartographic,J=new Array(8),G=0;G<8;++G)J[G]=new t.Cartesian3;var X=new c;
/**
     * @license
     *
     * Grauw URI utilities
     *
     * See: http://hg.grauw.nl/grauw-lib/file/tip/src/uri.js
     *
     * <AUTHOR> Holst (http://www.grauw.nl/)
     *
     *   Copyright 2012 Laurens Holst
     *
     *   Licensed under the Apache License, Version 2.0 (the "License");
     *   you may not use this file except in compliance with the License.
     *   You may obtain a copy of the License at
     *
     *       http://www.apache.org/licenses/LICENSE-2.0
     *
     *   Unless required by applicable law or agreed to in writing, software
     *   distributed under the License is distributed on an "AS IS" BASIS,
     *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     *   See the License for the specific language governing permissions and
     *   limitations under the License.
     *
     */
function Q(e){if(e instanceof Q)this.scheme=e.scheme,this.authority=e.authority,this.path=e.path,this.query=e.query,this.fragment=e.fragment;else if(e){var t=Z.exec(e);this.scheme=t[1],this.authority=t[2],this.path=t[3],this.query=t[4],this.fragment=t[5]}}l.projectTo2D=function(e,r,a){var i,s=(r=n.defaultValue(r,X)).ellipsoid,o=e.center,u=e.radius;i=t.Cartesian3.equals(o,t.Cartesian3.ZERO)?t.Cartesian3.clone(t.Cartesian3.UNIT_X,M):s.geodeticSurfaceNormal(o,M);var c=t.Cartesian3.cross(t.Cartesian3.UNIT_Z,i,K);t.Cartesian3.normalize(c,c);var d=t.Cartesian3.cross(i,c,F);t.Cartesian3.normalize(d,d),t.Cartesian3.multiplyByScalar(i,u,i),t.Cartesian3.multiplyByScalar(d,u,d),t.Cartesian3.multiplyByScalar(c,u,c);var f=t.Cartesian3.negate(d,H),p=t.Cartesian3.negate(c,N),h=J,m=h[0];t.Cartesian3.add(i,d,m),t.Cartesian3.add(m,c,m),m=h[1],t.Cartesian3.add(i,d,m),t.Cartesian3.add(m,p,m),m=h[2],t.Cartesian3.add(i,f,m),t.Cartesian3.add(m,p,m),m=h[3],t.Cartesian3.add(i,f,m),t.Cartesian3.add(m,c,m),t.Cartesian3.negate(i,i),m=h[4],t.Cartesian3.add(i,d,m),t.Cartesian3.add(m,c,m),m=h[5],t.Cartesian3.add(i,d,m),t.Cartesian3.add(m,p,m),m=h[6],t.Cartesian3.add(i,f,m),t.Cartesian3.add(m,p,m),m=h[7],t.Cartesian3.add(i,f,m),t.Cartesian3.add(m,c,m);for(var y=h.length,v=0;v<y;++v){var C=h[v];t.Cartesian3.add(o,C,C);var g=s.cartesianToCartographic(C,Y);r.project(g,C)}var q=(o=(a=l.fromPoints(h,a)).center).x,w=o.y,b=o.z;return o.x=b,o.y=q,o.z=w,a},l.isOccluded=function(e,t){return!t.isBoundingSphereVisible(e)},l.equals=function(e,r){return e===r||n.defined(e)&&n.defined(r)&&t.Cartesian3.equals(e.center,r.center)&&e.radius===r.radius},l.prototype.intersectPlane=function(e){return l.intersectPlane(this,e)},l.prototype.distanceSquaredTo=function(e){return l.distanceSquaredTo(this,e)},l.prototype.computePlaneDistances=function(e,t,r){return l.computePlaneDistances(this,e,t,r)},l.prototype.isOccluded=function(e){return l.isOccluded(this,e)},l.prototype.equals=function(e){return l.equals(this,e)},l.prototype.clone=function(e){return l.clone(this,e)},l.prototype.volume=function(){var e=this.radius;return A*e*e*e},Q.prototype.scheme=null,Q.prototype.authority=null,Q.prototype.path="",Q.prototype.query=null,Q.prototype.fragment=null;var Z=new RegExp("^(?:([^:/?#]+):)?(?://([^/?#]*))?([^?#]*)(?:\\?([^#]*))?(?:#(.*))?$");Q.prototype.getScheme=function(){return this.scheme},Q.prototype.getAuthority=function(){return this.authority},Q.prototype.getPath=function(){return this.path},Q.prototype.getQuery=function(){return this.query},Q.prototype.getFragment=function(){return this.fragment},Q.prototype.isAbsolute=function(){return!!this.scheme&&!this.fragment},Q.prototype.isSameDocumentAs=function(e){return e.scheme==this.scheme&&e.authority==this.authority&&e.path==this.path&&e.query==this.query},Q.prototype.equals=function(e){return this.isSameDocumentAs(e)&&e.fragment==this.fragment},Q.prototype.normalize=function(){this.removeDotSegments(),this.scheme&&(this.scheme=this.scheme.toLowerCase()),this.authority&&(this.authority=this.authority.replace(ee,re).replace(W,te)),this.path&&(this.path=this.path.replace(W,te)),this.query&&(this.query=this.query.replace(W,te)),this.fragment&&(this.fragment=this.fragment.replace(W,te))};var W=/%[0-9a-z]{2}/gi,$=/[a-zA-Z0-9\-\._~]/,ee=/(.*@)?([^@:]*)(:.*)?/;function te(e){var t=unescape(e);return $.test(t)?t:e.toUpperCase()}function re(e,t,r,n){return(t||"")+r.toLowerCase()+(n||"")}function ne(e,t){if(null===e||"object"!=typeof e)return e;t=n.defaultValue(t,!1);var r=new e.constructor;for(var a in e)if(e.hasOwnProperty(a)){var i=e[a];t&&(i=ne(i,t)),r[a]=i}return r}function ae(e,t,r){r=n.defaultValue(r,!1);var a,i,s,o={},u=n.defined(e),c=n.defined(t);if(u)for(a in e)e.hasOwnProperty(a)&&(i=e[a],c&&r&&"object"==typeof i&&t.hasOwnProperty(a)?(s=t[a],o[a]="object"==typeof s?ae(i,s,r):i):o[a]=i);if(c)for(a in t)t.hasOwnProperty(a)&&!o.hasOwnProperty(a)&&(s=t[a],o[a]=s);return o}function ie(e,t){var r;return"undefined"!=typeof document&&(r=document),ie._implementation(e,t,r)}Q.prototype.resolve=function(e){var t=new Q;return this.scheme?(t.scheme=this.scheme,t.authority=this.authority,t.path=this.path,t.query=this.query):(t.scheme=e.scheme,this.authority?(t.authority=this.authority,t.path=this.path,t.query=this.query):(t.authority=e.authority,""==this.path?(t.path=e.path,t.query=this.query||e.query):("/"==this.path.charAt(0)?(t.path=this.path,t.removeDotSegments()):(e.authority&&""==e.path?t.path="/"+this.path:t.path=e.path.substring(0,e.path.lastIndexOf("/")+1)+this.path,t.removeDotSegments()),t.query=this.query))),t.fragment=this.fragment,t},Q.prototype.removeDotSegments=function(){var e,t=this.path.split("/"),r=[],n=""==t[0];for(n&&t.shift(),""==t[0]&&t.shift();t.length;)".."==(e=t.shift())?r.pop():"."!=e&&r.push(e);"."!=e&&".."!=e||r.push(""),n&&r.unshift(""),this.path=r.join("/")},Q.prototype.toString=function(){var e="";return this.scheme&&(e+=this.scheme+":"),this.authority&&(e+="//"+this.authority),e+=this.path,this.query&&(e+="?"+this.query),this.fragment&&(e+="#"+this.fragment),e},ie._implementation=function(e,t,r){if(!n.defined(t)){if(void 0===r)return e;t=n.defaultValue(r.baseURI,r.location.href)}var a=new Q(t);return new Q(e).resolve(a).toString()};var se,oe=/^blob:/i;function ue(e){return oe.test(e)}var ce=/^data:/i;function de(e){return ce.test(e)}var fe=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5}),le=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3,PACK:4,BLOCK:5,BLOCKPACK:6});function pe(e){e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT);var t=n.defaultValue(e.throttleByServer,!1),r=n.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=n.defaultValue(e.priority,0),this.throttle=r,this.throttleByServer=t,this.type=n.defaultValue(e.type,le.OTHER),this.serverKey=void 0,this.state=fe.UNISSUED,this.deferred=void 0,this.cancelled=!1}function he(e,t,r){this.statusCode=e,this.response=t,this.responseHeaders=r,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){var t={};if(!e)return t;for(var r=e.split("\r\n"),n=0;n<r.length;++n){var a=r[n],i=a.indexOf(": ");if(i>0){var s=a.substring(0,i),o=a.substring(i+2);t[s]=o}}return t}(this.responseHeaders))}function me(e){this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function ye(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}pe.prototype.cancel=function(){this.cancelled=!0},pe.prototype.clone=function(e){return n.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=this.RequestState.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new pe(this)},he.prototype.toString=function(){var e="Request has failed.";return n.defined(this.statusCode)&&(e+=" Status Code: "+this.statusCode),e},Object.defineProperties(me.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){this._maximumLength=e,this._length>e&&e>0&&(this._length=e,this._array.length=e)}},comparator:{get:function(){return this._comparator}}}),me.prototype.reserve=function(e){e=n.defaultValue(e,this._length),this._array.length=e},me.prototype.heapify=function(e){e=n.defaultValue(e,0);for(var t=this._length,r=this._comparator,a=this._array,i=-1,s=!0;s;){var o=2*(e+1),u=o-1;i=u<t&&r(a[u],a[e])<0?u:e,o<t&&r(a[o],a[i])<0&&(i=o),i!==e?(ye(a,i,e),e=i):s=!1}},me.prototype.resort=function(){for(var e=this._length,t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},me.prototype.insert=function(e){var t,r=this._array,a=this._comparator,i=this._maximumLength,s=this._length++;for(s<r.length?r[s]=e:r.push(e);0!==s;){var o=Math.floor((s-1)/2);if(!(a(r[s],r[o])<0))break;ye(r,s,o),s=o}return n.defined(i)&&this._length>i&&(t=r[i],r.pop(),this._length=i),t},me.prototype.pop=function(e){if(e=n.defaultValue(e,0),0!==this._length){var t=this._array,r=t[e];return ye(t,e,--this._length),t[this._length]=void 0,this.heapify(e),r}};var ve="undefined"!=typeof performance&&"function"==typeof performance.now&&isFinite(performance.now())?function(){return performance.now()}:function(){return Date.now()};function Ce(e,t){return e.priority-t.priority}var ge={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0,totalRequestTime:0},qe=20,we=new me({comparator:Ce});we.maximumLength=qe,we.reserve(qe);var be=[],Re={},xe="undefined"!=typeof document?new Q(document.location.href):new Q,Ae=new o.Event;function Oe(){}function Ee(e){n.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function Se(e){var t=n.defaultValue(Oe.requestsByServer[e],Oe.maximumRequestsPerServer);return Re[e]<t}function _e(e){return n.defined(e.packKey)||(e.packKey=e.serverKey+"_"+e.providerName),e.packKey}function Ie(e){return n.defined(e.blockKey)||(e.blockKey=e.serverKey+"_"+e.providerName+"_"+e.quadKey+e.url.substring(e.url.indexOf("dataVersion"))),e.blockKey}function Te(e){var t=_e(e);return n.defined(Oe.packRequestGroup[t])||(Oe.packRequestGroup[t]=[]),n.defined(Oe.packRequestQuadKey[t])||(Oe.packRequestQuadKey[t]=""),n.defined(Oe.packRequestPromise[t])||(Oe.packRequestPromise[t]=n.when.defer()),n.defined(Oe.quadKeyIndex[t])||(Oe.quadKeyIndex[t]=0),e.quadKeyIndex=Oe.quadKeyIndex[t]++,e.deferred=Oe.packRequestPromise[t],e.state=fe.ISSUED,Oe.packRequestGroup[t].push(e),e.deferred.promise}function Pe(e){for(var t=0,r=e.length;t<r;t++){e[t].state=fe.CANCELLED}}function ke(e){for(var t=[],r={},n=0,a=e.length;n<a;n++){var i=e[n];if(!i.cancelled){var s=i.quadKey;r[s]||(r[s]=!0,t.push(s))}}return t}function Ue(){var e=Oe.packRequestGroup;for(var t in e)if(e.hasOwnProperty(t)){var r=e[t];if(r.length<1)continue;var a=r[0].clone(),i=-1!==a.url.indexOf("rest/maps");a.serverKey=r[0].serverKey,a.state=r[0].state;var s=a.url,o=ke(r);if(o.length<1)continue;Oe.packRequestQuadKey[t]=i?o.join(","):o.join(";");var u=Oe.packRequestQuadKey[t];if(a.throttleByServer&&!Se(a.serverKey)){Pe(r),Oe.packRequestPromise[t].reject();continue}a.deferred=Oe.packRequestPromise[t];var c=new Q(s);c.query=i?n.defined(c.query)?c.query+"&tiles="+u:"tiles="+u:n.defined(c.query)?c.query+"&extratiles="+u:"extratiles="+u,a.url=c.toString(),ze(a,a.url)}Oe.packRequestGroup={},Oe.packRequestPromise={},Oe.packRequestQuadKey={},Oe.quadKeyIndex={}}function Be(){var e=Oe.blockRequest;for(var t in e){if(e.hasOwnProperty(t))ze(e[t])}Oe.blockRequest={}}function De(e){if(e.state===fe.UNISSUED)if(e.state=fe.ISSUED,e.type===le.PACK||e.type===le.BLOCKPACK){var t=_e(e);n.defined(Oe.packRequestPromise[t])||(Oe.packRequestPromise[t]=n.when.defer()),e.deferred=Oe.packRequestPromise[t]}else e.deferred=n.when.defer();return e.deferred.promise}function ze(e,t){var r=De(e);return e.state=fe.ACTIVE,be.push(e),++ge.numberOfActiveRequests,++ge.numberOfActiveRequestsEver,++Re[e.serverKey],e.startTime=ve(),e.requestFunction(t).then(function(e){return function(t){if(e.state!==fe.CANCELLED&&(--ge.numberOfActiveRequests,--Re[e.serverKey],Ae.raiseEvent(),e.state=fe.RECEIVED,e.deferred.resolve(t),e.endTime=ve(),(Oe.statisticRequestTime>0||e.type!==le.OTHER)&&(ge.totalRequestTime+=e.endTime-e.startTime),e.type===le.BLOCK||e.type===le.BLOCKPACK)){var r=Ie(e);n.defined(Oe.blockDefer[r])&&(Oe.blockDefer[r]=void 0,delete Oe.blockDefer[r])}}}(e)).otherwise(function(e){return function(t){e.state!==fe.CANCELLED&&(++ge.numberOfFailedRequests,--ge.numberOfActiveRequests,--Re[e.serverKey],Ae.raiseEvent(t),e.state=fe.FAILED,e.deferred.reject(t))}}(e)),r}function Ve(e){var t=e.state===fe.ACTIVE;e.state=fe.CANCELLED,++ge.numberOfCancelledRequests,e.deferred.reject(),t&&(--ge.numberOfActiveRequests,--Re[e.serverKey],++ge.numberOfCancelledActiveRequests),n.defined(e.cancelFunction)&&e.cancelFunction()}Oe.TIMEOUT=5e3,Oe.CANCLE_COUNT=3,Oe.statisticRequestTime=-1,Oe.maximumRequests=50,Oe.maximumRequestsPerServer=6,Oe.perPacketCount=20,Oe.requestsByServer={"api.cesium.com:443":18,"assets.cesium.com:443":18},Oe.throttleRequests=!0,Oe.debugShowStatistics=!1,Oe.requestCompletedEvent=Ae,Object.defineProperties(Oe,{activeRequestLength:{get:function(){return be.length}},statistics:{get:function(){return ge}},priorityHeapLength:{get:function(){return qe},set:function(e){if(e<qe)for(;we.length>e;){Ve(we.pop())}qe=e,we.maximumLength=e,we.reserve(e)}}}),Oe.packRequestGroup={},Oe.packRequestPromise={},Oe.packRequestQuadKey={},Oe.quadKeyIndex={},Oe.packRequestHeap={},Oe.blockDefer={},Oe.blockRequest={},Oe.update=function(){var e,t,r=0,n=be.length;for(e=0;e<n;++e)(t=be[e]).cancelled&&Ve(t),t.state===fe.ACTIVE?r>0&&(be[e-r]=t):++r;be.length-=r;var a=we.internalArray,i=we.length;for(e=0;e<i;++e)Ee(a[e]);we.resort(),function(){for(var e in Oe.packRequestHeap)if(Oe.packRequestHeap.hasOwnProperty(e)){for(var t=Oe.packRequestHeap[e],r=t.internalArray,n=t.length,a=0;a<n;++a)Ee(r[a]);t.resort()}}(),Be(),function(){for(var e in Oe.packRequestHeap)if(Oe.packRequestHeap.hasOwnProperty(e))for(var t=Oe.packRequestHeap[e];t.length>0;){var r=t.pop();r.cancelled?Ve(r):Te(r)}Ue()}();for(var s=Math.max(Oe.maximumRequests-be.length,0),o=0;o<s&&we.length>0;)(t=we.pop()).cancelled?Ve(t):!t.throttleByServer||Se(t.serverKey)?(ze(t),++o):Ve(t);!function(){if(!Oe.debugShowStatistics)return;0===ge.numberOfActiveRequests&&ge.lastNumberOfActiveRequests>0&&(ge.numberOfAttemptedRequests>0&&(console.log("Number of attempted requests: "+ge.numberOfAttemptedRequests),ge.numberOfAttemptedRequests=0),ge.numberOfCancelledRequests>0&&(console.log("Number of cancelled requests: "+ge.numberOfCancelledRequests),ge.numberOfCancelledRequests=0),ge.numberOfCancelledActiveRequests>0&&(console.log("Number of cancelled active requests: "+ge.numberOfCancelledActiveRequests),ge.numberOfCancelledActiveRequests=0),ge.numberOfFailedRequests>0&&(console.log("Number of failed requests: "+ge.numberOfFailedRequests),ge.numberOfFailedRequests=0));ge.lastNumberOfActiveRequests=ge.numberOfActiveRequests}()},Oe.getServerKey=function(e){var t=new Q(e).resolve(xe);t.normalize();var r=t.authority;/:/.test(r)||(r=r+":"+("https"===t.scheme?"443":"80"));var a=Re[r];return n.defined(a)||(Re[r]=0),r},Oe.request=function(e){if(de(e.url)||ue(e.url))return Ae.raiseEvent(),e.state=fe.RECEIVED,e.requestFunction();if(++ge.numberOfAttemptedRequests,n.defined(e.serverKey)||(e.serverKey=Oe.getServerKey(e.url)),e.type===le.BLOCK)return function(e){var t=Ie(e),r=Oe.blockDefer[t];return n.defined(r)||(r=Oe.blockDefer[t]=n.when.defer(),Oe.blockRequest[t]=e),e.deferred=r,e.state=fe.ISSUED,e.deferred.promise}(e);if(!e.throttleByServer||Se(e.serverKey)){if(!Oe.throttleRequests||!e.throttle)return ze(e);if(!(be.length>=Oe.maximumRequests)){var t;if(Ee(e),e.type===le.PACK||e.type===le.BLOCKPACK){var r=function(e){var t=_e(e),r=Oe.packRequestHeap[t];return n.defined(r)||((r=Oe.packRequestHeap[t]=new me({comparator:Ce})).maximumLength=Oe.perPacketCount,r.reserve(qe)),r}(e),a=!0;if(e.type===le.BLOCKPACK)for(var i=0;i<r.length;i++)if(r._array[i].quadKey===e.quadKey){e.blockRequest=r._array[i],a=!1;break}a&&(t=r.insert(e))}else t=we.insert(e);if(n.defined(t)){if(t===e)return;Ve(t)}return De(e)}}},Oe.clearForSpecs=function(){for(;we.length>0;){Ve(we.pop())}for(var e=be.length,t=0;t<e;++t)Ve(be[t]);be.length=0,Re={},ge.numberOfAttemptedRequests=0,ge.numberOfActiveRequests=0,ge.numberOfCancelledRequests=0,ge.numberOfCancelledActiveRequests=0,ge.numberOfFailedRequests=0,ge.numberOfActiveRequestsEver=0,ge.lastNumberOfActiveRequests=0,ge.totalRequestTime=0},Oe.numberOfActiveRequestsByServer=function(e){return Re[e]},Oe.requestHeap=we;var je={},Le={};je.add=function(e,t){var r=e.toLowerCase()+":"+t;n.defined(Le[r])||(Le[r]=!0)},je.remove=function(e,t){var r=e.toLowerCase()+":"+t;n.defined(Le[r])&&delete Le[r]},je.contains=function(e){var t=function(e){var t=new Q(e);t.normalize();var r=t.getAuthority();if(n.defined(r)){if(-1!==r.indexOf("@")){var a=r.split("@");r=a[1]}if(-1===r.indexOf(":")){var i=t.getScheme();if(n.defined(i)||(i=(i=window.location.protocol).substring(0,i.length-1)),"http"===i)r+=":80";else{if("https"!==i)return;r+=":443"}}return r}}(e);return!(!n.defined(t)||!n.defined(Le[t]))},je.clear=function(){Le={}};var Me={};function Ke(e,t){n.defined(Me[e])||(Me[e]=!0,console.warn(n.defaultValue(t,e)))}Ke.geometryOutlines="Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.",Ke.geometryZIndex="Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored",Ke.geometryHeightReference="Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored",Ke.geometryExtrudedHeightReference="Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored";var Fe,Ne=function(){try{var e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(e){return!1}}();function He(e,t,r,a){var i,s=e.query;if(!n.defined(s)||0===s.length)return{};if(-1===s.indexOf("=")){var o={};o[s]=void 0,i=o}else i=function(e){var t={};if(""===e)return t;for(var r=e.replace(/\+/g,"%20").split(/[&;]/),a=0,i=r.length;a<i;++a){var s=r[a].split("=");if(s.length>2){var o=r[a].indexOf("=");s=[r[a].substring(0,o),r[a].substring(o+1,r[a].length)]}var u=decodeURIComponent(s[0]),c=s[1];c=n.defined(c)?decodeURIComponent(c):"";var d=t[u];"string"==typeof d?t[u]=[d,c]:Array.isArray(d)?d.push(c):t[u]=c}return t}(s);t._queryParameters=r?Xe(i,t._queryParameters,a):i,e.query=void 0}function Ye(e,t){var r=t._queryParameters,a=Object.keys(r);1!==a.length||n.defined(r[a[0]])?e.query=function(e,t){var r="";for(var n in e)if(e.hasOwnProperty(n)){var a=e[n],i=encodeURIComponent(n)+"=";if(Array.isArray(a))for(var s=0,o=a.length;s<o;++s)r+=!0===t?i+encodeURI(a[s])+"&":i+encodeURIComponent(a[s])+"&";else r+=!0===t?i+encodeURI(a)+"&":i+encodeURIComponent(a)+"&"}return r.slice(0,-1)}(r):e.query=a[0]}function Je(e,t){return n.defined(e)?n.defined(e.clone)?e.clone():ne(e):t}function Ge(e){if(e.state===fe.ISSUED||e.state===fe.ACTIVE)throw new u.RuntimeError("The Resource is already being fetched.");e.state=fe.UNISSUED,e.deferred=void 0}function Xe(e,t,r){if(!r)return ae(e,t);var a=ne(e,!0);for(var i in t)if(t.hasOwnProperty(i)){var s=a[i],o=t[i];n.defined(s)?(Array.isArray(s)||(s=a[i]=[s]),a[i]=s.concat(o)):a[i]=Array.isArray(o)?o.slice():o}return a}function Qe(e){"string"==typeof(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT))&&(e={url:e}),this._url=void 0,this._templateValues=Je(e.templateValues,{}),this._queryParameters=Je(e.queryParameters,{}),this.headers=Je(e.headers,{}),this.request=n.defaultValue(e.request,new pe),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=n.defaultValue(e.retryAttempts,0),this._retryCount=0;var t=new Q(e.url);He(t,this,!0,!0),t.fragment=void 0,this._url=t.toString()}function Ze(e){var t=e.resource,r=e.flipY,a=e.preferImageBitmap,i=t.request;i.url=t.url,i.requestFunction=function(){var e=!1;t.isDataUri||t.isBlobUri||(e=t.isCrossOriginUrl);var s=n.when.defer();return Qe._Implementations.createImage(i,e,s,r,a),s.promise};var s=Oe.request(i);if(n.defined(s))return s.otherwise((function(e){return i.state!==fe.FAILED?n.when.reject(e):t.retryOnError(e).then((function(s){return s?(i.state=fe.UNISSUED,i.deferred=void 0,Ze({resource:t,flipY:r,preferImageBitmap:a})):n.when.reject(e)}))}))}function We(e,t,r){var a={};a[t]=r,e.setQueryParameters(a);var i=e.request;i.url=e.url,i.requestFunction=function(){var t=n.when.defer();return window[r]=function(e){t.resolve(e);try{delete window[r]}catch(e){window[r]=void 0}},Qe._Implementations.loadAndExecuteScript(e.url,r,t),t.promise};var s=Oe.request(i);if(n.defined(s))return s.otherwise((function(a){return i.state!==fe.FAILED?n.when.reject(a):e.retryOnError(a).then((function(s){return s?(i.state=fe.UNISSUED,i.deferred=void 0,We(e,t,r)):n.when.reject(a)}))}))}Qe.createIfNeeded=function(e){return e instanceof Qe?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new Qe({url:e})},Qe.supportsImageBitmapOptions=function(){if(n.defined(Fe))return Fe;if("function"!=typeof createImageBitmap)return Fe=n.when.resolve(!1);return Fe=Qe.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWP4////fwAJ+wP9CNHoHgAAAABJRU5ErkJggg=="}).then((function(e){return createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none"})})).then((function(e){return!0})).otherwise((function(){return!1}))},Object.defineProperties(Qe,{isBlobSupported:{get:function(){return Ne}}}),Object.defineProperties(Qe.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){var t=new Q(e);He(t,this,!1),t.fragment=void 0,this._url=t.toString()}},extension:{get:function(){return function(e){var t=new Q(e);t.normalize();var r=t.path,n=r.lastIndexOf("/");return-1!==n&&(r=r.substr(n+1)),n=r.lastIndexOf("."),-1===n?"":r.substr(n+1)}(this._url)}},isDataUri:{get:function(){return de(this._url)}},isBlobUri:{get:function(){return ue(this._url)}},isCrossOriginUrl:{get:function(){return function(e){n.defined(se)||(se=document.createElement("a")),se.href=window.location.href;var t=se.host,r=se.protocol;return se.href=e,se.href=se.href,r!==se.protocol||t!==se.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}}}),Qe.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;var r=new Q(this._url);e&&Ye(r,this);var a=r.toString().replace(/%7B/g,"{").replace(/%7D/g,"}"),i=this._templateValues;return a=a.replace(/{(.*?)}/g,(function(e,t){var r=i[t];return n.defined(r)?encodeURIComponent(r):e})),t&&n.defined(this.proxy)&&(a=this.proxy.getURL(a)),a},Qe.prototype.setQueryParameters=function(e,t){this._queryParameters=t?Xe(this._queryParameters,e,!1):Xe(e,this._queryParameters,!1)},Qe.prototype.appendQueryParameters=function(e){this._queryParameters=Xe(e,this._queryParameters,!0)},Qe.prototype.setTemplateValues=function(e,t){this._templateValues=t?ae(this._templateValues,e):ae(e,this._templateValues)},Qe.prototype.getDerivedResource=function(e){var t=this.clone();if(t._retryCount=0,n.defined(e.url)){var r=new Q(e.url);He(r,t,!0,n.defaultValue(e.preserveQueryParameters,!1)),r.fragment=void 0,t._url=r.resolve(new Q(ie(this._url))).toString()}return n.defined(e.queryParameters)&&(t._queryParameters=ae(e.queryParameters,t._queryParameters)),n.defined(e.templateValues)&&(t._templateValues=ae(e.templateValues,t.templateValues)),n.defined(e.headers)&&(t.headers=ae(e.headers,t.headers)),n.defined(e.proxy)&&(t.proxy=e.proxy),n.defined(e.request)&&(t.request=e.request),n.defined(e.retryCallback)&&(t.retryCallback=e.retryCallback),n.defined(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},Qe.prototype.retryOnError=function(e){var t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return n.when(!1);var r=this;return n.when(t(this,e)).then((function(e){return++r._retryCount,e}))},Qe.prototype.clone=function(e){return n.defined(e)||(e=new Qe({url:this._url})),e._url=this._url,e._queryParameters=ne(this._queryParameters),e._templateValues=ne(this._templateValues),e.headers=ne(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e},Qe.prototype.getBaseUri=function(e){return function(e,t){var r="",a=e.lastIndexOf("/");return-1!==a&&(r=e.substring(0,a+1)),t?(e=new Q(e),n.defined(e.query)&&(r+="?"+e.query),n.defined(e.fragment)&&(r+="#"+e.fragment),r):r}(this.getUrlComponent(e),e)},Qe.prototype.appendForwardSlash=function(){var e;this._url=(0!==(e=this._url).length&&"/"===e[e.length-1]||(e+="/"),e)},Qe.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},Qe.fetchArrayBuffer=function(e){return new Qe(e).fetchArrayBuffer()},Qe.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},Qe.fetchBlob=function(e){return new Qe(e).fetchBlob()},Qe.prototype.fetchImage=function(e){e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT);var t=n.defaultValue(e.preferImageBitmap,!1),r=n.defaultValue(e.preferBlob,!1),a=n.defaultValue(e.flipY,!1);if(Ge(this.request),!Ne||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!r)return Ze({resource:this,flipY:a,preferImageBitmap:t});var i,s,o,u=this.fetchBlob();return n.defined(u)?Qe.supportsImageBitmapOptions().then((function(e){return i=e&&t,u})).then((function(e){if(n.defined(e)){if(o=e,i)return Qe.createImageBitmapFromBlob(e,{flipY:a,premultiplyAlpha:!1});var t=window.URL.createObjectURL(e);return Ze({resource:s=new Qe({url:t}),flipY:a,preferImageBitmap:!1})}})).then((function(e){if(n.defined(e))return e.blob=o,i||window.URL.revokeObjectURL(s.url),e})).otherwise((function(e){return n.defined(s)&&window.URL.revokeObjectURL(s.url),e.blob=o,n.when.reject(e)})):void 0},Qe.fetchImage=function(e){return new Qe(e).fetchImage({flipY:e.flipY,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},Qe.prototype.fetchText=function(){return this.fetch({responseType:"text"})},Qe.fetchText=function(e){return new Qe(e).fetchText()},Qe.prototype.fetchJson=function(){var e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(n.defined(e))return e.then((function(e){if(n.defined(e))return JSON.parse(e)}))},Qe.fetchJson=function(e){return new Qe(e).fetchJson()},Qe.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},Qe.fetchXML=function(e){return new Qe(e).fetchXML()},Qe.prototype.fetchJsonp=function(e){var t;e=n.defaultValue(e,"callback"),Ge(this.request);do{t="loadJsonp"+Math.random().toString().substring(2,8)}while(n.defined(window[t]));return We(this,e,t)},Qe.fetchJsonp=function(e){return new Qe(e).fetchJsonp(e.callbackParameterName)},Qe.prototype._makeRequest=function(e){var t=this;Ge(t.request);var r=t.request;r.url=t.url,r.requestFunction=function(a){var i=e.responseType,s=ae(e.headers,t.headers),o=e.overrideMimeType,u=e.method,c=e.data,d=n.when.defer(),f=n.defined(a)?a:t.url,l=Qe._Implementations.loadWithXhr(f,i,u,c,s,d,o);return n.defined(l)&&n.defined(l.abort)&&(r.cancelFunction=function(){l.abort()}),d.promise};var a=Oe.request(r);if(n.defined(a))return a.then((function(e){return e})).otherwise((function(a){return r.state!==fe.FAILED?n.when.reject(a):t.retryOnError(a).then((function(i){return i?(r.state=fe.UNISSUED,r.deferred=void 0,t.fetch(e)):n.when.reject(a)}))}))};var $e=/^data:(.*?)(;base64)?,(.*)$/;function et(e,t){var r=decodeURIComponent(t);return e?atob(r):r}function tt(e,t){for(var r=et(e,t),n=new ArrayBuffer(r.length),a=new Uint8Array(n),i=0;i<r.length;i++)a[i]=r.charCodeAt(i);return n}function rt(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}Qe.prototype.fetch=function(e){return(e=Je(e,{})).method="GET",this._makeRequest(e)},Qe.fetch=function(e){return new Qe(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe.prototype.delete=function(e){return(e=Je(e,{})).method="DELETE",this._makeRequest(e)},Qe.delete=function(e){return new Qe(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},Qe.prototype.head=function(e){return(e=Je(e,{})).method="HEAD",this._makeRequest(e)},Qe.head=function(e){return new Qe(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe.prototype.options=function(e){return(e=Je(e,{})).method="OPTIONS",this._makeRequest(e)},Qe.options=function(e){return new Qe(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe.prototype.post=function(e,t){return r.Check.defined("data",e),(t=Je(t,{})).method="POST",t.data=e,this._makeRequest(t)},Qe.post=function(e){return new Qe(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe.prototype.put=function(e,t){return r.Check.defined("data",e),(t=Je(t,{})).method="PUT",t.data=e,this._makeRequest(t)},Qe.put=function(e){return new Qe(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe.prototype.patch=function(e,t){return r.Check.defined("data",e),(t=Je(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},Qe.patch=function(e){return new Qe(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},Qe._Implementations={},Qe._Implementations.createImage=function(e,t,r,a,i){var s=e.url;Qe.supportsImageBitmapOptions().then((function(o){if(o&&i){var c=n.when.defer(),d=Qe._Implementations.loadWithXhr(s,"blob","GET",void 0,void 0,c,void 0,void 0,void 0);return n.defined(d)&&n.defined(d.abort)&&(e.cancelFunction=function(){d.abort()}),c.promise.then((function(e){if(n.defined(e))return Qe.createImageBitmapFromBlob(e,{flipY:a,premultiplyAlpha:!1});r.reject(new u.RuntimeError("Successfully retrieved "+s+" but it contained no content."))})).then(r.resolve)}!function(e,t,r){var n=new Image;n.onload=function(){r.resolve(n)},n.onerror=function(e){r.reject(e)},t&&(je.contains(e)?n.crossOrigin="use-credentials":n.crossOrigin=""),n.src=e}(s,t,r)})).otherwise(r.reject)},Qe.createImageBitmapFromBlob=function(e,t){return r.Check.defined("options",t),r.Check.typeOf.bool("options.flipY",t.flipY),r.Check.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none"})};var nt="undefined"==typeof XMLHttpRequest;Qe._Implementations.loadWithXhr=function(e,t,r,a,i,s,o){var c=$e.exec(e);if(null===c){if(!nt){var d=new XMLHttpRequest;if(je.contains(e)&&(d.withCredentials=!0),e=e.replace(/{/g,"%7B").replace(/}/g,"%7D"),d.open(r,e,!0),n.defined(o)&&n.defined(d.overrideMimeType)&&d.overrideMimeType(o),n.defined(i))for(var f in i)i.hasOwnProperty(f)&&d.setRequestHeader(f,i[f]);n.defined(t)&&(d.responseType=t);var l=!1;return"string"==typeof e&&(l=0===e.indexOf("file://")||"undefined"!=typeof window&&"file://"===window.location.origin),d.onload=function(){if(!(d.status<200||d.status>=300)||l&&0===d.status){var e=d.response,a=d.responseType;if("HEAD"===r||"OPTIONS"===r){var i=d.getAllResponseHeaders().trim().split(/[\r\n]+/),o={};return i.forEach((function(e){var t=e.split(": "),r=t.shift();o[r]=t.join(": ")})),void s.resolve(o)}if(204===d.status)s.resolve();else if(!n.defined(e)||n.defined(t)&&a!==t)if("json"===t&&"string"==typeof e)try{s.resolve(JSON.parse(e))}catch(e){s.reject(e)}else(""===a||"document"===a)&&n.defined(d.responseXML)&&d.responseXML.hasChildNodes()?s.resolve(d.responseXML):""!==a&&"text"!==a||!n.defined(d.responseText)?s.reject(new u.RuntimeError("Invalid XMLHttpRequest response type.")):s.resolve(d.responseText);else s.resolve(e)}else s.reject(new he(d.status,d.response,d.getAllResponseHeaders()))},d.onerror=function(e){s.reject(new he)},d.send(a),d}!function(e,t,r,n,a,i,s){var o=require("url").parse(e),c="https:"===o.protocol?require("https"):require("http"),d=require("zlib"),f={protocol:o.protocol,hostname:o.hostname,port:o.port,path:o.path,query:o.query,method:r,headers:a};c.request(f).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)i.reject(new he(e.statusCode,e,e.headers));else{var r=[];e.on("data",(function(e){r.push(e)})),e.on("end",(function(){var n=Buffer.concat(r);"gzip"===e.headers["content-encoding"]?d.gunzip(n,(function(e,r){e?i.reject(new u.RuntimeError("Error decompressing response.")):i.resolve(rt(r,t))})):i.resolve(rt(n,t))}))}})).on("error",(function(e){i.reject(new he)})).end()}(e,t,r,0,i,s)}else s.resolve(function(e,t){t=n.defaultValue(t,"");var r=e[1],a=!!e[2],i=e[3];switch(t){case"":case"text":return et(a,i);case"arraybuffer":return tt(a,i);case"blob":var s=tt(a,i);return new Blob([s],{type:r});case"document":return(new DOMParser).parseFromString(et(a,i),r);case"json":return JSON.parse(et(a,i))}}(c,t))},Qe._Implementations.loadAndExecuteScript=function(e,t,r){return function(e){var t=n.when.defer(),r=document.createElement("script");r.async=!0,r.src=e;var a=document.getElementsByTagName("head")[0];return r.onload=function(){r.onload=void 0,a.removeChild(r),t.resolve()},r.onerror=function(e){t.reject(e)},a.appendChild(r),t.promise}(e).otherwise(r.reject)},Qe._DefaultImplementations={},Qe._DefaultImplementations.createImage=Qe._Implementations.createImage,Qe._DefaultImplementations.loadWithXhr=Qe._Implementations.loadWithXhr,Qe._DefaultImplementations.loadAndExecuteScript=Qe._Implementations.loadAndExecuteScript,Qe.DEFAULT=Object.freeze(new Qe({url:"undefined"==typeof document?"":document.location.href.split("?")[0]}));var at,it,st,ot=/((?:.*\/)|^)Cesium\.js$/;function ut(e){return"undefined"==typeof document?e:(n.defined(at)||(at=document.createElement("a")),at.href=e,at.href=at.href,at.href)}function ct(){return n.defined(it)||(e="undefined"!=typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"==typeof define&&n.defined(define.amd)&&!define.amd.toUrlUndefined&&n.defined(require.toUrl)?ie("..",lt("Core/buildModuleUrl.js")):function(){for(var e=document.getElementsByTagName("script"),t=0,r=e.length;t<r;++t){var n=e[t].getAttribute("src"),a=ot.exec(n);if(null!==a)return a[1]}}(),(it=new Qe({url:ut(e)})).appendForwardSlash()),it;var e}function dt(e){return ut(require.toUrl("../"+e))}function ft(e){return ct().getDerivedResource({url:e}).url}function lt(e){return n.defined(st)||(st="object"==typeof define&&n.defined(define.amd)&&!define.amd.toUrlUndefined&&n.defined(require.toUrl)?dt:ft),st(e)}lt._cesiumScriptRegex=ot,lt._buildModuleUrlFromBaseUrl=ft,lt._clearBaseResource=function(){it=void 0},lt.setBaseUrl=function(e){it=Qe.DEFAULT.getDerivedResource({url:e})},lt.getCesiumBaseUrl=ct,e.BoundingSphere=l,e.GeographicProjection=c,e.Intersect=d,e.Interval=f,e.Resource=Qe,e.buildModuleUrl=lt,e.oneTimeWarning=Ke}));
