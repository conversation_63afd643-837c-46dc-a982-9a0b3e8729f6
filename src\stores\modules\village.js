import { defineStore } from "pinia";
import { ref } from "vue";
import { useMapStore } from "./map";
import { getJiangtuanMapData, getVillageData } from "../../api";

export const useVillageStore = defineStore("village", () => {
  const pageData = ref({});
  const mapStore = useMapStore();
  async function getData(query) {
    try {
      mapStore.$reset();

      const params = {
        ...query,
      };
      const mapParams = {
        ...query,
      };
      const results = await Promise.allSettled([getVillageData(params), getJiangtuanMapData(mapParams)]);

      if (results[0].status === "fulfilled") {
        pageData.value = results[0].value;
      }

      if (results[1].status === "fulfilled") {
        const mapData = results[1].value;
        if (mapData.mapView) {
          mapStore.setMapView(mapData.mapView);
        } else {
          mapStore.setDefaultView();
        }
        if (mapData.markData) {
          mapStore.setMarkData(mapData.markData);
        }
        if (mapData.polygonData) {
          mapStore.setPolygonData(mapData.polygonData);
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  return { pageData, getData };
});
