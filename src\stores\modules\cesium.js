import { defineStore } from "pinia";
import { useMapStore } from "./map";
import { useGeoJsonStore } from "./geoJson";
import { getMapData } from "../../api";

export const useCesiumStore = defineStore("Cesium", () => {
  const mapStore = useMapStore();
  // const geoJsonStore = useGeoJsonStore();
  // async function getData(query) {
  //   await geoJsonStore.getData(query);
  // }
  async function getData(query) {
    try {
      const mapParams = {
        ...query,
      };
      const mapData = await getMapData(mapParams);
      if (mapData.placeList) {
        mapStore.setPlaceList(mapData.placeList);
      }
      if (mapData.mapView) {
        mapStore.setMapView(mapData.mapView);
      } else {
        mapStore.setDefaultView();
      }
      if (mapData.markData) {
        mapStore.setMarkData(mapData.markData);
      }
      if (mapData.polygonData) {
        mapStore.setPolygonData(mapData.polygonData);
      }
      if (mapData.buildingData) {
        mapStore.setBuildingData(mapData.buildingData);
      }
      if (mapData.integrationData) {
        mapStore.setIntegrationData(mapData.integrationData);
      }
    } catch (error) {
      console.log(error);
    }
  }

  return { getData };
});
