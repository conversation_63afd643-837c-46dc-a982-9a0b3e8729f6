define(["exports","./S3MPixelFormat-4f2b7689","./when-b60132fc","./Cartographic-3309dd0d","./Check-7b2a090c","./IndexDatatype-8a5eead4","./WebGLConstants-4ae0db90","./FeatureDetection-806b12f0","./ComponentDatatype-c140a87d"],(function(e,t,n,r,a,i,s,o,f){"use strict";
//! Use DXT1 compression.
function u(e,t,n,r){var a=e|t<<8,i=a>>11&31,s=a>>5&63,o=31&a;return n[r+0]=i<<3|i>>2,n[r+1]=s<<2|s>>4,n[r+2]=o<<3|o>>2,n[r+3]=255,a}function c(e,t,n,r){var a=0;0!=(6&r)&&(a=8),function(e,t,n,r){for(var a=new Uint8Array(16),i=u(t[n+0],t[n+1],a,0),s=u(t[n+2],t[n+3],a,4),o=0;o<3;o++){var f=a[o],c=a[4+o];r&&i<=s?(a[8+o]=(f+c)/2,a[12+o]=0):(a[8+o]=(2*f+c)/3,a[12+o]=(f+2*c)/3)}a[11]=255,a[15]=r&&i<=s?0:255;var y=new Uint8Array(16);for(o=0;o<4;++o){var p=t[n+4+o];y[4*o+0]=3&p,y[4*o+1]=p>>2&3,y[4*o+2]=p>>4&3,y[4*o+3]=p>>6&3}for(o=0;o<16;++o)for(var d=4*y[o],l=0;l<4;++l)e[4*o+l]=a[d+l]}(e,t,n+a,0!=(1&r)),0!=(2&r)?function(e,t,n){for(var r=0;r<8;++r){var a=bytes[n+r],i=15&a,s=240&a;e[8*r+3]=i|i<<4,e[8*r+7]=s|s>>4}}(e,0,n):0!=(4&r)&&function(e,t,n){var r=t[n+0],a=t[n+1],i=new Uint8Array(8);if(i[0]=r,i[1]=a,r<=a){for(var s=1;s<5;++s)i[1+s]=((5-s)*r+s*a)/5;i[6]=0,i[7]=255}else for(s=1;s<7;++s)i[1+s]=((7-s)*r+s*a)/7;var o=new Uint8Array(16),f=(n+=2,0);for(s=0;s<2;++s){for(var u=0,c=0;c<3;++c)u|=t[n++]<<8*c;for(c=0;c<8;++c){var y=u>>3*c&7;o[f++]=y}}for(s=0;s<16;++s)e[4*s+3]=i[o[s]]}(e,t,n)}function y(e){}y.decode=function(e,n,r,a,i){if(null!=e&&null!=a&&0!=r&&0!=n){var s=0;1&(s=i>t.S3MPixelFormat.BGR||i===t.S3MPixelFormat.LUMINANCE_ALPHA?4:33)&&32&s?function(e,t,n,r){for(var a=new Uint16Array(4),i=e,s=0,o=0,f=0,u=0,c=0,y=0,p=0,d=0,l=0,A=t/4,v=n/4,h=0;h<v;h++)for(var x=0;x<A;x++)f=4*((v-h)*A+x),a[0]=r[f],a[1]=r[f+1],u=31&a[0],c=2016&a[0],y=63488&a[0],p=31&a[1],d=2016&a[1],l=63488&a[1],a[2]=5*u+3*p>>3|5*c+3*d>>3&2016|5*y+3*l>>3&63488,a[3]=5*p+3*u>>3|5*d+3*c>>3&2016|5*l+3*y>>3&63488,s=r[f+2],i[o=4*h*t+4*x]=a[3&s],i[o+1]=a[s>>2&3],i[o+2]=a[s>>4&3],i[o+3]=a[s>>6&3],i[o+=t]=a[s>>8&3],i[o+1]=a[s>>10&3],i[o+2]=a[s>>12&3],i[o+3]=a[s>>14],s=r[f+3],i[o+=t]=a[3&s],i[o+1]=a[s>>2&3],i[o+2]=a[s>>4&3],i[o+3]=a[s>>6&3],i[o+=t]=a[s>>8&3],i[o+1]=a[s>>10&3],i[o+2]=a[s>>12&3],i[o+3]=a[s>>14]}
/*! @brief Decompresses an image in memory.

     @param rgba		Storage for the decompressed pixels.
     @param width	The width of the source image.
     @param height	The height of the source image.
     @param blocks	The compressed DXT blocks.
     @param flags	Compression flags.

     The decompressed pixels will be written as a contiguous array of width*height
     16 rgba values, with each component as 1 byte each. In memory this is:

     { r1, g1, b1, a1, .... , rn, gn, bn, an } for n = width*height

     The flags parameter should specify either kDxt1, kDxt3 or kDxt5 compression,
     however, DXT1 will be used by default if none is specified. All other flags
     are ignored.

     Internally this function calls squish::Decompress for each block.
     */(e,n,r,a):function(e,t,n,r,a){for(var i=0!=(1&a)?8:16,s=0,o=0;o<n;o+=4)for(var f=0;f<t;f+=4){var u=new Uint8Array(64);c(u,r,s,a);for(var y=0,p=0;p<4;++p)for(var d=0;d<4;++d){var l=f+d,A=o+p;if(l<t&&A<n)for(var v=4*(t*(n-A)+l),h=0;h<4;++h)e[v++]=u[y++];else y+=4}s+=i}}(e,n,r,a,s)}};var p=Object.freeze({SVC_Vertex:1,SVC_Normal:2,SVC_VertexColor:4,SVC_SecondColor:8,SVC_TexutreCoord:16,SVC_TexutreCoordIsW:32});function d(){return!0}var l={STREAM_DRAW:s.WebGLConstants.STREAM_DRAW,STATIC_DRAW:s.WebGLConstants.STATIC_DRAW,DYNAMIC_DRAW:s.WebGLConstants.DYNAMIC_DRAW,validate:function(e){return e===l.STREAM_DRAW||e===l.STATIC_DRAW||e===l.DYNAMIC_DRAW}};function A(e){var t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).context._gl,r=e.bufferTarget,a=e.typedArray,i=e.sizeInBytes,s=e.usage,o=n.defined(a);o&&(i=a.byteLength);var f=t.createBuffer();t.bindBuffer(r,f),t.bufferData(r,o?a:i,s),t.bindBuffer(r,null),this._gl=t,this._webgl2=e.context._webgl2,this._bufferTarget=r,this._sizeInBytes=i,this._usage=s,this._buffer=f,this.vertexArrayDestroyable=!0,this.context=e.context,e.context.memorySize+=i}function v(){}A.createVertexBuffer=function(e){return new A({context:e.context,bufferTarget:s.WebGLConstants.ARRAY_BUFFER,typedArray:e.typedArray,sizeInBytes:e.sizeInBytes,usage:e.usage})},A.createIndexBuffer=function(e){var t=e.context,n=e.indexDatatype,r=i.IndexDatatype.getSizeInBytes(n),a=new A({context:t,bufferTarget:s.WebGLConstants.ELEMENT_ARRAY_BUFFER,typedArray:e.typedArray,sizeInBytes:e.sizeInBytes,usage:e.usage}),o=a.sizeInBytes/r;return Object.defineProperties(a,{indexDatatype:{get:function(){return n}},bytesPerIndex:{get:function(){return r}},numberOfIndices:{get:function(){return o}}}),a},Object.defineProperties(A.prototype,{sizeInBytes:{get:function(){return this._sizeInBytes}},usage:{get:function(){return this._usage}}}),A.prototype._getBuffer=function(){return this._buffer},A.prototype.copyFromArrayView=function(e,t){t=n.defaultValue(t,0);var r=this._gl,a=this._bufferTarget;r.bindBuffer(a,this._buffer),r.bufferSubData(a,t,e),r.bindBuffer(a,null)},A.prototype.copyFromBuffer=function(e,t,n,r){var a=s.WebGLConstants.COPY_READ_BUFFER,i=s.WebGLConstants.COPY_WRITE_BUFFER,o=this._gl;o.bindBuffer(i,this._buffer),o.bindBuffer(a,e._buffer),o.copyBufferSubData(a,i,t,n,r),o.bindBuffer(i,null),o.bindBuffer(a,null)},A.prototype.getBufferData=function(e,t,r,a){t=n.defaultValue(t,0),r=n.defaultValue(r,0);var i=this._gl,o=s.WebGLConstants.COPY_READ_BUFFER;i.bindBuffer(o,this._buffer),i.getBufferSubData(o,t,e,r,a),i.bindBuffer(o,null)},A.prototype.isDestroyed=function(){return!1},A.prototype.destroy=function(){return this._gl.deleteBuffer(this._buffer),this.context.memorySize-=this.sizeInBytes,function(e,t){function n(){}for(var r in e)"function"==typeof e[r]&&(e[r]=n);e.isDestroyed=d}(this)},v.computeNeighbors=function(e,t){for(var n=e.length/3,r=new Uint32Array(t+1),a=new Uint32Array(t+1),i=function(e,t){e<t?r[e+1]++:a[t+1]++},s=0;s<n;s++){var o=e[3*s],f=e[3*s+1],u=e[3*s+2];i(o,f),i(f,u),i(u,o)}for(s=f=o=0;s<t;s++)u=r[s+1],i=a[s+1],r[s+1]=o,a[s+1]=f,o+=u,f+=i;var c=new Uint32Array(6*n),y=r[t];for(i=function(e,t,n){if(e<t){var i=r[e+1]++;c[2*i]=t,c[2*i+1]=n}else i=a[t+1]++,c[2*y+2*i]=e,c[2*y+2*i+1]=n},s=0;s<n;s++)o=e[3*s],f=e[3*s+1],u=e[3*s+2],i(o,f,s),i(f,u,s),i(u,o,s);for(o=function(e,t){var n=2*e;for(e=t-e,t=1;t<e;t++){for(var r=c[n+2*t],a=c[n+2*t+1],i=t-1;0<=i&&c[n+2*i]>r;i--)c[n+2*i+2]=c[n+2*i],c[n+2*i+3]=c[n+2*i+1];c[n+2*i+2]=r,c[n+2*i+3]=a}},s=0;s<t;s++)o(r[s],r[s+1]),o(y+a[s],y+a[s+1]);var p=new Int32Array(3*n),d=function(t,n){return t===e[3*n]?0:t===e[3*n+1]?1:t===e[3*n+2]?2:-1};for(n=function(e,t){e=d(e,t),p[3*t+e]=-1},o=function(e,t,n,r){e=d(e,t),p[3*t+e]=r,n=d(n,r),p[3*r+n]=t},s=0;s<t;s++){f=r[s],u=r[s+1],i=a[s];for(var l=a[s+1];f<u&&i<l;){var A=c[2*f],v=c[2*y+2*i];A===v?(o(s,c[2*f+1],v,c[2*y+2*i+1]),f++,i++):A<v?(n(s,c[2*f+1]),f++):(n(v,c[2*y+2*i+1]),i++)}for(;f<u;)n(s,c[2*f+1]),f++;for(;i<l;)n(v=c[2*y+2*i],c[2*y+2*i+1]),i++}return p};var h=null;function x(){}function b(e){return e*Math.PI/180}v.deduplicate=function(e,t,n,r,a){void 0===n&&(n=0),void 0===r&&(r=0),void 0===a&&(a=e.byteLength/(4*t)),e=new Uint32Array(e,r,a*t),r=new Uint32Array(a);var i=Math.floor(1.1*a)+1;(null==h||h.length<2*i)&&(h=new Uint32Array(function(e){--e;for(var t=1;32>t;t<<=1)e|=e>>t;return e+1}(2*i)));for(var s=0;s<2*i;s++)h[s]=0;var o=0,f=0!==n?Math.ceil(7.84*1.96/(n*n)*n*(1-n)):a;for(s=0;s<a;s++){if(s===f){if((c=1-o/s)******Math.sqrt(c*(1-c)/s)<n)return null;f*=2}for(var u,c=s*t,y=u=0;y<t;y++)u=(u=e[c+y]+u|0)+(u<<11)+(u>>>2)|0;y=(u>>>=0)%i;for(var p=o;0!==h[2*y+1];){if(h[2*y]===u){var d=h[2*y+1]-1,l=d*t;e:{for(var A=0;A<t;A++)if(e[c+A]!==e[l+A]){l=!1;break e}l=!0}if(l){p=r[d];break}}++y>=i&&(y-=i)}p===o&&(h[2*y]=u,h[2*y+1]=s+1,o++),r[s]=p}if(0!==n&&1-o/a<n)return null;for(n=new Uint32Array(t*o),s=o=0;s<a;s++)if(r[s]===o){for(i=e,f=s*t,c=n,u=o*t,y=t,p=0;p<y;p++)c[u+p]=i[f+p];o++}return{buffer:n.buffer,indices:r,uniqueCount:o}};var C=b(4),m=b(35),D=Math.cos(m),g=Math.cos(C);var B={position0:new r.Cartesian3,position1:new r.Cartesian3,faceNormal0:new r.Cartesian3,faceNormal1:new r.Cartesian3,cosAngle:0},I=new r.Cartesian3,z=new r.Cartesian3;function T(e,t){var n,a=(n=e.cosAngle,Math.acos(1<n?1:-1>n?-1:n));return function(e,t,n){var r=n.x-t.x,a=n.y-t.y;(n=r*r+a*a+(t=n.z-t.z)*t)?(n=1/Math.sqrt(n),e.x=r*n,e.y=a*n,e.z=t*n):(e.x=0,e.y=0,e.z=0)}(z,e.position1,e.position0),r.Cartesian3.cross(e.faceNormal0,e.faceNormal1,I),a*(0<r.Cartesian3.dot(I,z)?-1:1)>t}var S=new r.Cartesian3,_=new r.Cartesian3,w=new r.Cartesian3;function V(e){var t=e.x*e.x+e.y*e.y+e.z*e.z;t>0&&(t=1/Math.sqrt(t),e.x*=t,e.y*=t,e.z*=t)}function P(e){}function F(e){if(n.defined(e.cachedSidenessVertexBuffer))return e.cachedSidenessVertexBuffer;var t=new Float32Array(8),r=0;return t[r++]=0,t[r++]=0,t[r++]=0,t[r++]=1,t[r++]=1,t[r++]=1,t[r++]=1,t[r++]=0,e.cachedSidenessVertexBuffer=A.createVertexBuffer({context:e,typedArray:t,usage:l.STATIC_DRAW}),e.cachedSidenessVertexBuffer.vertexArrayDestroyable=!1,e.cachedSidenessVertexBuffer}function L(e,t){for(var n,a,i,s=t.componentsPerAttribute,o=e.vertCompressConstant,f=new r.Cartesian3(e.minVerticesValue.x,e.minVerticesValue.y,e.minVerticesValue.z),u=new Uint16Array(t.typedArray.buffer,t.typedArray.byteOffset,t.typedArray.byteLength/2),c=new Float32Array(3*e.verticesCount),y=0;y<e.verticesCount;y++)n=u[s*y]*o+f.x,a=u[s*y+1]*o+f.y,i=u[s*y+2]*o+f.z,c[3*y]=n,c[3*y+1]=a,c[3*y+2]=i;return c}x.extractEdges=function(e){var t=e.vertices,n=e.dim,a=B,i=a.position0,s=a.position1,o=a.faceNormal0,f=a.faceNormal1,u=function(e){for(var t=e.faces.length/3,n=e.vertices,a=e.dim,i=e.faces,s=new Float32Array(3*t),o=0;o<t;o++){var f=i[3*o+0],u=i[3*o+1],c=i[3*o+2];S.x=n[a*f],S.y=n[a*f+1],S.z=n[a*f+2],_.x=n[a*u],_.y=n[a*u+1],_.z=n[a*u+2],w.x=n[a*c],w.y=n[a*c+1],w.z=n[a*c+2],r.Cartesian3.subtract(_,S,_),r.Cartesian3.subtract(w,S,w),r.Cartesian3.cross(_,w,S),V(S),s[3*o+0]=S.x,s[3*o+1]=S.y,s[3*o+2]=S.z}return s}(e),c=function(e){var t=e.faces.length/3,n=e.faces,r=e.neighbors,a=0,i=0;for(i=0;i<t;i++){var s=r[3*i+0],o=r[3*i+1],f=r[3*i+2],u=n[3*i+0],c=n[3*i+1],y=n[3*i+2];a+=-1===s||u<c?1:0,a+=-1===o||c<y?1:0,a+=-1===f||y<u?1:0}var p=new Int32Array(4*a),d=0;for(i=0;i<t;i++)s=r[3*i+0],o=r[3*i+1],f=r[3*i+2],u=n[3*i+0],c=n[3*i+1],y=n[3*i+2],(-1===s||u<c)&&(p[d++]=u,p[d++]=c,p[d++]=i,p[d++]=s),(-1===o||c<y)&&(p[d++]=c,p[d++]=y,p[d++]=i,p[d++]=o),(-1===f||y<u)&&(p[d++]=y,p[d++]=u,p[d++]=i,p[d++]=f);return p}(e),y=c.length/4,p=new Float32Array(9*y),d=0,l=new Float32Array(12*y),A=0,v=0,h=0,x=function(e,t){0===t&&(t=e,e=0);for(var n=Array(t-e),r=e;r<t;r++)n[r-e]=r;return n}(0,y),b=new Float32Array(y);b.forEach((function(e,a,o){var f=c[4*a+0],u=c[4*a+1];i.x=t[f*n],i.y=t[f*n+1],i.z=t[f*n+2],s.x=t[u*n],s.y=t[u*n+1],s.z=t[u*n+2],o[a]=r.Cartesian3.distance(i,s)})),x.sort((function(e,t){return b[t]-b[e]}));for(var m=[],I=[],z=0;z<y;z++){var P=x[z],F=b[P],L=c[4*P+0],O=c[4*P+1],R=c[4*P+2],E=c[4*P+3],N=-1===E;if(i.x=t[L*n],i.y=t[L*n+1],i.z=t[L*n+2],s.x=t[O*n],s.y=t[O*n+1],s.z=t[O*n+2],N)o.x=u[3*R],o.y=u[3*R+1],o.z=u[3*R+2],f.x=o.x,f.y=o.y,f.z=o.z,a.cosAngle=r.Cartesian3.dot(o,f);else if(o.x=u[3*R],o.y=u[3*R+1],o.z=u[3*R+2],f.x=u[3*E],f.y=u[3*E+1],f.z=u[3*E+2],a.cosAngle=r.Cartesian3.dot(o,f),a.cosAngle>g)continue;v+=F,h++,N||a.cosAngle<D?(p[d++]=a.position0.x,p[d++]=a.position0.y,p[d++]=a.position0.z,p[d++]=a.position1.x,p[d++]=a.position1.y,p[d++]=a.position1.z,p[d++]=a.faceNormal0.x,p[d++]=a.faceNormal0.y,p[d++]=a.faceNormal0.z,m.push(F)):T(a,C)&&(l[A++]=a.position0.x,l[A++]=a.position0.y,l[A++]=a.position0.z,l[A++]=a.position1.x,l[A++]=a.position1.y,l[A++]=a.position1.z,l[A++]=a.faceNormal0.x,l[A++]=a.faceNormal0.y,l[A++]=a.faceNormal0.z,l[A++]=a.faceNormal1.x,l[A++]=a.faceNormal1.y,l[A++]=a.faceNormal1.z,I.push(F))}p=p.slice(0,d),l=l.slice(0,A);var M=v/h,U=m.length,W=I.length;return{regular:{instancesData:p,instanceCount:U,edgeLength:U*M},silhouette:{instancesData:l,instanceCount:W,edgeLength:W*M},averageEdgeLength:M}},P.RegularInstanceStride=12,P.SilhouetteInstanceStride=15,P.createEdgeData=function(e,t,r){if(0==t.length)return null;var a,i=t[0];a=0===i.indexType?new Uint16Array(i.indicesTypedArray.buffer,i.indicesTypedArray.byteOffset,i.indicesTypedArray.byteLength/2):new Uint32Array(i.indicesTypedArray.buffer,i.indicesTypedArray.byteOffset,i.indicesTypedArray.byteLength/4);var s=P.extractEdgeInformation(e,!1,a),o=x.extractEdges(s);return n.defined(r)&&(n.defined(o.regular.instancesData)&&r.push(o.regular.instancesData.buffer),n.defined(o.silhouette.instancesData)&&r.push(o.silhouette.instancesData.buffer)),o},P.createIndexBuffer=function(e){return n.defined(e.cachedSidenessIndexBuffer)||(e.cachedSidenessIndexBuffer=A.createIndexBuffer({context:e,typedArray:(t=new Uint16Array(6),r=0,t[r++]=2,t[r++]=1,t[r++]=0,t[r++]=3,t[r++]=2,t[r++]=0,t),usage:l.STATIC_DRAW,indexDatatype:i.IndexDatatype.UNSIGNED_SHORT}),e.cachedSidenessIndexBuffer.vertexArrayDestroyable=!1),e.cachedSidenessIndexBuffer;var t,r},P.createRegularEdgeAttributes=function(e,t){if(n.defined(t.instancesData)&&0!=t.instancesData.length){var r={},a=[];t.attributeLocations=r,t.attributes=a;var i=A.createVertexBuffer({context:e,typedArray:t.instancesData,usage:l.STATIC_DRAW});t.instancesData=null;var s=f.ComponentDatatype.getSizeInBytes(f.ComponentDatatype.FLOAT),o=F(e),u=0;r.aSideness=u++,a.push({index:r.aSideness,vertexBuffer:o,componentsPerAttribute:2,componentDatatype:f.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*f.ComponentDatatype.getSizeInBytes(f.ComponentDatatype.FLOAT),normalize:!1});var c=P.RegularInstanceStride,y=0;r.aPosition0=u++,a.push({index:r.aPosition0,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=3,r.aPosition1=u++,a.push({index:r.aPosition1,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=3,r.aNormal=u++,a.push({index:r.aNormal,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=3,r.batchId=u++,a.push({index:r.batchId,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=1,r.aVariantStroke=u++,a.push({index:r.aVariantStroke,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=1,r.aVariantExtension=u++,a.push({index:r.aVariantExtension,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*y,strideInBytes:s*c,instanceDivisor:1}),y+=1}},P.createSilhouetteEdgeAttributes=function(e,t){if(n.defined(t.instancesData)&&0!=t.instancesData.length){var r={},a=[];t.attributeLocations=r,t.attributes=a;var i=A.createVertexBuffer({context:e,typedArray:t.instancesData,usage:l.STATIC_DRAW});t.instancesData=null;var s=f.ComponentDatatype.getSizeInBytes(f.ComponentDatatype.FLOAT),o=0;r.aSideness=o++,a.push({index:r.aSideness,vertexBuffer:F(e),componentsPerAttribute:2,componentDatatype:f.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*s,normalize:!1});var u=P.SilhouetteInstanceStride,c=0;r.aPosition0=o++,a.push({index:r.aPosition0,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=3,r.aPosition1=o++,a.push({index:r.aPosition1,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=3,r.aNormalA=o++,a.push({index:r.aNormalA,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=3,r.aNormalB=o++,a.push({index:r.aNormalB,vertexBuffer:i,componentsPerAttribute:3,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=3,r.batchId=o++,a.push({index:r.batchId,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=1,r.aVariantStroke=o++,a.push({index:r.aVariantStroke,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=1,r.aVariantExtension=o++,a.push({index:r.aVariantExtension,vertexBuffer:i,componentsPerAttribute:1,componentDatatype:f.ComponentDatatype.FLOAT,normalize:!0,offsetInBytes:s*c,strideInBytes:s*u,instanceDivisor:1}),c+=1}},P.extractEdgeInformation=function(e,t,r){var a,i=e.attrLocation.aPosition,s=e.vertexAttributes[i],o=n.defined(e.nCompressOptions)&&(e.nCompressOptions&p.SVC_Vertex)===p.SVC_Vertex,f=s.componentsPerAttribute;o?(f=3,a=L(e,s)):a=new Float32Array(s.typedArray.buffer,s.typedArray.byteOffset,s.typedArray.byteLength/4);var u=a.length/f;if(t&&r)return{faces:r,neighbors:v.computeNeighbors(r,u),vertices:a,dim:f};var c,y=s.typedArray.buffer;c=o?a.buffer:y.slice(s.typedArray.byteOffset,s.typedArray.byteOffset+s.typedArray.byteLength);var d=v.deduplicate(c,f),l=P.selectIndexData(d.indices,r);return{faces:l,neighbors:v.computeNeighbors(l,d.uniqueCount),vertices:new Float32Array(d.buffer),dim:f}},P.selectIndexData=function(e,t){if(t){t=t.slice();for(var n=0;n<t.length;n++)t[n]=e[t[n]];return t}return e};var O=new r.Cartesian3,R=new r.Cartesian3,E=new r.Cartesian3,N=new r.Cartesian3,M=new r.Cartesian3,U=new r.Cartesian3,W=new r.Cartesian3,q=new r.Cartesian3;function G(e,t){function r(e,t,n){var r=48217*e%2147483647,a=t+r/2147483647*(n-=t);return{seed:r,result:Math.round(a)}}var a=function(e,t){var n=new Float32Array(6),r=new Uint32Array(n.buffer),a=new Uint32Array(1);n[0]=e.x,n[1]=e.y,n[2]=e.z,n[3]=t.x,n[4]=t.y,n[5]=t.z,a[0]=5381;for(var i=0;i<r.length;i++)a[0]=31*a[0]+r[i];return a[0]}(e,t);n.defined(a)||(a=2147483647*Math.random());var i=r(a,0,255);a=i.seed,i.result,a=(i=r(a,0,5)).seed;var s=i.result;i=function(e){var t=48217*e%2147483647;return{seed:t,result:t/2147483646}}(a),a=i.seed;var o=i.result;return o=-(1-Math.min(o/.7,1))+Math.max(0,o-.7)/(1-.7),{variantStroke:s,variantExtension:o=255*(Math.abs(o)**1.2*(0>o?-1:1)*.5+.5)}}P.createEdgeDataByIndices=function(e,t){var a,i,s=e.attrLocation.aPosition,o=e.vertexAttributes[s],f=n.defined(e.nCompressOptions)&&(e.nCompressOptions&p.SVC_Vertex)===p.SVC_Vertex,u=o.componentsPerAttribute;f?(u=3,a=L(e,o)):a=new Float32Array(o.typedArray.buffer,o.typedArray.byteOffset,o.typedArray.byteLength/4);for(var c=[],y=[],d=(i=0===t.indexType?new Uint16Array(t.indicesTypedArray.buffer,t.indicesTypedArray.byteOffset,t.indicesTypedArray.byteLength/2):new Uint32Array(t.indicesTypedArray.buffer,t.indicesTypedArray.byteOffset,t.indicesTypedArray.byteLength/4)).length,l=0,A=0,v=4*Math.floor(d/4);A<v;A+=4){var h=i[A],x=i[A+1],b=i[A+2],C=i[A+3];if(O.x=a[u*h],O.y=a[u*h+1],O.z=a[u*h+2],R.x=a[u*x],R.y=a[u*x+1],R.z=a[u*x+2],E.x=a[u*b],E.y=a[u*b+1],E.z=a[u*b+2],N.x=a[u*C],N.y=a[u*C+1],N.z=a[u*C+2],!(r.Cartesian3.equals(R,E)||r.Cartesian3.equals(R,N)||r.Cartesian3.equals(R,O)||r.Cartesian3.equals(E,O)||r.Cartesian3.equals(N,O))){if(b===C){if(r.Cartesian3.subtract(R,O,M),r.Cartesian3.subtract(E,O,U),r.Cartesian3.cross(M,U,M),r.Cartesian3.equals(M,r.Cartesian3.ZERO))continue;r.Cartesian3.normalize(M,M),c.push(O.x),c.push(O.y),c.push(O.z),c.push(R.x),c.push(R.y),c.push(R.z),c.push(M.x),c.push(M.y),c.push(M.z),c.push(h);var m=(g=G(O,R)).variantStroke,D=g.variantExtension;c.push(m),c.push(D)}else{if(r.Cartesian3.subtract(R,O,M),r.Cartesian3.subtract(E,O,U),r.Cartesian3.cross(M,U,M),r.Cartesian3.equals(M,r.Cartesian3.ZERO))continue;if(r.Cartesian3.normalize(M,M),r.Cartesian3.subtract(R,O,W),r.Cartesian3.subtract(N,O,q),r.Cartesian3.cross(q,W,W),r.Cartesian3.equals(W,r.Cartesian3.ZERO))continue;r.Cartesian3.normalize(W,W),y.push(O.x),y.push(O.y),y.push(O.z),y.push(R.x),y.push(R.y),y.push(R.z),y.push(M.x),y.push(M.y),y.push(M.z),y.push(W.x),y.push(W.y),y.push(W.z),y.push(h);var g;m=(g=G(O,R)).variantStroke,D=g.variantExtension;y.push(m),y.push(D)}l+=r.Cartesian3.distance(O,R)}}var B=l/(d/4),I=c.length/P.RegularInstanceStride,z=y.length/P.SilhouetteInstanceStride;return{regular:{instancesData:new Float32Array(c),instanceCount:I,edgeLength:I*B},silhouette:{instancesData:new Float32Array(y),instanceCount:z,edgeLength:z},averageEdgeLength:B}};var k=Object.freeze({encNONE:0,enrS3TCDXTN:14,enrPVRTPF_PVRTC2:19,enrPVRTPF_PVRTC:20,enrPVRTPF_PVRTC_4bpp:21,enrPVRTPF_ETC1:22});e.DXTTextureDecode=y,e.S3MCompressType=k,e.S3MEdgeProcessor=P,e.VertexCompressOption=p}));
