import { ref, shallowRef } from "vue";
import { defineStore } from "pinia";
import { getMarkDetail } from "../../api";

const activeNameMap = new Map();

export const useMapDialogStore = defineStore("mapDialog", () => {
  const activateName = ref("");
  const mapType = ref("");
  const dialogData = ref({});
  const dialogContent = ref({});

  function openRequest(data, cType) {
    if (!activeNameMap.has(data.name)) {
      return;
    }
    mapType.value = cType;
    dialogData.value = {
      id: data.id,
      name: data.name,
      position: activeNameMap.get(data.name) ? data.position : false,
    };
    //请求弹窗数据
    getMarkDetail(data.id).then((res) => {
      dialogContent.value = res;
      activateName.value = data.name;
    });
  }

  function close() {
    dialogData.value = {};
    activateName.value = "";
  }

  return { activateName, dialogData, mapType, dialogContent, openRequest, close };
});

export function mountDialog(name, mountMap) {
  activeNameMap.set(name, mountMap);
}

export function unmountDialog(name) {
  activeNameMap.delete(name);
}
