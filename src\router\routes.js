import Chart from "@/views/chart/index.vue";
import Wide from "@/views/wide/index.vue";
import MapControl from "@/views/mapControl/index.vue";
import PageHeader from "@/views/pageHeader/index.vue";
import RiverContent from "@/views/riverContent/index.vue";
import VillageMain from "@/views/villageMain/index.vue";
import MapMask from "@/views/MapMask.vue";
import Background from "@/views/Background.vue";
import GrainBackground from "@/views/GrainBackground.vue";
import GrainContent from "@/views/grainContent/index.vue";
import Login from "@/views/Login.vue";
import notFound from "@/views/404.vue";
import Earth from "@/components/threejs/earth/index.vue";
import OlMap from "@/components/olMap/index.vue";
import ImageMap from "@/views/ImageMap.vue";
import Cesium from "@/components/cesium/index.vue";
import DynamicPageEdit from "@/dynamicPage/edit.vue";
import { useOlStore } from "@/stores/modules/ol";
import { useCesiumStore } from "@/stores/modules/cesium";
import { useVillageStore } from "@/stores/modules/village";
import { useGrainStore } from "@/stores/modules/grain";
import { useChartStore } from "@/stores/modules/chart";
// 配置页面对应各层级需要的页面
// meta 元信息配置
// stores 配置当前页面需要自动调用的store，会自动调用store的getData方法
// layout pageSize 针对当前页面配置页面自适应参数
const routes = [
  {
    path: "/pageEdit",
    name: "PageEdit",
    components: {
      editLayer: DynamicPageEdit,
    },
  },
  {
    path: "/grain",
    name: "Grain",
    components: {
      firstLayer: GrainBackground,
      secondLayer: ImageMap,
      thirdLayer: GrainContent,
    },
    meta: {
      stores: [useGrainStore],
      layout: "default", //显示适配：default 等比缩放、adapt 高度缩放，宽度占满全屏
      pageSize: {
        width: 1920,
        height: 1080,
      },
    },
  },
  {
    path: "/",
    name: "Village",
    components: {
      firstLayer: OlMap,
      secondLayer: MapMask,
      thirdLayer: RiverContent,
      fourthLayer: VillageMain,
    },
    meta: {
      stores: [useVillageStore],
      layout: "adapt", //显示适配：default 等比缩放、adapt 高度缩放，宽度占满全屏
      pageSize: {
        width: 1920,
        height: 1080,
      },
    },
  },
  {
    path: "/chart",
    name: "Chart",
    components: {
      firstLayer: Background,
      thirdLayer: Chart,
      fourthLayer: PageHeader,
    },
    meta: {
      stores: [useChartStore],
    },
  },
  {
    path: "/wide",
    name: "Wide",
    components: {
      firstLayer: Background,
      thirdLayer: Wide,
      fourthLayer: PageHeader,
    },
  },
  {
    path: "/earth",
    name: "Earth",
    components: {
      firstLayer: Earth,
      fourthLayer: PageHeader,
    },
  },
  {
    path: "/ol",
    name: "Ol",
    components: {
      firstLayer: OlMap,
      secondLayer: MapControl,
      thirdLayer: Chart,
      fourthLayer: PageHeader,
    },
    meta: {
      stores: [useOlStore, useChartStore],
    },
  },
  {
    path: "/cesium",
    name: "Cesium",
    components: {
      firstLayer: Cesium,
      secondLayer: MapControl,
      fourthLayer: PageHeader,
    },
    meta: {
      stores: [useCesiumStore],
    },
  },
  {
    path: "/login",
    name: "Login",
    components: {
      firstLayer: Login,
    },
  },
  {
    path: "/not-found",
    name: "404",
    components: {
      firstLayer: notFound,
    },
  },
];
export default routes;
