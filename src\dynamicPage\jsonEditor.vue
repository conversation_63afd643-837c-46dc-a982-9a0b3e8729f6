<template>
  <div ref="editorRef" class="editor"></div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import JSONEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.min.css';

const props = defineProps({
  data: {
    type: Object,
    default: {}
  }
})

const emit = defineEmits(['change'])

const editorRef = ref(null)

let editor

const options = {
  mode: 'code',
  modes: ['code'],
  search: false,
  statusBar: false,
  mainMenuBar: false,
  onChange: () => {
    // 数据发生变化
    try {
      const json = editor.get();
      emit('change', json)
    } catch (e) {

    }
  }
}

function initEditor() {
  editor = new JSONEditor(editorRef.value, options)
  editor.set(props.data);
}

onMounted(() => {
  initEditor()
})
</script>
<style scoped>
.editor {
  width: 270px;
}
</style>