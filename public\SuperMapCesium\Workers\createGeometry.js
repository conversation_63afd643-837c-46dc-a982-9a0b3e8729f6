define(["./when-b60132fc","./PrimitivePipeline-580026be","./createTaskProcessorWorker","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Cartesian2-47311507","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryPipeline-44c6c124","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./WebMercatorProjection-01b1b5e7"],(function(e,r,t,a,n,i,o,s,c,u,d,b,f,l,m,p,y,C,v,P,k,h,G){"use strict";var W={};function A(r){var t=W[r];return e.defined(t)||("object"==typeof exports?W[t]=t=require("Workers/"+r):require(["Workers/"+r],(function(e){W[t=e]=e}))),t}return t((function(t,a){for(var n=t.subTasks,i=n.length,o=new Array(i),s=0;s<i;s++){var c=n[s],u=c.geometry,d=c.moduleName;if(e.defined(d)){var b=A(d);o[s]=b(u,c.offset)}else o[s]=u}return e.when.all(o,(function(e){return r.PrimitivePipeline.packCreateGeometryResults(e,a)}))}))}));
