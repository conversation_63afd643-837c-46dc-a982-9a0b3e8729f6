define(["./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./EllipseGeometry-462fe80a","./Cartesian2-47311507","./VertexFormat-6446fca0","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-8958744c","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-79deae95","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-44c6c124","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-a793ed08","./Plane-a3d8b3d2"],(function(e,t,i,r,o,a,n,l,s,d,m,u,c,p,y,_,h,G,x,f,g,b,v,E,w,A){"use strict";function C(e){var t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:t,semiMinorAxis:t,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new r.EllipseGeometry(o),this._workerName="createCircleGeometry"}C.packedLength=r.EllipseGeometry.packedLength,C.pack=function(e,t,i){return r.EllipseGeometry.pack(e._ellipseGeometry,t,i)};var M=new r.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),F={center:new e.Cartesian3,radius:void 0,ellipsoid:o.Ellipsoid.clone(o.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new a.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return C.unpack=function(t,n,l){var s=r.EllipseGeometry.unpack(t,n,M);return F.center=e.Cartesian3.clone(s._center,F.center),F.ellipsoid=o.Ellipsoid.clone(s._ellipsoid,F.ellipsoid),F.height=s._height,F.extrudedHeight=s._extrudedHeight,F.granularity=s._granularity,F.vertexFormat=a.VertexFormat.clone(s._vertexFormat,F.vertexFormat),F.stRotation=s._stRotation,F.shadowVolume=s._shadowVolume,i.defined(l)?(F.semiMajorAxis=s._semiMajorAxis,F.semiMinorAxis=s._semiMinorAxis,l._ellipseGeometry=new r.EllipseGeometry(F),l):(F.radius=s._semiMajorAxis,new C(F))},C.createGeometry=function(e){return r.EllipseGeometry.createGeometry(e._ellipseGeometry)},C.createShadowVolume=function(e,t,i){var r=e._ellipseGeometry._granularity,o=e._ellipseGeometry._ellipsoid,n=t(r,o),l=i(r,o);return new C({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:o,stRotation:e._ellipseGeometry._stRotation,granularity:r,extrudedHeight:n,height:l,vertexFormat:a.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(C.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(t,r){return i.defined(r)&&(t=C.unpack(t,r)),t._ellipseGeometry._center=e.Cartesian3.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=o.Ellipsoid.clone(t._ellipseGeometry._ellipsoid),C.createGeometry(t)}}));
