define(["./arrayRemoveDuplicates-d2f048c5","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CoplanarPolygonGeometryLibrary-b4bff604","./when-b60132fc","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryPipeline-44c6c124","./IndexDatatype-8a5eead4","./PolygonGeometryLibrary-208ca427","./FeatureDetection-806b12f0","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./OrientedBoundingBox-08964f84","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-ce9a1fbb","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./PolygonPipeline-d328cdf1","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-ed1a6bf4","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./ArcType-29cf2197"],(function(e,t,r,n,a,o,i,y,l,c,p,u,s,d,m,f,b,g,h,P,v,G,C,L,E,T,k,H,w,A){"use strict";function D(e){for(var t=e.length,r=new Float64Array(3*t),n=u.IndexDatatype.createTypedArray(t,2*t),o=0,i=0,c=0;c<t;c++){var p=e[c];r[o++]=p.x,r[o++]=p.y,r[o++]=p.z,n[i++]=c,n[i++]=(c+1)%t}var s=new l.GeometryAttributes({position:new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:r})});return new y.Geometry({attributes:s,indices:n,primitiveType:d.PrimitiveType.LINES})}function I(e){var t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy;this._polygonHierarchy=t,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=s.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+1}I.fromPositions=function(e){return new I({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions}})},I.pack=function(e,t,r){return r=i.defaultValue(r,0),t[r=s.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,r)]=e.packedLength,t};var _={polygonHierarchy:{}};return I.unpack=function(e,t,r){t=i.defaultValue(t,0);var n=s.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=n.startingIndex,delete n.startingIndex;var a=e[t];return i.defined(r)||(r=new I(_)),r._polygonHierarchy=n,r.packedLength=a,r},I.createGeometry=function(n){var a=n._polygonHierarchy,i=a.positions;if(!((i=e.arrayRemoveDuplicates(i,r.Cartesian3.equalsEpsilon,!0)).length<3)&&o.CoplanarPolygonGeometryLibrary.validOutline(i)){var l=s.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(a,!1);if(0!==l.length){for(var u=[],d=0;d<l.length;d++){var m=new c.GeometryInstance({geometry:D(l[d])});u.push(m)}var f=p.GeometryPipeline.combineInstances(u)[0],b=t.BoundingSphere.fromPoints(a.positions);return new y.Geometry({attributes:f.attributes,indices:f.indices,primitiveType:f.primitiveType,boundingSphere:b})}}},function(e,t){return i.defined(t)&&(e=I.unpack(e,t)),e._ellipsoid=m.Ellipsoid.clone(e._ellipsoid),I.createGeometry(e)}}));
