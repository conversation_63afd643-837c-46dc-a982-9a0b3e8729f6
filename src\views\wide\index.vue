<template>
  <LayoutBox :parts="parts" :focus="true" :ultra-wide="ultraWide"></LayoutBox>
</template>
<script setup>
import LeftOne from './LeftOne.vue'
import LeftTwo from './LeftTwo.vue'
import RightOne from './RightOne.vue'
import RightTwo from './RightTwo.vue'

const ultraWide = {
  aspectRatio: 32 / 9,
  contentScale: 1.6
}

const parts = [
  {
    component: LeftOne,
    positionStyle: {
      top: "120px",
      left: "20px"
    },
    ultraWidePosition: {
      top: "230px",
      left: "20px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0
    }
  },
  {
    component: LeftTwo,
    positionStyle: {
      top: "600px",
      left: "20px"
    },
    ultraWidePosition: {
      top: "230px",
      left: "840px"
    },
    animation: {
      type: "LeftSlide",
      startTime: 0.5
    }
  },
  {
    component: RightOne,
    positionStyle: {
      top: "120px",
      right: "20px"
    },
    ultraWidePosition: {
      top: "230px",
      right: "20px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0
    }
  },
  {
    component: RightTwo,
    positionStyle: {
      top: "600px",
      right: "20px"
    },
    ultraWidePosition: {
      top: "230px",
      right: "840px"
    },
    animation: {
      type: "RightSlide",
      startTime: 0.5
    }
  }
]
</script>
