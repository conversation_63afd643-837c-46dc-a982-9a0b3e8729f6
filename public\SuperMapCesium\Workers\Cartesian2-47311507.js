define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3"],(function(e,t,a,n,i){"use strict";function r(e,a,r,u){a=n.defaultValue(a,0),r=n.defaultValue(r,0),u=n.defaultValue(u,0),i.CesiumMath.equalsEpsilon(u,6356752.314245179,i.CesiumMath.EPSILON10)&&(i.CesiumMath.Radius=u),e._radii=new t.Cartesian3(a,r,u),e._radiiSquared=new t.Cartesian3(a*a,r*r,u*u),e._radiiToTheFourth=new t.Cartesian3(a*a*a*a,r*r*r*r,u*u*u*u),e._oneOverRadii=new t.Cartesian3(0===a?0:1/a,0===r?0:1/r,0===u?0:1/u),e._oneOverRadiiSquared=new t.Cartesian3(0===a?0:1/(a*a),0===r?0:1/(r*r),0===u?0:1/(u*u)),e._minimumRadius=Math.min(a,r,u),e._maximumRadius=Math.max(a,r,u),e._centerToleranceSquared=i.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function u(e,t,a){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,r(this,e,t,a)}Object.defineProperties(u.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),u.clone=function(e,a){if(n.defined(e)){var i=e._radii;return n.defined(a)?(t.Cartesian3.clone(i,a._radii),t.Cartesian3.clone(e._radiiSquared,a._radiiSquared),t.Cartesian3.clone(e._radiiToTheFourth,a._radiiToTheFourth),t.Cartesian3.clone(e._oneOverRadii,a._oneOverRadii),t.Cartesian3.clone(e._oneOverRadiiSquared,a._oneOverRadiiSquared),a._minimumRadius=e._minimumRadius,a._maximumRadius=e._maximumRadius,a._centerToleranceSquared=e._centerToleranceSquared,a):new u(i.x,i.y,i.z)}},u.fromCartesian3=function(e,t){return n.defined(t)||(t=new u),n.defined(e)?(r(t,e.x,e.y,e.z),t):t},u.WGS84=Object.freeze(new u(6378137,6378137,i.CesiumMath.Radius)),u.XIAN80=Object.freeze(new u(6378140,6378140,6356755.29)),u.CGCS2000=Object.freeze(new u(6378137,6378137,6356752.31)),u.UNIT_SPHERE=Object.freeze(new u(1,1,1)),u.MOON=Object.freeze(new u(i.CesiumMath.LUNAR_RADIUS,i.CesiumMath.LUNAR_RADIUS,i.CesiumMath.LUNAR_RADIUS)),u.prototype.clone=function(e){return u.clone(this,e)},u.packedLength=t.Cartesian3.packedLength,u.pack=function(e,a,i){return i=n.defaultValue(i,0),t.Cartesian3.pack(e._radii,a,i),a},u.unpack=function(e,a,i){a=n.defaultValue(a,0);var r=t.Cartesian3.unpack(e,a);return u.fromCartesian3(r,i)},u.prototype.geocentricSurfaceNormal=t.Cartesian3.normalize,u.prototype.geodeticSurfaceNormalCartographic=function(e,a){var i=e.longitude,r=e.latitude,u=Math.cos(r),o=u*Math.cos(i),s=u*Math.sin(i),d=Math.sin(r);return n.defined(a)||(a=new t.Cartesian3),a.x=o,a.y=s,a.z=d,t.Cartesian3.normalize(a,a)},u.prototype.geodeticSurfaceNormal=function(e,a){return n.defined(a)||(a=new t.Cartesian3),a=t.Cartesian3.multiplyComponents(e,this._oneOverRadiiSquared,a),t.Cartesian3.normalize(a,a)};var o=new t.Cartesian3,s=new t.Cartesian3;u.prototype.cartographicToCartesian=function(e,a){var i=o,r=s;this.geodeticSurfaceNormalCartographic(e,i),t.Cartesian3.multiplyComponents(this._radiiSquared,i,r);var u=Math.sqrt(t.Cartesian3.dot(i,r));return t.Cartesian3.divideByScalar(r,u,r),t.Cartesian3.multiplyByScalar(i,e.height,i),n.defined(a)||(a=new t.Cartesian3),t.Cartesian3.add(r,i,a)},u.prototype.cartographicArrayToCartesianArray=function(e,t){var a=e.length;n.defined(t)?t.length=a:t=new Array(a);for(var i=0;i<a;i++)t[i]=this.cartographicToCartesian(e[i],t[i]);return t};var d=new t.Cartesian3,h=new t.Cartesian3,c=new t.Cartesian3;function l(e,t,a,i){this.west=n.defaultValue(e,0),this.south=n.defaultValue(t,0),this.east=n.defaultValue(a,0),this.north=n.defaultValue(i,0)}u.prototype.cartesianToCartographic=function(e,a){var r=this.scaleToGeodeticSurface(e,h);if(n.defined(r)){var u=this.geodeticSurfaceNormal(r,d),o=t.Cartesian3.subtract(e,r,c),s=Math.atan2(u.y,u.x),l=Math.asin(u.z),f=i.CesiumMath.sign(t.Cartesian3.dot(o,e))*t.Cartesian3.magnitude(o);return n.defined(a)?(a.longitude=s,a.latitude=l,a.height=f,a):new t.Cartographic(s,l,f)}},u.prototype.cartesianArrayToCartographicArray=function(e,t){var a=e.length;n.defined(t)?t.length=a:t=new Array(a);for(var i=0;i<a;++i)t[i]=this.cartesianToCartographic(e[i],t[i]);return t},u.prototype.scaleToGeodeticSurface=function(e,a){return t.scaleToGeodeticSurface(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,a)},u.prototype.scaleToGeocentricSurface=function(e,a){n.defined(a)||(a=new t.Cartesian3);var i=e.x,r=e.y,u=e.z,o=this._oneOverRadiiSquared,s=1/Math.sqrt(i*i*o.x+r*r*o.y+u*u*o.z);return t.Cartesian3.multiplyByScalar(e,s,a)},u.prototype.transformPositionToScaledSpace=function(e,a){return n.defined(a)||(a=new t.Cartesian3),t.Cartesian3.multiplyComponents(e,this._oneOverRadii,a)},u.prototype.transformPositionFromScaledSpace=function(e,a){return n.defined(a)||(a=new t.Cartesian3),t.Cartesian3.multiplyComponents(e,this._radii,a)},u.prototype.equals=function(e){return this===e||n.defined(e)&&t.Cartesian3.equals(this._radii,e._radii)},u.prototype.toString=function(){return this._radii.toString()},u.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,a,i){a=n.defaultValue(a,0);var r=this._squaredXOverSquaredZ;if(n.defined(i)||(i=new t.Cartesian3),i.x=0,i.y=0,i.z=e.z*(1-r),!(Math.abs(i.z)>=this._radii.z-a))return i},Object.defineProperties(l.prototype,{width:{get:function(){return l.computeWidth(this)}},height:{get:function(){return l.computeHeight(this)}}}),l.packedLength=4,l.pack=function(e,t,a){return a=n.defaultValue(a,0),t[a++]=e.west,t[a++]=e.south,t[a++]=e.east,t[a]=e.north,t},l.unpack=function(e,t,a){return t=n.defaultValue(t,0),n.defined(a)||(a=new l),a.west=e[t++],a.south=e[t++],a.east=e[t++],a.north=e[t],a},l.computeWidth=function(e){var t=e.east,a=e.west;return t<a&&(t+=i.CesiumMath.TWO_PI),t-a},l.computeHeight=function(e){return e.north-e.south},l.fromDegrees=function(e,t,a,r,u){return e=i.CesiumMath.toRadians(n.defaultValue(e,0)),t=i.CesiumMath.toRadians(n.defaultValue(t,0)),a=i.CesiumMath.toRadians(n.defaultValue(a,0)),r=i.CesiumMath.toRadians(n.defaultValue(r,0)),n.defined(u)?(u.west=e,u.south=t,u.east=a,u.north=r,u):new l(e,t,a,r)},l.fromRadians=function(e,t,a,i,r){return n.defined(r)?(r.west=n.defaultValue(e,0),r.south=n.defaultValue(t,0),r.east=n.defaultValue(a,0),r.north=n.defaultValue(i,0),r):new l(e,t,a,i)},l.fromCartographicArray=function(e,t){for(var a=Number.MAX_VALUE,r=-Number.MAX_VALUE,u=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,d=-Number.MAX_VALUE,h=0,c=e.length;h<c;h++){var f=e[h];a=Math.min(a,f.longitude),r=Math.max(r,f.longitude),s=Math.min(s,f.latitude),d=Math.max(d,f.latitude);var m=f.longitude>=0?f.longitude:f.longitude+i.CesiumMath.TWO_PI;u=Math.min(u,m),o=Math.max(o,m)}return r-a>o-u&&(a=u,(r=o)>i.CesiumMath.PI&&(r-=i.CesiumMath.TWO_PI),a>i.CesiumMath.PI&&(a-=i.CesiumMath.TWO_PI)),n.defined(t)?(t.west=a,t.south=s,t.east=r,t.north=d,t):new l(a,s,r,d)},l.fromCartesianArray=function(e,t,a){t=n.defaultValue(t,u.WGS84);for(var r=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,d=-Number.MAX_VALUE,h=Number.MAX_VALUE,c=-Number.MAX_VALUE,f=0,m=e.length;f<m;f++){var p=t.cartesianToCartographic(e[f]);r=Math.min(r,p.longitude),o=Math.max(o,p.longitude),h=Math.min(h,p.latitude),c=Math.max(c,p.latitude);var C=p.longitude>=0?p.longitude:p.longitude+i.CesiumMath.TWO_PI;s=Math.min(s,C),d=Math.max(d,C)}return o-r>d-s&&(r=s,(o=d)>i.CesiumMath.PI&&(o-=i.CesiumMath.TWO_PI),r>i.CesiumMath.PI&&(r-=i.CesiumMath.TWO_PI)),n.defined(a)?(a.west=r,a.south=h,a.east=o,a.north=c,a):new l(r,h,o,c)},l.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.west=e.west,t.south=e.south,t.east=e.east,t.north=e.north,t):new l(e.west,e.south,e.east,e.north)},l.equalsEpsilon=function(e,t,a){return e===t||n.defined(e)&&n.defined(t)&&Math.abs(e.west-t.west)<=a&&Math.abs(e.south-t.south)<=a&&Math.abs(e.east-t.east)<=a&&Math.abs(e.north-t.north)<=a},l.prototype.clone=function(e){return l.clone(this,e)},l.prototype.equals=function(e){return l.equals(this,e)},l.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.west===t.west&&e.south===t.south&&e.east===t.east&&e.north===t.north},l.prototype.equalsEpsilon=function(e,t){return l.equalsEpsilon(this,e,t)},l.validate=function(e){},l.southwest=function(e,a){return n.defined(a)?(a.longitude=e.west,a.latitude=e.south,a.height=0,a):new t.Cartographic(e.west,e.south)},l.northwest=function(e,a){return n.defined(a)?(a.longitude=e.west,a.latitude=e.north,a.height=0,a):new t.Cartographic(e.west,e.north)},l.northeast=function(e,a){return n.defined(a)?(a.longitude=e.east,a.latitude=e.north,a.height=0,a):new t.Cartographic(e.east,e.north)},l.southeast=function(e,a){return n.defined(a)?(a.longitude=e.east,a.latitude=e.south,a.height=0,a):new t.Cartographic(e.east,e.south)},l.center=function(e,a){var r=e.east,u=e.west;r<u&&(r+=i.CesiumMath.TWO_PI);var o=i.CesiumMath.negativePiToPi(.5*(u+r)),s=.5*(e.south+e.north);return n.defined(a)?(a.longitude=o,a.latitude=s,a.height=0,a):new t.Cartographic(o,s)},l.intersection=function(e,t,a){var r=e.east,u=e.west,o=t.east,s=t.west;r<u&&o>0?r+=i.CesiumMath.TWO_PI:o<s&&r>0&&(o+=i.CesiumMath.TWO_PI),r<u&&s<0?s+=i.CesiumMath.TWO_PI:o<s&&u<0&&(u+=i.CesiumMath.TWO_PI);var d=i.CesiumMath.negativePiToPi(Math.max(u,s)),h=i.CesiumMath.negativePiToPi(Math.min(r,o));if(!((e.west<e.east||t.west<t.east)&&h<=d)){var c=Math.max(e.south,t.south),f=Math.min(e.north,t.north);if(!(c>=f))return n.defined(a)?(a.west=d,a.south=c,a.east=h,a.north=f,a):new l(d,c,h,f)}},l.simpleIntersection=function(e,t,a){var i=Math.max(e.west,t.west),r=Math.max(e.south,t.south),u=Math.min(e.east,t.east),o=Math.min(e.north,t.north);if(!(r>=o||i>=u))return n.defined(a)?(a.west=i,a.south=r,a.east=u,a.north=o,a):new l(i,r,u,o)},l.union=function(e,t,a){n.defined(a)||(a=new l);var r=e.east,u=e.west,o=t.east,s=t.west;r<u&&o>0?r+=i.CesiumMath.TWO_PI:o<s&&r>0&&(o+=i.CesiumMath.TWO_PI),r<u&&s<0?s+=i.CesiumMath.TWO_PI:o<s&&u<0&&(u+=i.CesiumMath.TWO_PI);var d=i.CesiumMath.convertLongitudeRange(Math.min(u,s)),h=i.CesiumMath.convertLongitudeRange(Math.max(r,o));return a.west=d,a.south=Math.min(e.south,t.south),a.east=h,a.north=Math.max(e.north,t.north),a},l.expand=function(e,t,a){return n.defined(a)||(a=new l),a.west=Math.min(e.west,t.longitude),a.south=Math.min(e.south,t.latitude),a.east=Math.max(e.east,t.longitude),a.north=Math.max(e.north,t.latitude),a},l.contains=function(e,t){var a=t.longitude,n=t.latitude,r=e.west,u=e.east;return u<r&&(u+=i.CesiumMath.TWO_PI,a<0&&(a+=i.CesiumMath.TWO_PI)),(a>r||i.CesiumMath.equalsEpsilon(a,r,i.CesiumMath.EPSILON14))&&(a<u||i.CesiumMath.equalsEpsilon(a,u,i.CesiumMath.EPSILON14))&&n>=e.south&&n<=e.north};var f=new t.Cartographic;l.subsample=function(e,t,a,r){t=n.defaultValue(t,u.WGS84),a=n.defaultValue(a,0),n.defined(r)||(r=[]);var o=0,s=e.north,d=e.south,h=e.east,c=e.west,m=f;m.height=a,m.longitude=c,m.latitude=s,r[o]=t.cartographicToCartesian(m,r[o]),o++,m.longitude=h,r[o]=t.cartographicToCartesian(m,r[o]),o++,m.latitude=d,r[o]=t.cartographicToCartesian(m,r[o]),o++,m.longitude=c,r[o]=t.cartographicToCartesian(m,r[o]),o++,m.latitude=s<0?s:d>0?d:0;for(var p=1;p<8;++p)m.longitude=-Math.PI+p*i.CesiumMath.PI_OVER_TWO,l.contains(e,m)&&(r[o]=t.cartographicToCartesian(m,r[o]),o++);return 0===m.latitude&&(m.longitude=c,r[o]=t.cartographicToCartesian(m,r[o]),o++,m.longitude=h,r[o]=t.cartographicToCartesian(m,r[o]),o++),r.length=o,r};var m=new t.Cartographic;function p(e,t){this.x=n.defaultValue(e,0),this.y=n.defaultValue(t,0)}l.prototype.contains=function(e){return l.contains(this,l.southwest(e,m))&&l.contains(this,l.northwest(e,m))&&l.contains(this,l.southeast(e,m))&&l.contains(this,l.northeast(e,m))},l.MAX_VALUE=Object.freeze(new l(-Math.PI,-i.CesiumMath.PI_OVER_TWO,Math.PI,i.CesiumMath.PI_OVER_TWO)),p.fromElements=function(e,t,a){return n.defined(a)?(a.x=e,a.y=t,a):new p(e,t)},p.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.x=e.x,t.y=e.y,t):new p(e.x,e.y)},p.fromCartesian3=p.clone,p.fromCartesian4=p.clone,p.packedLength=2,p.pack=function(e,t,a){return a=n.defaultValue(a,0),t[a++]=e.x,t[a]=e.y,t},p.unpack=function(e,t,a){return t=n.defaultValue(t,0),n.defined(a)||(a=new p),a.x=e[t++],a.y=e[t],a},p.packArray=function(e,t){var i=e.length,r=2*i;if(n.defined(t)){if(!Array.isArray(t)&&t.length!==r)throw new a.DeveloperError("If result is a typed array, it must have exactly array.length * 2 elements");t.length!==r&&(t.length=r)}else t=new Array(r);for(var u=0;u<i;++u)p.pack(e[u],t,2*u);return t},p.unpackArray=function(e,t){var a=e.length;n.defined(t)?t.length=a/2:t=new Array(a/2);for(var i=0;i<a;i+=2){var r=i/2;t[r]=p.unpack(e,i,t[r])}return t},p.fromArray=p.unpack,p.maximumComponent=function(e){return Math.max(e.x,e.y)},p.minimumComponent=function(e){return Math.min(e.x,e.y)},p.minimumByComponent=function(e,t,a){return a.x=Math.min(e.x,t.x),a.y=Math.min(e.y,t.y),a},p.maximumByComponent=function(e,t,a){return a.x=Math.max(e.x,t.x),a.y=Math.max(e.y,t.y),a},p.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y},p.magnitude=function(e){return Math.sqrt(p.magnitudeSquared(e))};var C=new p;p.distance=function(e,t){return p.subtract(e,t,C),p.magnitude(C)},p.distanceSquared=function(e,t){return p.subtract(e,t,C),p.magnitudeSquared(C)},p.normalize=function(e,t){var a=p.magnitude(e);return t.x=e.x/a,t.y=e.y/a,t},p.dot=function(e,t){return e.x*t.x+e.y*t.y},p.multiplyComponents=function(e,t,a){return a.x=e.x*t.x,a.y=e.y*t.y,a},p.divideComponents=function(e,t,a){return a.x=e.x/t.x,a.y=e.y/t.y,a},p.add=function(e,t,a){return a.x=e.x+t.x,a.y=e.y+t.y,a},p.subtract=function(e,t,a){return a.x=e.x-t.x,a.y=e.y-t.y,a},p.multiplyByScalar=function(e,t,a){return a.x=e.x*t,a.y=e.y*t,a},p.divideByScalar=function(e,t,a){return a.x=e.x/t,a.y=e.y/t,a},p.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t},p.abs=function(e,t){return t.x=Math.abs(e.x),t.y=Math.abs(e.y),t};var M=new p;p.lerp=function(e,t,a,n){return p.multiplyByScalar(t,a,M),n=p.multiplyByScalar(e,1-a,n),p.add(M,n,n)};var g=new p,y=new p;p.angleBetween=function(e,t){return p.normalize(e,g),p.normalize(t,y),i.CesiumMath.acosClamped(p.dot(g,y))};var _=new p;p.mostOrthogonalAxis=function(e,t){var a=p.normalize(e,_);return p.abs(a,a),t=a.x<=a.y?p.clone(p.UNIT_X,t):p.clone(p.UNIT_Y,t)},p.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.x===t.x&&e.y===t.y},p.equalsArray=function(e,t,a){return e.x===t[a]&&e.y===t[a+1]},p.equalsEpsilon=function(e,t,a,r){return e===t||n.defined(e)&&n.defined(t)&&i.CesiumMath.equalsEpsilon(e.x,t.x,a,r)&&i.CesiumMath.equalsEpsilon(e.y,t.y,a,r)},p.ZERO=Object.freeze(new p(0,0)),p.UNIT_X=Object.freeze(new p(1,0)),p.UNIT_Y=Object.freeze(new p(0,1)),p.prototype.clone=function(e){return p.clone(this,e)},p.prototype.equals=function(e){return p.equals(this,e)},p.prototype.equalsEpsilon=function(e,t,a){return p.equalsEpsilon(this,e,t,a)},p.prototype.toString=function(){return"("+this.x+", "+this.y+")"},e.Cartesian2=p,e.Ellipsoid=u,e.Rectangle=l}));
