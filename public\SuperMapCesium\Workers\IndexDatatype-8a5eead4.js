define(["exports","./when-b60132fc","./Check-7b2a090c","./Math-119be1a3","./WebGLConstants-4ae0db90"],(function(e,r,t,n,N){"use strict";var E={UNSIGNED_BYTE:N.WebGLConstants.UNSIGNED_BYTE,UNSIGNED_SHORT:N.WebGLConstants.UNSIGNED_SHORT,UNSIGNED_INT:N.WebGLConstants.UNSIGNED_INT,getSizeInBytes:function(e){switch(e){case E.UNSIGNED_BYTE:return Uint8Array.BYTES_PER_ELEMENT;case E.UNSIGNED_SHORT:return Uint16Array.BYTES_PER_ELEMENT;case E.UNSIGNED_INT:return Uint32Array.BYTES_PER_ELEMENT}},fromSizeInBytes:function(e){switch(e){case 2:return E.UNSIGNED_SHORT;case 4:return E.UNSIGNED_INT;case 1:return E.UNSIGNED_BYTE}},validate:function(e){return r.defined(e)&&(e===E.UNSIGNED_BYTE||e===E.UNSIGNED_SHORT||e===E.UNSIGNED_INT)},createTypedArray:function(e,r){return e>=n.CesiumMath.SIXTY_FOUR_KILOBYTES?new Uint32Array(r):new Uint16Array(r)},createTypedArrayFromArrayBuffer:function(e,r,t,N){return e>=n.CesiumMath.SIXTY_FOUR_KILOBYTES?new Uint32Array(r,t,N):new Uint16Array(r,t,N)}},a=Object.freeze(E);e.IndexDatatype=a}));
