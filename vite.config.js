import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
// import basicSsl from "@vitejs/plugin-basic-ssl";
import { viteMockServe } from "vite-plugin-mock";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 判断自定义元素
          isCustomElement: (tag) => tag.startsWith("swiper-"),
        },
      },
    }),
    //开发环境mock
    viteMockServe({
      mockPath: "./src/api/mock",
    }),
    //生产环境mock
    // viteMockServe({
    //   mockPath: "./src/api/mock",
    //   localEnabled: false, // 开发打包开关
    //   prodEnabled: true, // 生产打包开关
    //   injectCode: `
    //     import { setupProdMockServer } from './api/mock/mockProdServer';
    //     setupProdMockServer();
    //   `,
    // }),
    // basicSsl(),
  ],
  base: "",
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    // https: true,
    proxy: {
      "/ypzlbm": {
        target: "https://*************:31607",
        changeOrigin: true,
        secure: false,
      },
      "/geoserver": {
        target: "http://*************:8888",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
