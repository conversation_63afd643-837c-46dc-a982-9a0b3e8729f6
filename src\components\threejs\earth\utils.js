/**
 *lng:经度
 *lat:维度
 *radius:地球半径
 */

export function lglt2xyz(radius, lng, lat) {
  const phi = (180 + lng) * (Math.PI / 180);
  const theta = (90 - lat) * (Math.PI / 180);
  return {
    x: -radius * Math.sin(theta) * Math.cos(phi),
    y: radius * Math.cos(theta),
    z: radius * Math.sin(theta) * Math.sin(phi),
  };
}
// function lglt2xyz(radius, lng, lat) {
//   const theta = (90 + lng) * (Math.PI / 180);
//   const phi = (90 - lat) * (Math.PI / 180);
//   return new THREE.Vector3().setFromSpherical(new THREE.Spherical(radius, phi, theta));
// }
