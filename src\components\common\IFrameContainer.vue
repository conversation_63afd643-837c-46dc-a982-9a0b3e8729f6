<template>
  <iframe v-if="props.url" ref="iframe" class="container" :src="props.url">
  </iframe>
</template>
<script setup>
import { watch, onMounted, onBeforeUnmount, inject, ref, onUpdated } from "vue";
const props = defineProps({
  url: {
    type: String,
    default: ""
  }
})

const iframe = ref(null)


onMounted(() => {

})
onBeforeUnmount(() => {

})
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
  border-width: 0px;
}
</style>
