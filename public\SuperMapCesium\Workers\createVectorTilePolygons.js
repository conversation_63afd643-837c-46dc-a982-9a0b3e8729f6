define(["./AttributeCompression-90851096","./Cartographic-3309dd0d","./Color-2a095a27","./when-b60132fc","./Cartesian2-47311507","./IndexDatatype-8a5eead4","./Math-119be1a3","./OrientedBoundingBox-08964f84","./createTaskProcessorWorker","./Check-7b2a090c","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./buildModuleUrl-8958744c","./Event-16a2dfbf","./EllipsoidTangentPlane-ce9a1fbb","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./GeometryAttribute-06a41648","./PolygonPipeline-d328cdf1","./earcut-2.2.1-20c8012f","./ComponentDatatype-c140a87d","./EllipsoidRhumbLine-ed1a6bf4"],(function(e,a,r,n,t,i,o,s,d,f,c,u,l,h,p,g,b,C,y,m,v,I,w,x){"use strict";var A=new a.Cartesian3,E=new t.Ellipsoid,N=new t.Rectangle,k={min:void 0,max:void 0,indexBytesPerElement:void 0};function T(e,a,n){var t=a.length,i=2+t*s.OrientedBoundingBox.packedLength+1+function(e){for(var a=e.length,n=0,t=0;t<a;++t)n+=r.Color.packedLength+3+e[t].batchIds.length;return n}(n),o=new Float64Array(i),d=0;o[d++]=e,o[d++]=t;for(var f=0;f<t;++f)s.OrientedBoundingBox.pack(a[f],o,d),d+=s.OrientedBoundingBox.packedLength;var c=n.length;o[d++]=c;for(var u=0;u<c;++u){var l=n[u];r.Color.pack(l.color,o,d),d+=r.Color.packedLength,o[d++]=l.offset,o[d++]=l.count;var h=l.batchIds,p=h.length;o[d++]=p;for(var g=0;g<p;++g)o[d++]=h[g]}return o}var B=new a.Cartesian3,L=new a.Cartesian3,O=new a.Cartesian3,P=new a.Cartesian3,U=new a.Cartesian3,F=new a.Cartographic,D=new t.Rectangle;return d((function(d,f){var c;!function(e){var r=new Float64Array(e),n=0;k.indexBytesPerElement=r[n++],k.min=r[n++],k.max=r[n++],a.Cartesian3.unpack(r,n,A),n+=a.Cartesian3.packedLength,t.Ellipsoid.unpack(r,n,E),n+=t.Ellipsoid.packedLength,t.Rectangle.unpack(r,n,N)}(d.packedBuffer),c=2===k.indexBytesPerElement?new Uint16Array(d.indices):new Uint32Array(d.indices);var u,l,h,p=new Uint16Array(d.positions),g=new Uint32Array(d.counts),b=new Uint32Array(d.indexCounts),C=new Uint32Array(d.batchIds),y=new Uint32Array(d.batchTableColors),m=new Array(g.length),v=A,I=E,w=N,x=k.min,R=k.max,S=d.minimumHeights,M=d.maximumHeights;n.defined(S)&&n.defined(M)&&(S=new Float32Array(S),M=new Float32Array(M));var _=p.length/2,G=p.subarray(0,_),Y=p.subarray(_,2*_);e.AttributeCompression.zigZagDeltaDecode(G,Y);var V=new Float32Array(3*_);for(u=0;u<_;++u){var H=G[u],W=Y[u],z=o.CesiumMath.lerp(w.west,w.east,H/32767),Z=o.CesiumMath.lerp(w.south,w.north,W/32767),j=a.Cartographic.fromRadians(z,Z,0,F),q=I.cartographicToCartesian(j,B);a.Cartesian3.pack(q,V,3*u)}var J=g.length,K=new Array(J),Q=new Array(J),X=0,$=0;for(u=0;u<J;++u)K[u]=X,Q[u]=$,X+=g[u],$+=b[u];var ee,ae=new Float32Array(3*_*2),re=new Uint16Array(2*_),ne=new Uint32Array(Q.length),te=new Uint32Array(b.length),ie=[],oe={};for(u=0;u<J;++u)h=y[u],n.defined(oe[h])?(oe[h].positionLength+=g[u],oe[h].indexLength+=b[u],oe[h].batchIds.push(u)):oe[h]={positionLength:g[u],indexLength:b[u],offset:0,indexOffset:0,batchIds:[u]};var se=0,de=0;for(h in oe)if(oe.hasOwnProperty(h)){(ee=oe[h]).offset=se,ee.indexOffset=de;var fe=2*ee.positionLength,ce=2*ee.indexLength+6*ee.positionLength;se+=fe,de+=ce,ee.indexLength=ce}var ue=[];for(h in oe)oe.hasOwnProperty(h)&&(ee=oe[h],ue.push({color:r.Color.fromRgba(parseInt(h)),offset:ee.indexOffset,count:ee.indexLength,batchIds:ee.batchIds}));for(u=0;u<J;++u){var le=(ee=oe[h=y[u]]).offset,he=3*le,pe=le,ge=K[u],be=g[u],Ce=C[u],ye=x,me=R;n.defined(S)&&n.defined(M)&&(ye=S[u],me=M[u]);var ve=Number.POSITIVE_INFINITY,Ie=Number.NEGATIVE_INFINITY,we=Number.POSITIVE_INFINITY,xe=Number.NEGATIVE_INFINITY;for(l=0;l<be;++l){var Ae=a.Cartesian3.unpack(V,3*ge+3*l,B);I.scaleToGeodeticSurface(Ae,Ae);var Ee=I.cartesianToCartographic(Ae,F),Ne=Ee.latitude,ke=Ee.longitude;ve=Math.min(Ne,ve),Ie=Math.max(Ne,Ie),we=Math.min(ke,we),xe=Math.max(ke,xe);var Te=I.geodeticSurfaceNormal(Ae,L),Be=a.Cartesian3.multiplyByScalar(Te,ye,O),Le=a.Cartesian3.add(Ae,Be,P);Be=a.Cartesian3.multiplyByScalar(Te,me,Be);var Oe=a.Cartesian3.add(Ae,Be,U);a.Cartesian3.subtract(Oe,v,Oe),a.Cartesian3.subtract(Le,v,Le),a.Cartesian3.pack(Oe,ae,he),a.Cartesian3.pack(Le,ae,he+3),re[pe]=Ce,re[pe+1]=Ce,he+=6,pe+=2}(w=D).west=we,w.east=xe,w.south=ve,w.north=Ie,m[u]=s.OrientedBoundingBox.fromRectangle(w,x,R,I);var Pe=ee.indexOffset,Ue=Q[u],Fe=b[u];for(ne[u]=Pe,l=0;l<Fe;l+=3){var De=c[Ue+l]-ge,Re=c[Ue+l+1]-ge,Se=c[Ue+l+2]-ge;ie[Pe++]=2*De+le,ie[Pe++]=2*Re+le,ie[Pe++]=2*Se+le,ie[Pe++]=2*Se+1+le,ie[Pe++]=2*Re+1+le,ie[Pe++]=2*De+1+le}for(l=0;l<be;++l){var Me=l,_e=(l+1)%be;ie[Pe++]=2*Me+1+le,ie[Pe++]=2*_e+le,ie[Pe++]=2*Me+le,ie[Pe++]=2*Me+1+le,ie[Pe++]=2*_e+1+le,ie[Pe++]=2*_e+le}ee.offset+=2*be,ee.indexOffset=Pe,te[u]=Pe-ne[u]}ie=i.IndexDatatype.createTypedArray(ae.length/3,ie);for(var Ge=ue.length,Ye=0;Ye<Ge;++Ye){for(var Ve=ue[Ye].batchIds,He=0,We=Ve.length,ze=0;ze<We;++ze)He+=te[Ve[ze]];ue[Ye].count=He}var Ze=T(2===ie.BYTES_PER_ELEMENT?i.IndexDatatype.UNSIGNED_SHORT:i.IndexDatatype.UNSIGNED_INT,m,ue);return f.push(ae.buffer,ie.buffer,ne.buffer,te.buffer,re.buffer,Ze.buffer),{positions:ae.buffer,indices:ie.buffer,indexOffsets:ne.buffer,indexCounts:te.buffer,batchIds:re.buffer,packedBuffer:Ze.buffer}}))}));
