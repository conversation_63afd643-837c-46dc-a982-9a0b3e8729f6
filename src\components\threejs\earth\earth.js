import * as THREE from "three";
import { lglt2xyz } from "./utils";
import { flyArc } from "./flyLine";
import earthTexture from "../../../assets/threejs/earth.png";
import earthGlowPng from "../../../assets/threejs/earth_glow.png";
import earthGlowLightPng from "../../../assets/threejs/earth_glow_light.png";
import pointArr from "../../../assets/threejs/world";
import wavePng from "../../../assets/threejs/wave.png";
import pointPng from "../../../assets/threejs/point.png";

//地球半径
const earthRadius = 14;
//世界轮廓线段
const countryLineColor = 0x7aa5a5;

const lineConfig = {
  color: 0xf3ae76, // 飞线的颜色
  flyLineColor: 0xff7714, // 飞行线的颜色
  speed: 0.004, // 拖尾飞线的速度
};

let waveAnimate;
let lineAnimate;

export function createEarthObj(cityList, flyLineDatas) {
  const object3D = new THREE.Object3D();

  const earthMesh = createEarthImageMesh(earthRadius);
  const glow = earthGlow(earthRadius, earthGlowPng, 3.05);
  const glowLight = earthGlow(earthRadius, earthGlowLightPng, 3.15);

  object3D.add(countryLine(earthRadius + 0.01));
  object3D.add(earthMesh);
  object3D.add(glow);
  object3D.add(glowLight);

  if (cityList && cityList.length) {
    const cityMeshGroup = getCityMeshGroup(cityList);
    object3D.add(cityMeshGroup);
    createWaveAnimate(cityMeshGroup.children);
  }

  if (flyLineDatas && flyLineDatas.length) {
    const flyLineGroup = getFlyLineGroup(flyLineDatas);
    object3D.add(flyLineGroup);
    createLineAnimate(flyLineGroup.children);
  }

  return { object3D, earthAnimate };
}

function createEarthImageMesh(radius) {
  // TextureLoader创建一个纹理加载器对象，可以加载图片作为纹理贴图
  const textureLoader = new THREE.TextureLoader();
  //加载纹理贴图
  const texture = textureLoader.load(earthTexture);
  //创建一个球体几何对象
  const geometry = new THREE.SphereGeometry(radius, 96, 96);
  //材质对象Material
  // MeshLambertMaterial  MeshBasicMaterial
  const material = new THREE.MeshLambertMaterial({
    map: texture, //设置地球0颜色贴图map
  });
  const mesh = new THREE.Mesh(geometry, material); //网格模型对象Mesh
  return mesh;
}

function earthGlow(radius, img, scale) {
  // TextureLoader创建一个纹理加载器对象，可以加载图片作为纹理贴图
  const textureLoader = new THREE.TextureLoader();
  const texture = textureLoader.load(img); //加载纹理贴图
  // 创建精灵材质对象SpriteMaterial
  const spriteMaterial = new THREE.SpriteMaterial({
    map: texture, //设置精灵纹理贴图
    transparent: true, //开启透明
    // opacity: 0.5,//可以通过透明度整体调节光圈
  });
  // 创建表示地球光圈的精灵模型
  const sprite = new THREE.Sprite(spriteMaterial);
  sprite.scale.set(radius * scale, radius * scale, 1); //适当缩放精灵

  return sprite;
}

function countryLine(R) {
  const geometry = new THREE.BufferGeometry(); //创建一个Buffer类型几何体对象
  //类型数组创建顶点数据
  const vertices = new Float32Array(pointArr);
  // 创建属性缓冲区对象
  const attribute = new THREE.BufferAttribute(vertices, 3); //3个为一组，表示一个顶点的xyz坐标
  // 设置几何体attributes属性的位置属性
  geometry.attributes.position = attribute;
  // 线条渲染几何体顶点数据
  const material = new THREE.LineBasicMaterial({
    color: countryLineColor, //线条颜色
  }); //材质对象
  const line = new THREE.LineSegments(geometry, material); //间隔绘制直线
  line.scale.set(R, R, R); //lineData.js对应球面半径是1，需要缩放R倍

  return line;
}

function getCityMeshGroup(cityList) {
  const meshGroup = new THREE.Group();

  for (let index = 0; index < cityList.length; index++) {
    const city = cityList[index];
    const cityMesh = createCityMesh(city.longitude, city.latitude);
    meshGroup.add(cityMesh);
  }

  return meshGroup;
}

function createCityMesh(lon, lat) {
  const cityXyz = lglt2xyz(earthRadius, lon, lat);
  //城市点位
  const geometry = new THREE.PlaneGeometry(1, 1);
  const pointTexture = new THREE.TextureLoader().load(pointPng);
  const pointMaterial = new THREE.MeshBasicMaterial({
    color: 0xffc300,
    map: pointTexture,
    transparent: true,
    opacity: 1.0,
    side: THREE.DoubleSide,
    depthWrite: false,
  });
  const pointMesh = new THREE.Mesh(geometry, pointMaterial);
  const pointScale = earthRadius * 0.05; //矩形平面Mesh的尺寸
  pointMesh.scale.set(pointScale, pointScale, pointScale); //设置mesh大小
  //设置mesh位置
  pointMesh.position.set(cityXyz.x, cityXyz.y, cityXyz.z);

  //城市波动圈
  const waveTexture = new THREE.TextureLoader().load(wavePng);
  const waveMaterial = new THREE.MeshBasicMaterial({
    color: 0x22ffcc,
    map: waveTexture,
    transparent: true,
    opacity: 1.0,
    side: THREE.DoubleSide,
    depthWrite: false,
  });
  const waveMesh = new THREE.Mesh(geometry, waveMaterial);
  const waveScale = earthRadius * 0.1;
  waveMesh.size = waveScale; //自定义一个属性，表示mesh静态大小
  waveMesh.scale.set(waveScale, waveScale, waveScale);
  waveMesh._s = Math.random() + 1.0; //光圈在原来mesh.size基础上1~2倍之间变化

  pointMesh.add(waveMesh);
  // mesh姿态设置
  // mesh在球面上的法线方向(球心和球面坐标构成的方向向量)
  const coordVec3 = new THREE.Vector3(cityXyz.x, cityXyz.y, cityXyz.z).normalize();
  // mesh默认在XOY平面上，法线方向沿着z轴new THREE.Vector3(0, 0, 1)
  const meshNormal = new THREE.Vector3(0, 0, 1);
  // 四元数属性.quaternion表示mesh的角度状态
  //.setFromUnitVectors();计算两个向量之间构成的四元数值
  pointMesh.quaternion.setFromUnitVectors(meshNormal, coordVec3);
  return pointMesh;
}

function createWaveAnimate(cityMeshArr) {
  waveAnimate = function () {
    // 所有波动光圈都有自己的透明度和大小状态
    // 一个波动光圈透明度变化过程是：0~1~0反复循环
    cityMeshArr.forEach((pointMesh) => {
      const waveMesh = pointMesh.children[0];
      waveMesh._s += 0.007;
      const newScale = waveMesh.size * waveMesh._s;
      waveMesh.scale.set(newScale, newScale, newScale);
      if (waveMesh._s <= 1.5) {
        waveMesh.material.opacity = (waveMesh._s - 1) * 2; //2等于1/(1.5-1.0)，保证透明度在0~1之间变化
      } else if (waveMesh._s > 1.5 && waveMesh._s <= 2) {
        waveMesh.material.opacity = 1 - (waveMesh._s - 1.5) * 2; //2等于1/(2.0-1.5) mesh缩放2倍对应0 缩放1.5被对应1
      } else {
        waveMesh._s = 1.0;
      }
    });
  };
}

function getFlyLineGroup(flyLineDatas) {
  const meshGroup = new THREE.Group();

  for (let index = 0; index < flyLineDatas.length; index++) {
    const { from, to } = flyLineDatas[index];
    if (from && to && to.length) {
      for (let cIndex = 0; cIndex < to.length; cIndex++) {
        const toPos = to[cIndex];
        const lineMesh = createFlyLine(from, toPos);
        meshGroup.add(lineMesh);
      }
    }
  }

  return meshGroup;
}

function createFlyLine(from, to) {
  const line = flyArc(earthRadius, from.longitude, from.latitude, to.longitude, to.latitude, lineConfig);
  return line;
}
function createLineAnimate(lineMeshArr) {
  lineAnimate = function () {
    lineMeshArr.forEach((fly) => {
      const flyMesh = fly.children[0];
      flyMesh.rotation.z += lineConfig.speed; // 调节飞线速度
      if (flyMesh.rotation.z >= flyMesh.flyEndAngle) flyMesh.rotation.z = 0;
    });
  };
}

function earthAnimate() {
  if (typeof waveAnimate === "function") {
    waveAnimate();
  }
  if (typeof lineAnimate === "function") {
    lineAnimate();
  }
}
