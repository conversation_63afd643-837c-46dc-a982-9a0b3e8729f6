define(["exports"],(function(e){"use strict";var t,r=void 0!==r?r:{},n={};for(t in r)r.hasOwnProperty(t)&&(n[t]=r[t]);var o="./this.program",i=function(e,t){throw t},a=!1,s=!1,c=!1,u=!1;if(a="object"==typeof window,s="function"==typeof importScripts,c="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,u=!a&&!c&&!s,r.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var l,d,f,p,E,_="";function m(e){return r.locateFile?r.locateFile(e,_):_+e}if(c)_=s?require("path").dirname(_)+"/":__dirname+"/",l=function(e,t){return p||(p=require("fs")),E||(E=require("path")),e=E.normalize(e),p.readFileSync(e,t?null:"utf8")},f=function(e){var t=l(e,!0);return t.buffer||(t=new Uint8Array(t)),M(t.buffer),t},process.argv.length>1&&(o=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),"undefined"!=typeof module&&(module.exports=r),process.on("uncaughtException",(function(e){if(!(e instanceof Ha))throw e})),process.on("unhandledRejection",be),i=function(e){process.exit(e)},r.inspect=function(){return"[Emscripten Module object]"};else if(u)"undefined"!=typeof read&&(l=function(e){return read(e)}),f=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(M("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs&&scriptArgs,"function"==typeof quit&&(i=function(e){quit(e)}),"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print);else{if(!a&&!s)throw new Error("environment detection error");s?_=self.location.href:"undefined"!=typeof document&&document.currentScript&&(_=document.currentScript.src),_=0!==_.indexOf("blob:")?_.substr(0,_.lastIndexOf("/")+1):"",l=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},s&&(f=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),d=function(e,t,r){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)}}var g=r.print||console.log.bind(console),T=r.printErr||console.warn.bind(console);for(t in n)n.hasOwnProperty(t)&&(r[t]=n[t]);n=null,r.arguments,Object.getOwnPropertyDescriptor(r,"arguments")||Object.defineProperty(r,"arguments",{configurable:!0,get:function(){be("Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),r.thisProgram&&(o=r.thisProgram),Object.getOwnPropertyDescriptor(r,"thisProgram")||Object.defineProperty(r,"thisProgram",{configurable:!0,get:function(){be("Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),r.quit&&(i=r.quit),Object.getOwnPropertyDescriptor(r,"quit")||Object.defineProperty(r,"quit",{configurable:!0,get:function(){be("Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),M(void 0===r.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),M(void 0===r.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),M(void 0===r.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),M(void 0===r.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),M(void 0===r.read,"Module.read option was removed (modify read_ in JS)"),M(void 0===r.readAsync,"Module.readAsync option was removed (modify readAsync in JS)"),M(void 0===r.readBinary,"Module.readBinary option was removed (modify readBinary in JS)"),M(void 0===r.setWindowTitle,"Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),M(void 0===r.TOTAL_MEMORY,"Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Object.getOwnPropertyDescriptor(r,"read")||Object.defineProperty(r,"read",{configurable:!0,get:function(){be("Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(r,"readAsync")||Object.defineProperty(r,"readAsync",{configurable:!0,get:function(){be("Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(r,"readBinary")||Object.defineProperty(r,"readBinary",{configurable:!0,get:function(){be("Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(r,"setWindowTitle")||Object.defineProperty(r,"setWindowTitle",{configurable:!0,get:function(){be("Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}});var h=16;function O(e,t){return t||(t=h),Math.ceil(e/t)*t}function w(e){w.shown||(w.shown={}),w.shown[e]||(w.shown[e]=1,T(e))}var y,D=0,v=function(e){D=e},b=function(){return D};r.wasmBinary&&(y=r.wasmBinary),Object.getOwnPropertyDescriptor(r,"wasmBinary")||Object.defineProperty(r,"wasmBinary",{configurable:!0,get:function(){be("Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}});var R,A=r.noExitRuntime||!0;function S(e,t,r){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":return N[e>>0];case"i16":return U[e>>1];case"i32":case"i64":return j[e>>2];case"float":return H[e>>2];case"double":return Q[e>>3];default:be("invalid type for getValue: "+t)}return null}Object.getOwnPropertyDescriptor(r,"noExitRuntime")||Object.defineProperty(r,"noExitRuntime",{configurable:!0,get:function(){be("Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),"object"!=typeof WebAssembly&&be("no native wasm support detected");var P=!1;function M(e,t){e||be("Assertion failed: "+t)}function F(e){var t=r["_"+e];return M(t,"Cannot call unknown function "+e+", make sure it is exported"),t}function x(e,t,r,n,o){var i={string:function(e){var t=0;if(null!=e&&0!==e){var r=1+(e.length<<2);V(e,t=Sa(r),r)}return t},array:function(e){var t=Sa(e.length);return K(e,t),t}};var a=F(e),s=[],c=0;if(M("array"!==t,'Return type should not be "array".'),n)for(var u=0;u<n.length;u++){var l=i[r[u]];l?(0===c&&(c=Ra()),s[u]=l(n[u])):s[u]=n[u]}var d=a.apply(null,s);return d=function(e){return"string"===t?W(e):"boolean"===t?Boolean(e):e}(d),0!==c&&Aa(c),d}function I(e,t,r,n){return function(){return x(e,t,r,arguments)}}var k,N,X,U,C,j,L,H,Q,G="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function B(e,t,r){for(var n=t+r,o=t;e[o]&&!(o>=n);)++o;if(o-t>16&&e.subarray&&G)return G.decode(e.subarray(t,o));for(var i="";t<o;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var c=63&e[t++];if(224==(240&a)?a=(15&a)<<12|s<<6|c:(240!=(248&a)&&w("Invalid UTF-8 leading byte 0x"+a.toString(16)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),a=(7&a)<<18|s<<12|c<<6|63&e[t++]),a<65536)i+=String.fromCharCode(a);else{var u=a-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}function W(e,t){return e?B(X,e,t):""}function z(e,t,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a);if(s<=127){if(r>=i)break;t[r++]=s}else if(s<=2047){if(r+1>=i)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=i)break;s>=2097152&&w("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x1FFFFF)."),t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-o}function V(e,t,r){return M("number"==typeof r,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),z(e,X,t,r)}function Y(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++r)),n<=127?++t:t+=n<=2047?2:n<=65535?3:4}return t}function q(e){var t=Y(e)+1,r=Ia(t);return r&&z(e,N,r,t),r}function K(e,t){M(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),N.set(e,t)}function J(e,t,r){for(var n=0;n<e.length;++n)M(e.charCodeAt(n)==e.charCodeAt(n)&255),N[t++>>0]=e.charCodeAt(n);r||(N[t>>0]=0)}function Z(e,t){return e%t>0&&(e+=t-e%t),e}function $(e){k=e,r.HEAP8=N=new Int8Array(e),r.HEAP16=U=new Int16Array(e),r.HEAP32=j=new Int32Array(e),r.HEAPU8=X=new Uint8Array(e),r.HEAPU16=C=new Uint16Array(e),r.HEAPU32=L=new Uint32Array(e),r.HEAPF32=H=new Float32Array(e),r.HEAPF64=Q=new Float64Array(e)}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var ee=5242880;r.TOTAL_STACK&&M(ee===r.TOTAL_STACK,"the stack size can no longer be determined at runtime");var te,re=r.INITIAL_MEMORY||16777216;function ne(){var e=Fa();M(0==(3&e)),L[1+(e>>2)]=34821223,L[2+(e>>2)]=2310721022,j[0]=1668509029}function oe(){if(!P){var e=Fa(),t=L[1+(e>>2)],r=L[2+(e>>2)];34821223==t&&2310721022==r||be("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x"+r.toString(16)+" "+t.toString(16)),1668509029!==j[0]&&be("Runtime error: The application has corrupted its heap memory area (address zero)!")}}Object.getOwnPropertyDescriptor(r,"INITIAL_MEMORY")||Object.defineProperty(r,"INITIAL_MEMORY",{configurable:!0,get:function(){be("Module.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),M(re>=ee,"INITIAL_MEMORY should be larger than TOTAL_STACK, was "+re+"! (TOTAL_STACK="+ee+")"),M("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),M(!r.wasmMemory,"Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally"),M(16777216==re,"Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically"),function(){var e=new Int16Array(1),t=new Int8Array(e.buffer);if(e[0]=25459,115!==t[0]||99!==t[1])throw"Runtime error: expected the system to be little-endian!"}();var ie=[],ae=[],se=[],ce=[],ue=!1,le=!1;function de(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)me(r.preRun.shift());Le(ie)}function fe(){oe(),M(!ue),ue=!0,r.noFSInit||_t.init.initialized||_t.init(),lt.init(),Le(ae)}function pe(){oe(),_t.ignorePermissions=!1,Le(se)}function Ee(){oe(),le=!0}function _e(){if(oe(),r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)ge(r.postRun.shift());Le(ce)}function me(e){ie.unshift(e)}function ge(e){ce.unshift(e)}ae.push({func:function(){wa()}}),M(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Te=0,he=null,Oe=null,we={};function ye(e){for(var t=e;;){if(!we[e])return e;e=t+Math.random()}}function De(e){Te++,r.monitorRunDependencies&&r.monitorRunDependencies(Te),e?(M(!we[e]),we[e]=1,null===he&&"undefined"!=typeof setInterval&&(he=setInterval((function(){if(P)return clearInterval(he),void(he=null);var e=!1;for(var t in we)e||(e=!0,T("still waiting on run dependencies:")),T("dependency: "+t);e&&T("(end of list)")}),1e4))):T("warning: run dependency added without ID")}function ve(e){if(Te--,r.monitorRunDependencies&&r.monitorRunDependencies(Te),e?(M(we[e]),delete we[e]):T("warning: run dependency removed without ID"),0==Te&&(null!==he&&(clearInterval(he),he=null),Oe)){var t=Oe;Oe=null,t()}}function be(e){r.onAbort&&r.onAbort(e),P=!0,e="abort("+(e+="")+") at "+Be(),new WebAssembly.RuntimeError(e)}function Re(e,t){return String.prototype.startsWith?e.startsWith(t):0===e.indexOf(t)}r.preloadedImages={},r.preloadedAudios={};var Ae="data:application/octet-stream;base64,";function Se(e){return Re(e,Ae)}var Pe,Me="file://";function Fe(e){return Re(e,Me)}function xe(e,t){return function(){var n=e,o=t;return t||(o=r.asm),M(ue,"native function `"+n+"` called before runtime initialization"),M(!le,"native function `"+n+"` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),o[e]||M(o[e],"exported native function `"+n+"` not found"),o[e].apply(null,arguments)}}var Ie,ke,Ne,Xe="undefined"==typeof window?self:window;function Ue(e){try{if(e==Pe&&y)return new Uint8Array(y);if(f)return f(e);throw"both async and sync fetching of the wasm failed"}catch(e){be(e)}}function Ce(){if(!y&&(a||s)){if("function"==typeof fetch&&!Fe(Pe))return fetch(Pe,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+Pe+"'";return e.arrayBuffer()})).catch((function(){return Ue(Pe)}));if(d)return new Promise((function(e,t){d(Pe,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return Ue(Pe)}))}function je(){var e={env:Oa,wasi_snapshot_preview1:Oa};function t(e,t){var n=e.exports;r.asm=n,M(R=r.asm.memory,"memory not found in wasm exports"),$(R.buffer),M(te=r.asm.__indirect_function_table,"table not found in wasm exports"),ve("wasm-instantiate")}De("wasm-instantiate");var n=r;function o(e){M(r===n,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),n=null,t(e.instance)}function i(t){return Ce().then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(e){Fe(Pe),be(e)}))}if(r.instantiateWasm)try{return r.instantiateWasm(e,t)}catch(e){return T("Module.instantiateWasm callback failed with error: "+e),!1}return y||"function"!=typeof WebAssembly.instantiateStreaming||Se(Pe)||Fe(Pe)||"function"!=typeof fetch?i(o):fetch(Pe,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(o,(function(e){return i(o)}))})),{}}function Le(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?te.get(n)():te.get(n)(t.arg):n(void 0===t.arg?null:t.arg)}else t(r)}}function He(e){return w("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function Qe(e){return e.replace(/\b_Z[\w\d_]+/g,(function(e){var t=He(e);return e===t?e:t+" ["+e+"]"}))}function Ge(){return""}function Be(){var e="";return r.extraStackTrace&&(e+="\n"+r.extraStackTrace()),Qe(e)}function We(e,t,r,n){be("Assertion failed: "+W(e)+", at: "+[t?W(t):"unknown filename",r,n?W(n):"unknown function"])}Se(Pe=Xe.location.href.endsWith(".openrealspace")?"../../static/Build/Cesium/ThirdParty/OSGBToS3M.wasm":"ThirdParty/OSGBToS3M.wasm")||(Pe=m(Pe)),r.callRuntimeCallbacks=Le,r.demangle=He,r.demangleAll=Qe,r.jsStackTrace=Ge,r.stackTrace=Be,r.___assert_fail=We,Ne=c?function(){var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:"undefined"!=typeof dateNow?dateNow:function(){return performance.now()},r._emscripten_get_now=Ne;var ze=!0;function Ve(e){return j[ya()>>2]=e,e}function Ye(e,t){var r;if(0===e)r=Date.now();else{if(1!==e&&4!==e||!ze)return Ve(28),-1;r=Ne()}return j[t>>2]=r/1e3|0,j[t+4>>2]=r%1e3*1e3*1e3|0,0}function qe(e,t){return Ye(e,t)}r._emscripten_get_now_is_monotonic=ze,r.setErrNo=Ve,r._clock_gettime=Ye,r.___clock_gettime=qe;var Ke={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function Je(e){return Ia(e+Ke.SIZE)+Ke.SIZE}function Ze(e,t){}function $e(e,t){}function et(e){this.excPtr=e,this.ptr=e-Ke.SIZE,this.set_type=function(e){j[this.ptr+Ke.TYPE_OFFSET>>2]=e},this.get_type=function(){return j[this.ptr+Ke.TYPE_OFFSET>>2]},this.set_destructor=function(e){j[this.ptr+Ke.DESTRUCTOR_OFFSET>>2]=e},this.get_destructor=function(){return j[this.ptr+Ke.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(e){j[this.ptr+Ke.REFCOUNT_OFFSET>>2]=e},this.set_caught=function(e){e=e?1:0,N[this.ptr+Ke.CAUGHT_OFFSET>>0]=e},this.get_caught=function(){return 0!=N[this.ptr+Ke.CAUGHT_OFFSET>>0]},this.set_rethrown=function(e){e=e?1:0,N[this.ptr+Ke.RETHROWN_OFFSET>>0]=e},this.get_rethrown=function(){return 0!=N[this.ptr+Ke.RETHROWN_OFFSET>>0]},this.init=function(e,t){this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=j[this.ptr+Ke.REFCOUNT_OFFSET>>2];j[this.ptr+Ke.REFCOUNT_OFFSET>>2]=e+1},this.release_ref=function(){var e=j[this.ptr+Ke.REFCOUNT_OFFSET>>2];return j[this.ptr+Ke.REFCOUNT_OFFSET>>2]=e-1,M(e>0),1===e}}r.ExceptionInfoAttrs=Ke,r.___cxa_allocate_exception=Je,r._atexit=Ze,r.___cxa_atexit=$e,r.ExceptionInfo=et;var tt=0;r.exceptionLast=tt;var rt=0;function nt(e,t,r){throw new et(e).init(t,r),tt=e,rt++,e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."}function ot(){if(!ot.called){ot.called=!0;var e=(new Date).getFullYear(),t=new Date(e,0,1),r=new Date(e,6,1),n=t.getTimezoneOffset(),o=r.getTimezoneOffset(),i=Math.max(n,o);j[ba()>>2]=60*i,j[va()>>2]=Number(n!=o);var a=l(t),s=l(r),c=q(a),u=q(s);o<n?(j[Da()>>2]=c,j[Da()+4>>2]=u):(j[Da()>>2]=u,j[Da()+4>>2]=c)}function l(e){var t=e.toTimeString().match(/\(([A-Za-z ]+)\)$/);return t?t[1]:"GMT"}}function it(e,t){ot();var r=new Date(1e3*j[e>>2]);j[t>>2]=r.getSeconds(),j[t+4>>2]=r.getMinutes(),j[t+8>>2]=r.getHours(),j[t+12>>2]=r.getDate(),j[t+16>>2]=r.getMonth(),j[t+20>>2]=r.getFullYear()-1900,j[t+24>>2]=r.getDay();var n=new Date(r.getFullYear(),0,1),o=(r.getTime()-n.getTime())/864e5|0;j[t+28>>2]=o,j[t+36>>2]=-60*r.getTimezoneOffset();var i=new Date(r.getFullYear(),6,1).getTimezoneOffset(),a=n.getTimezoneOffset(),s=0|(i!=a&&r.getTimezoneOffset()==Math.min(a,i));j[t+32>>2]=s;var c=j[Da()+(s?4:0)>>2];return j[t+40>>2]=c,t}function at(e,t){return it(e,t)}r.uncaughtExceptionCount=rt,r.___cxa_throw=nt,r._tzset=ot,r._localtime_r=it,r.___localtime_r=at;var st={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,t){for(var r=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:function(e){var t="/"===e.charAt(0),r="/"===e.substr(-1);return(e=st.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:function(e){var t=st.splitPath(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(e){if("/"===e)return"/";var t=(e=(e=st.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},extname:function(e){return st.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return st.normalize(e.join("/"))},join2:function(e,t){return st.normalize(e+"/"+t)}};function ct(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(c)try{var t=require("crypto");return function(){return t.randomBytes(1)[0]}}catch(e){}return function(){be("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: function(array) { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };")}}r.PATH=st,r.getRandomDevice=ct;var ut={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:_t.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t="/"===n.charAt(0)}return(t?"/":"")+(e=st.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||"."},relative:function(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=ut.resolve(e).substr(1),t=ut.resolve(t).substr(1);for(var n=r(e.split("/")),o=r(t.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var c=[];for(s=a;s<n.length;s++)c.push("..");return(c=c.concat(o.slice(a))).join("/")}};r.PATH_FS=ut;var lt={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){lt.ttys[e]={input:[],output:[],ops:t},_t.registerDevice(e,lt.stream_ops)},stream_ops:{open:function(e){var t=lt.ttys[e.node.rdev];if(!t)throw new _t.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.get_char)throw new _t.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new _t.ErrnoError(29)}if(void 0===s&&0===i)throw new _t.ErrnoError(6);if(null==s)break;i++,t[r+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.put_char)throw new _t.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,t[r+i])}catch(e){throw new _t.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(c){var r=Buffer.alloc?Buffer.alloc(256):new Buffer(256),n=0;try{n=p.readSync(process.stdin.fd,r,0,256,null)}catch(e){if(-1==e.toString().indexOf("EOF"))throw e;n=0}t=n>0?r.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=ha(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(g(B(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(g(B(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(T(B(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(T(B(e.output,0)),e.output=[])}}};function dt(e){for(var t=O(e,16384),r=Ia(t);e<t;)N[r+e++]=0;return r}r.TTY=lt,r.mmapAlloc=dt;var ft={ops_table:null,mount:function(e){return ft.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(_t.isBlkdev(r)||_t.isFIFO(r))throw new _t.ErrnoError(63);ft.ops_table||(ft.ops_table={dir:{node:{getattr:ft.node_ops.getattr,setattr:ft.node_ops.setattr,lookup:ft.node_ops.lookup,mknod:ft.node_ops.mknod,rename:ft.node_ops.rename,unlink:ft.node_ops.unlink,rmdir:ft.node_ops.rmdir,readdir:ft.node_ops.readdir,symlink:ft.node_ops.symlink},stream:{llseek:ft.stream_ops.llseek}},file:{node:{getattr:ft.node_ops.getattr,setattr:ft.node_ops.setattr},stream:{llseek:ft.stream_ops.llseek,read:ft.stream_ops.read,write:ft.stream_ops.write,allocate:ft.stream_ops.allocate,mmap:ft.stream_ops.mmap,msync:ft.stream_ops.msync}},link:{node:{getattr:ft.node_ops.getattr,setattr:ft.node_ops.setattr,readlink:ft.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ft.node_ops.getattr,setattr:ft.node_ops.setattr},stream:_t.chrdev_stream_ops}});var o=_t.createNode(e,t,r,n);return _t.isDir(o.mode)?(o.node_ops=ft.ops_table.dir.node,o.stream_ops=ft.ops_table.dir.stream,o.contents={}):_t.isFile(o.mode)?(o.node_ops=ft.ops_table.file.node,o.stream_ops=ft.ops_table.file.stream,o.usedBytes=0,o.contents=null):_t.isLink(o.mode)?(o.node_ops=ft.ops_table.link.node,o.stream_ops=ft.ops_table.link.stream):_t.isChrdev(o.mode)&&(o.node_ops=ft.ops_table.chrdev.node,o.stream_ops=ft.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[t]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var n=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=_t.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,_t.isDir(e.mode)?t.size=4096:_t.isFile(e.mode)?t.size=e.usedBytes:_t.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&ft.resizeFileStorage(e,t.size)},lookup:function(e,t){throw _t.genericErrors[44]},mknod:function(e,t,r,n){return ft.createNode(e,t,r,n)},rename:function(e,t,r){if(_t.isDir(e.mode)){var n;try{n=_t.lookupNode(t,r)}catch(e){}if(n)for(var o in n.contents)throw new _t.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=_t.lookupNode(e,t);for(var n in r.contents)throw new _t.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var n=ft.createNode(e,t,41471,0);return n.link=r,n},readlink:function(e){if(!_t.isLink(e.mode))throw new _t.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,n,o){var i=e.node.contents;if(o>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-o,n);if(M(a>=0),a>8&&i.subarray)t.set(i.subarray(o,o+a),r);else for(var s=0;s<a;s++)t[r+s]=i[o+s];return a},write:function(e,t,r,n,o,i){if(M(!(t instanceof ArrayBuffer)),t.buffer===N.buffer&&(i=!1),!n)return 0;var a=e.node;if(a.timestamp=Date.now(),t.subarray&&(!a.contents||a.contents.subarray)){if(i)return M(0===o,"canOwn must imply no weird position inside the file"),a.contents=t.subarray(r,r+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=t.slice(r,r+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(t.subarray(r,r+n),o),n}if(ft.expandFileStorage(a,o+n),a.contents.subarray&&t.subarray)a.contents.set(t.subarray(r,r+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=t[r+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(e,t,r){var n=t;if(1===r?n+=e.position:2===r&&_t.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new _t.ErrnoError(28);return n},allocate:function(e,t,r){ft.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,o,i){if(0!==t)throw new _t.ErrnoError(28);if(!_t.isFile(e.node.mode))throw new _t.ErrnoError(43);var a,s,c=e.node.contents;if(2&i||c.buffer!==k){if((n>0||n+r<c.length)&&(c=c.subarray?c.subarray(n,n+r):Array.prototype.slice.call(c,n,n+r)),s=!0,!(a=dt(r)))throw new _t.ErrnoError(48);N.set(c,a)}else s=!1,a=c.byteOffset;return{ptr:a,allocated:s}},msync:function(e,t,r,n,o){if(!_t.isFile(e.node.mode))throw new _t.ErrnoError(43);return 2&o||ft.stream_ops.write(e,t,0,n,r,!1),0}}};r.MEMFS=ft;var pt={0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"};r.ERRNO_MESSAGES=pt;var Et={EPERM:63,ENOENT:44,ESRCH:71,EINTR:27,EIO:29,ENXIO:60,E2BIG:1,ENOEXEC:45,EBADF:8,ECHILD:12,EAGAIN:6,EWOULDBLOCK:6,ENOMEM:48,EACCES:2,EFAULT:21,ENOTBLK:105,EBUSY:10,EEXIST:20,EXDEV:75,ENODEV:43,ENOTDIR:54,EISDIR:31,EINVAL:28,ENFILE:41,EMFILE:33,ENOTTY:59,ETXTBSY:74,EFBIG:22,ENOSPC:51,ESPIPE:70,EROFS:69,EMLINK:34,EPIPE:64,EDOM:18,ERANGE:68,ENOMSG:49,EIDRM:24,ECHRNG:106,EL2NSYNC:156,EL3HLT:107,EL3RST:108,ELNRNG:109,EUNATCH:110,ENOCSI:111,EL2HLT:112,EDEADLK:16,ENOLCK:46,EBADE:113,EBADR:114,EXFULL:115,ENOANO:104,EBADRQC:103,EBADSLT:102,EDEADLOCK:16,EBFONT:101,ENOSTR:100,ENODATA:116,ETIME:117,ENOSR:118,ENONET:119,ENOPKG:120,EREMOTE:121,ENOLINK:47,EADV:122,ESRMNT:123,ECOMM:124,EPROTO:65,EMULTIHOP:36,EDOTDOT:125,EBADMSG:9,ENOTUNIQ:126,EBADFD:127,EREMCHG:128,ELIBACC:129,ELIBBAD:130,ELIBSCN:131,ELIBMAX:132,ELIBEXEC:133,ENOSYS:52,ENOTEMPTY:55,ENAMETOOLONG:37,ELOOP:32,EOPNOTSUPP:138,EPFNOSUPPORT:139,ECONNRESET:15,ENOBUFS:42,EAFNOSUPPORT:5,EPROTOTYPE:67,ENOTSOCK:57,ENOPROTOOPT:50,ESHUTDOWN:140,ECONNREFUSED:14,EADDRINUSE:3,ECONNABORTED:13,ENETUNREACH:40,ENETDOWN:38,ETIMEDOUT:73,EHOSTDOWN:142,EHOSTUNREACH:23,EINPROGRESS:26,EALREADY:7,EDESTADDRREQ:17,EMSGSIZE:35,EPROTONOSUPPORT:66,ESOCKTNOSUPPORT:137,EADDRNOTAVAIL:4,ENETRESET:39,EISCONN:30,ENOTCONN:53,ETOOMANYREFS:141,EUSERS:136,EDQUOT:19,ESTALE:72,ENOTSUP:138,ENOMEDIUM:148,EILSEQ:25,EOVERFLOW:61,ECANCELED:11,ENOTRECOVERABLE:56,EOWNERDEAD:62,ESTRPIPE:135};r.ERRNO_CODES=Et;var _t={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e,t){if(t=t||{},!(e=ut.resolve(_t.cwd(),e)))return{path:"",node:null};var r={follow_mount:!0,recurse_count:0};for(var n in r)void 0===t[n]&&(t[n]=r[n]);if(t.recurse_count>8)throw new _t.ErrnoError(32);for(var o=st.normalizeArray(e.split("/").filter((function(e){return!!e})),!1),i=_t.root,a="/",s=0;s<o.length;s++){var c=s===o.length-1;if(c&&t.parent)break;if(i=_t.lookupNode(i,o[s]),a=st.join2(a,o[s]),_t.isMountpoint(i)&&(!c||c&&t.follow_mount)&&(i=i.mounted.root),!c||t.follow)for(var u=0;_t.isLink(i.mode);){var l=_t.readlink(a);if(a=ut.resolve(st.dirname(a),l),i=_t.lookupPath(a,{recurse_count:t.recurse_count}).node,u++>40)throw new _t.ErrnoError(32)}}return{path:a,node:i}},getPath:function(e){for(var t;;){if(_t.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:function(e,t){for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%_t.nameTable.length},hashAddNode:function(e){var t=_t.hashName(e.parent.id,e.name);e.name_next=_t.nameTable[t],_t.nameTable[t]=e},hashRemoveNode:function(e){var t=_t.hashName(e.parent.id,e.name);if(_t.nameTable[t]===e)_t.nameTable[t]=e.name_next;else for(var r=_t.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:function(e,t){var r=_t.mayLookup(e);if(r)throw new _t.ErrnoError(r,e);for(var n=_t.hashName(e.id,t),o=_t.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===e.id&&i===t)return o}return _t.lookup(e,t)},createNode:function(e,t,r,n){M("object"==typeof e);var o=new _t.FSNode(e,t,r,n);return _t.hashAddNode(o),o},destroyNode:function(e){_t.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(e){var t=_t.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:function(e){var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:function(e,t){return _t.ignorePermissions||(-1===t.indexOf("r")||292&e.mode)&&(-1===t.indexOf("w")||146&e.mode)&&(-1===t.indexOf("x")||73&e.mode)?0:2},mayLookup:function(e){var t=_t.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:function(e,t){try{_t.lookupNode(e,t);return 20}catch(e){}return _t.nodePermissions(e,"wx")},mayDelete:function(e,t,r){var n;try{n=_t.lookupNode(e,t)}catch(e){return e.errno}var o=_t.nodePermissions(e,"wx");if(o)return o;if(r){if(!_t.isDir(n.mode))return 54;if(_t.isRoot(n)||_t.getPath(n)===_t.cwd())return 10}else if(_t.isDir(n.mode))return 31;return 0},mayOpen:function(e,t){return e?_t.isLink(e.mode)?32:_t.isDir(e.mode)&&("r"!==_t.flagsToPermissionString(t)||512&t)?31:_t.nodePermissions(e,_t.flagsToPermissionString(t)):44},MAX_OPEN_FDS:4096,nextfd:function(e,t){e=e||0,t=t||_t.MAX_OPEN_FDS;for(var r=e;r<=t;r++)if(!_t.streams[r])return r;throw new _t.ErrnoError(33)},getStream:function(e){return _t.streams[e]},createStream:function(e,t,r){_t.FSStream||(_t.FSStream=function(){},_t.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var n=new _t.FSStream;for(var o in e)n[o]=e[o];e=n;var i=_t.nextfd(t,r);return e.fd=i,_t.streams[i]=e,e},closeStream:function(e){_t.streams[e]=null},chrdev_stream_ops:{open:function(e){var t=_t.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new _t.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,t){return e<<8|t},registerDevice:function(e,t){_t.devices[e]={stream_ops:t}},getDevice:function(e){return _t.devices[e]},getMounts:function(e){for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:function(e,t){"function"==typeof e&&(t=e,e=!1),_t.syncFSRequests++,_t.syncFSRequests>1&&T("warning: "+_t.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=_t.getMounts(_t.root.mount),n=0;function o(e){return M(_t.syncFSRequests>0),_t.syncFSRequests--,t(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,o(e));++n>=r.length&&o(null)}r.forEach((function(t){if(!t.type.syncfs)return i(null);t.type.syncfs(t,e,i)}))},mount:function(e,t,r){if("string"==typeof e)throw e;var n,o="/"===r,i=!r;if(o&&_t.root)throw new _t.ErrnoError(10);if(!o&&!i){var a=_t.lookupPath(r,{follow_mount:!1});if(r=a.path,n=a.node,_t.isMountpoint(n))throw new _t.ErrnoError(10);if(!_t.isDir(n.mode))throw new _t.ErrnoError(54)}var s={type:e,opts:t,mountpoint:r,mounts:[]},c=e.mount(s);return c.mount=s,s.root=c,o?_t.root=c:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),c},unmount:function(e){var t=_t.lookupPath(e,{follow_mount:!1});if(!_t.isMountpoint(t.node))throw new _t.ErrnoError(28);var r=t.node,n=r.mounted,o=_t.getMounts(n);Object.keys(_t.nameTable).forEach((function(e){for(var t=_t.nameTable[e];t;){var r=t.name_next;-1!==o.indexOf(t.mount)&&_t.destroyNode(t),t=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);M(-1!==i),r.mount.mounts.splice(i,1)},lookup:function(e,t){return e.node_ops.lookup(e,t)},mknod:function(e,t,r){var n=_t.lookupPath(e,{parent:!0}).node,o=st.basename(e);if(!o||"."===o||".."===o)throw new _t.ErrnoError(28);var i=_t.mayCreate(n,o);if(i)throw new _t.ErrnoError(i);if(!n.node_ops.mknod)throw new _t.ErrnoError(63);return n.node_ops.mknod(n,o,t,r)},create:function(e,t){return t=void 0!==t?t:438,t&=4095,t|=32768,_t.mknod(e,t,0)},mkdir:function(e,t){return t=void 0!==t?t:511,t&=1023,t|=16384,_t.mknod(e,t,0)},mkdirTree:function(e,t){for(var r=e.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{_t.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,t,r){return void 0===r&&(r=t,t=438),t|=8192,_t.mknod(e,t,r)},symlink:function(e,t){if(!ut.resolve(e))throw new _t.ErrnoError(44);var r=_t.lookupPath(t,{parent:!0}).node;if(!r)throw new _t.ErrnoError(44);var n=st.basename(t),o=_t.mayCreate(r,n);if(o)throw new _t.ErrnoError(o);if(!r.node_ops.symlink)throw new _t.ErrnoError(63);return r.node_ops.symlink(r,n,e)},rename:function(e,t){var r,n,o=st.dirname(e),i=st.dirname(t),a=st.basename(e),s=st.basename(t);if(r=_t.lookupPath(e,{parent:!0}).node,n=_t.lookupPath(t,{parent:!0}).node,!r||!n)throw new _t.ErrnoError(44);if(r.mount!==n.mount)throw new _t.ErrnoError(75);var c,u=_t.lookupNode(r,a),l=ut.relative(e,i);if("."!==l.charAt(0))throw new _t.ErrnoError(28);if("."!==(l=ut.relative(t,o)).charAt(0))throw new _t.ErrnoError(55);try{c=_t.lookupNode(n,s)}catch(e){}if(u!==c){var d=_t.isDir(u.mode),f=_t.mayDelete(r,a,d);if(f)throw new _t.ErrnoError(f);if(f=c?_t.mayDelete(n,s,d):_t.mayCreate(n,s))throw new _t.ErrnoError(f);if(!r.node_ops.rename)throw new _t.ErrnoError(63);if(_t.isMountpoint(u)||c&&_t.isMountpoint(c))throw new _t.ErrnoError(10);if(n!==r&&(f=_t.nodePermissions(r,"w")))throw new _t.ErrnoError(f);try{_t.trackingDelegate.willMovePath&&_t.trackingDelegate.willMovePath(e,t)}catch(r){T("FS.trackingDelegate['willMovePath']('"+e+"', '"+t+"') threw an exception: "+r.message)}_t.hashRemoveNode(u);try{r.node_ops.rename(u,n,s)}catch(e){throw e}finally{_t.hashAddNode(u)}try{_t.trackingDelegate.onMovePath&&_t.trackingDelegate.onMovePath(e,t)}catch(r){T("FS.trackingDelegate['onMovePath']('"+e+"', '"+t+"') threw an exception: "+r.message)}}},rmdir:function(e){var t=_t.lookupPath(e,{parent:!0}).node,r=st.basename(e),n=_t.lookupNode(t,r),o=_t.mayDelete(t,r,!0);if(o)throw new _t.ErrnoError(o);if(!t.node_ops.rmdir)throw new _t.ErrnoError(63);if(_t.isMountpoint(n))throw new _t.ErrnoError(10);try{_t.trackingDelegate.willDeletePath&&_t.trackingDelegate.willDeletePath(e)}catch(t){T("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+t.message)}t.node_ops.rmdir(t,r),_t.destroyNode(n);try{_t.trackingDelegate.onDeletePath&&_t.trackingDelegate.onDeletePath(e)}catch(t){T("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+t.message)}},readdir:function(e){var t=_t.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new _t.ErrnoError(54);return t.node_ops.readdir(t)},unlink:function(e){var t=_t.lookupPath(e,{parent:!0}).node,r=st.basename(e),n=_t.lookupNode(t,r),o=_t.mayDelete(t,r,!1);if(o)throw new _t.ErrnoError(o);if(!t.node_ops.unlink)throw new _t.ErrnoError(63);if(_t.isMountpoint(n))throw new _t.ErrnoError(10);try{_t.trackingDelegate.willDeletePath&&_t.trackingDelegate.willDeletePath(e)}catch(t){T("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+t.message)}t.node_ops.unlink(t,r),_t.destroyNode(n);try{_t.trackingDelegate.onDeletePath&&_t.trackingDelegate.onDeletePath(e)}catch(t){T("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+t.message)}},readlink:function(e){var t=_t.lookupPath(e).node;if(!t)throw new _t.ErrnoError(44);if(!t.node_ops.readlink)throw new _t.ErrnoError(28);return ut.resolve(_t.getPath(t.parent),t.node_ops.readlink(t))},stat:function(e,t){var r=_t.lookupPath(e,{follow:!t}).node;if(!r)throw new _t.ErrnoError(44);if(!r.node_ops.getattr)throw new _t.ErrnoError(63);return r.node_ops.getattr(r)},lstat:function(e){return _t.stat(e,!0)},chmod:function(e,t,r){var n;"string"==typeof e?n=_t.lookupPath(e,{follow:!r}).node:n=e;if(!n.node_ops.setattr)throw new _t.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&t|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,t){_t.chmod(e,t,!0)},fchmod:function(e,t){var r=_t.getStream(e);if(!r)throw new _t.ErrnoError(8);_t.chmod(r.node,t)},chown:function(e,t,r,n){var o;"string"==typeof e?o=_t.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new _t.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(e,t,r){_t.chown(e,t,r,!0)},fchown:function(e,t,r){var n=_t.getStream(e);if(!n)throw new _t.ErrnoError(8);_t.chown(n.node,t,r)},truncate:function(e,t){if(t<0)throw new _t.ErrnoError(28);var r;"string"==typeof e?r=_t.lookupPath(e,{follow:!0}).node:r=e;if(!r.node_ops.setattr)throw new _t.ErrnoError(63);if(_t.isDir(r.mode))throw new _t.ErrnoError(31);if(!_t.isFile(r.mode))throw new _t.ErrnoError(28);var n=_t.nodePermissions(r,"w");if(n)throw new _t.ErrnoError(n);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:function(e,t){var r=_t.getStream(e);if(!r)throw new _t.ErrnoError(8);if(0==(2097155&r.flags))throw new _t.ErrnoError(28);_t.truncate(r.node,t)},utime:function(e,t,r){var n=_t.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(t,r)})},open:function(e,t,n,o,i){if(""===e)throw new _t.ErrnoError(44);var a;if(n=void 0===n?438:n,n=64&(t="string"==typeof t?_t.modeStringToFlags(t):t)?4095&n|32768:0,"object"==typeof e)a=e;else{e=st.normalize(e);try{a=_t.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var s=!1;if(64&t)if(a){if(128&t)throw new _t.ErrnoError(20)}else a=_t.mknod(e,n,0),s=!0;if(!a)throw new _t.ErrnoError(44);if(_t.isChrdev(a.mode)&&(t&=-513),65536&t&&!_t.isDir(a.mode))throw new _t.ErrnoError(54);if(!s){var c=_t.mayOpen(a,t);if(c)throw new _t.ErrnoError(c)}512&t&&_t.truncate(a,0),t&=-131713;var u=_t.createStream({node:a,path:_t.getPath(a),flags:t,seekable:!0,position:0,stream_ops:a.stream_ops,ungotten:[],error:!1},o,i);u.stream_ops.open&&u.stream_ops.open(u),!r.logReadFiles||1&t||(_t.readFiles||(_t.readFiles={}),e in _t.readFiles||(_t.readFiles[e]=1,T("FS.trackingDelegate error on read file: "+e)));try{if(_t.trackingDelegate.onOpenFile){var l=0;1!=(2097155&t)&&(l|=_t.tracking.openFlags.READ),0!=(2097155&t)&&(l|=_t.tracking.openFlags.WRITE),_t.trackingDelegate.onOpenFile(e,l)}}catch(t){T("FS.trackingDelegate['onOpenFile']('"+e+"', flags) threw an exception: "+t.message)}return u},close:function(e){if(_t.isClosed(e))throw new _t.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{_t.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,t,r){if(_t.isClosed(e))throw new _t.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new _t.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new _t.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:function(e,t,r,n,o){if(n<0||o<0)throw new _t.ErrnoError(28);if(_t.isClosed(e))throw new _t.ErrnoError(8);if(1==(2097155&e.flags))throw new _t.ErrnoError(8);if(_t.isDir(e.node.mode))throw new _t.ErrnoError(31);if(!e.stream_ops.read)throw new _t.ErrnoError(28);var i=void 0!==o;if(i){if(!e.seekable)throw new _t.ErrnoError(70)}else o=e.position;var a=e.stream_ops.read(e,t,r,n,o);return i||(e.position+=a),a},write:function(e,t,r,n,o,i){if(n<0||o<0)throw new _t.ErrnoError(28);if(_t.isClosed(e))throw new _t.ErrnoError(8);if(0==(2097155&e.flags))throw new _t.ErrnoError(8);if(_t.isDir(e.node.mode))throw new _t.ErrnoError(31);if(!e.stream_ops.write)throw new _t.ErrnoError(28);e.seekable&&1024&e.flags&&_t.llseek(e,0,2);var a=void 0!==o;if(a){if(!e.seekable)throw new _t.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,t,r,n,o,i);a||(e.position+=s);try{e.path&&_t.trackingDelegate.onWriteToFile&&_t.trackingDelegate.onWriteToFile(e.path)}catch(t){T("FS.trackingDelegate['onWriteToFile']('"+e.path+"') threw an exception: "+t.message)}return s},allocate:function(e,t,r){if(_t.isClosed(e))throw new _t.ErrnoError(8);if(t<0||r<=0)throw new _t.ErrnoError(28);if(0==(2097155&e.flags))throw new _t.ErrnoError(8);if(!_t.isFile(e.node.mode)&&!_t.isDir(e.node.mode))throw new _t.ErrnoError(43);if(!e.stream_ops.allocate)throw new _t.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:function(e,t,r,n,o,i){if(0!=(2&o)&&0==(2&i)&&2!=(2097155&e.flags))throw new _t.ErrnoError(2);if(1==(2097155&e.flags))throw new _t.ErrnoError(2);if(!e.stream_ops.mmap)throw new _t.ErrnoError(43);return e.stream_ops.mmap(e,t,r,n,o,i)},msync:function(e,t,r,n,o){return e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,o):0},munmap:function(e){return 0},ioctl:function(e,t,r){if(!e.stream_ops.ioctl)throw new _t.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:function(e,t){if((t=t||{}).flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,n=_t.open(e,t.flags),o=_t.stat(e).size,i=new Uint8Array(o);return _t.read(n,i,0,o,0),"utf8"===t.encoding?r=B(i,0):"binary"===t.encoding&&(r=i),_t.close(n),r},writeFile:function(e,t,r){(r=r||{}).flags=r.flags||577;var n=_t.open(e,r.flags,r.mode);if("string"==typeof t){var o=new Uint8Array(Y(t)+1),i=z(t,o,0,o.length);_t.write(n,o,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");_t.write(n,t,0,t.byteLength,void 0,r.canOwn)}_t.close(n)},cwd:function(){return _t.currentPath},chdir:function(e){var t=_t.lookupPath(e,{follow:!0});if(null===t.node)throw new _t.ErrnoError(44);if(!_t.isDir(t.node.mode))throw new _t.ErrnoError(54);var r=_t.nodePermissions(t.node,"x");if(r)throw new _t.ErrnoError(r);_t.currentPath=t.path},createDefaultDirectories:function(){_t.mkdir("/tmp"),_t.mkdir("/home"),_t.mkdir("/home/<USER>")},createDefaultDevices:function(){_t.mkdir("/dev"),_t.registerDevice(_t.makedev(1,3),{read:function(){return 0},write:function(e,t,r,n,o){return n}}),_t.mkdev("/dev/null",_t.makedev(1,3)),lt.register(_t.makedev(5,0),lt.default_tty_ops),lt.register(_t.makedev(6,0),lt.default_tty1_ops),_t.mkdev("/dev/tty",_t.makedev(5,0)),_t.mkdev("/dev/tty1",_t.makedev(6,0));var e=ct();_t.createDevice("/dev","random",e),_t.createDevice("/dev","urandom",e),_t.mkdir("/dev/shm"),_t.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){_t.mkdir("/proc");var e=_t.mkdir("/proc/self");_t.mkdir("/proc/self/fd"),_t.mount({mount:function(){var t=_t.createNode(e,"fd",16895,73);return t.node_ops={lookup:function(e,t){var r=+t,n=_t.getStream(r);if(!n)throw new _t.ErrnoError(8);var o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},t}},{},"/proc/self/fd")},createStandardStreams:function(){r.stdin?_t.createDevice("/dev","stdin",r.stdin):_t.symlink("/dev/tty","/dev/stdin"),r.stdout?_t.createDevice("/dev","stdout",null,r.stdout):_t.symlink("/dev/tty","/dev/stdout"),r.stderr?_t.createDevice("/dev","stderr",null,r.stderr):_t.symlink("/dev/tty1","/dev/stderr");var e=_t.open("/dev/stdin",0),t=_t.open("/dev/stdout",1),n=_t.open("/dev/stderr",1);M(0===e.fd,"invalid handle for stdin ("+e.fd+")"),M(1===t.fd,"invalid handle for stdout ("+t.fd+")"),M(2===n.fd,"invalid handle for stderr ("+n.fd+")")},ensureErrnoError:function(){_t.ErrnoError||(_t.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){for(var t in this.errno=e,Et)if(Et[t]===e){this.code=t;break}},this.setErrno(e),this.message=pt[e],this.stack&&(Object.defineProperty(this,"stack",{value:(new Error).stack,writable:!0}),this.stack=Qe(this.stack))},_t.ErrnoError.prototype=new Error,_t.ErrnoError.prototype.constructor=_t.ErrnoError,[44].forEach((function(e){_t.genericErrors[e]=new _t.ErrnoError(e),_t.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){_t.ensureErrnoError(),_t.nameTable=new Array(4096),_t.mount(ft,{},"/"),_t.createDefaultDirectories(),_t.createDefaultDevices(),_t.createSpecialDirectories(),_t.filesystems={MEMFS:ft}},init:function(e,t,n){M(!_t.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),_t.init.initialized=!0,_t.ensureErrnoError(),r.stdin=e||r.stdin,r.stdout=t||r.stdout,r.stderr=n||r.stderr,_t.createStandardStreams()},quit:function(){_t.init.initialized=!1;var e=r._fflush;e&&e(0);for(var t=0;t<_t.streams.length;t++){var n=_t.streams[t];n&&_t.close(n)}},getMode:function(e,t){var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:function(e,t){var r=_t.analyzePath(e,t);return r.exists?r.object:null},analyzePath:function(e,t){try{e=(n=_t.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=_t.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=st.basename(e),n=_t.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:function(e,t,r,n){e="string"==typeof e?e:_t.getPath(e);for(var o=t.split("/").reverse();o.length;){var i=o.pop();if(i){var a=st.join2(e,i);try{_t.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,t,r,n,o){var i=st.join2("string"==typeof e?e:_t.getPath(e),t),a=_t.getMode(n,o);return _t.create(i,a)},createDataFile:function(e,t,r,n,o,i){var a=t?st.join2("string"==typeof e?e:_t.getPath(e),t):e,s=_t.getMode(n,o),c=_t.create(a,s);if(r){if("string"==typeof r){for(var u=new Array(r.length),l=0,d=r.length;l<d;++l)u[l]=r.charCodeAt(l);r=u}_t.chmod(c,146|s);var f=_t.open(c,577);_t.write(f,r,0,r.length,0,i),_t.close(f),_t.chmod(c,s)}return c},createDevice:function(e,t,r,n){var o=st.join2("string"==typeof e?e:_t.getPath(e),t),i=_t.getMode(!!r,!!n);_t.createDevice.major||(_t.createDevice.major=64);var a=_t.makedev(_t.createDevice.major++,0);return _t.registerDevice(a,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,t,n,o,i){for(var a=0,s=0;s<o;s++){var c;try{c=r()}catch(e){throw new _t.ErrnoError(29)}if(void 0===c&&0===a)throw new _t.ErrnoError(6);if(null==c)break;a++,t[n+s]=c}return a&&(e.node.timestamp=Date.now()),a},write:function(e,t,r,o,i){for(var a=0;a<o;a++)try{n(t[r+a])}catch(e){throw new _t.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),a}}),_t.mkdev(o,i,a)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!l)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=ha(l(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new _t.ErrnoError(29)}},createLazyFile:function(e,t,r,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),o=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,i=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(e){var t=e*a,o=(e+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=function(e,t){if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",r,!1),n!==a&&o.setRequestHeader("Range","bytes="+e+"-"+t),"undefined"!=typeof Uint8Array&&(o.responseType="arraybuffer"),o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+r+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):ha(o.responseText||"",!0)}(t,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,g("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!s)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var c={isDevice:!1,contents:a}}else c={isDevice:!1,url:r};var u=_t.createFile(e,t,c,n,o);c.contents?u.contents=c.contents:c.url&&(u.contents=null,u.url=c.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var l={};return Object.keys(u.stream_ops).forEach((function(e){var t=u.stream_ops[e];l[e]=function(){return _t.forceLoadFile(u),t.apply(null,arguments)}})),l.read=function(e,t,r,n,o){_t.forceLoadFile(u);var i=e.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(M(a>=0),i.slice)for(var s=0;s<a;s++)t[r+s]=i[o+s];else for(s=0;s<a;s++)t[r+s]=i.get(o+s);return a},u.stream_ops=l,u},createPreloadedFile:function(e,t,n,o,i,a,s,c,u,l){Browser.init();var d=t?ut.resolve(st.join2(e,t)):e,f=ye("cp "+d);function p(n){function p(r){l&&l(),c||_t.createDataFile(e,t,r,o,i,u),a&&a(),ve(f)}var E=!1;r.preloadPlugins.forEach((function(e){E||e.canHandle(d)&&(e.handle(n,d,p,(function(){s&&s(),ve(f)})),E=!0)})),E||p(n)}De(f),"string"==typeof n?Browser.asyncLoad(n,(function(e){p(e)}),s):p(n)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,t,r){t=t||function(){},r=r||function(){};var n=_t.indexedDB();try{var o=n.open(_t.DB_NAME(),_t.DB_VERSION)}catch(e){return r(e)}o.onupgradeneeded=function(){g("creating db"),o.result.createObjectStore(_t.DB_STORE_NAME)},o.onsuccess=function(){var n=o.result.transaction([_t.DB_STORE_NAME],"readwrite"),i=n.objectStore(_t.DB_STORE_NAME),a=0,s=0,c=e.length;function u(){0==s?t():r()}e.forEach((function(e){var t=i.put(_t.analyzePath(e).object.contents,e);t.onsuccess=function(){++a+s==c&&u()},t.onerror=function(){s++,a+s==c&&u()}})),n.onerror=r},o.onerror=r},loadFilesFromDB:function(e,t,r){t=t||function(){},r=r||function(){};var n=_t.indexedDB();try{var o=n.open(_t.DB_NAME(),_t.DB_VERSION)}catch(e){return r(e)}o.onupgradeneeded=r,o.onsuccess=function(){var n=o.result;try{var i=n.transaction([_t.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var a=i.objectStore(_t.DB_STORE_NAME),s=0,c=0,u=e.length;function l(){0==c?t():r()}e.forEach((function(e){var t=a.get(e);t.onsuccess=function(){_t.analyzePath(e).exists&&_t.unlink(e),_t.createDataFile(st.dirname(e),st.basename(e),t.result,!0,!0,!0),++s+c==u&&l()},t.onerror=function(){c++,s+c==u&&l()}})),i.onerror=r},o.onerror=r},absolutePath:function(){be("FS.absolutePath has been removed; use PATH_FS.resolve instead")},createFolder:function(){be("FS.createFolder has been removed; use FS.mkdir instead")},createLink:function(){be("FS.createLink has been removed; use FS.symlink instead")},joinPath:function(){be("FS.joinPath has been removed; use PATH.join instead")},mmapAlloc:function(){be("FS.mmapAlloc has been replaced by the top level function mmapAlloc")},standardizePath:function(){be("FS.standardizePath has been removed; use PATH.normalize instead")}};r.FS=_t;var mt={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt:function(e,t,r){if("/"===t[0])return t;var n;if(-100===e)n=_t.cwd();else{var o=_t.getStream(e);if(!o)throw new _t.ErrnoError(8);n=o.path}if(0==t.length){if(!r)throw new _t.ErrnoError(44);return n}return st.join2(n,t)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&st.normalize(t)!==st.normalize(_t.getPath(e.node)))return-54;throw e}return j[r>>2]=n.dev,j[r+4>>2]=0,j[r+8>>2]=n.ino,j[r+12>>2]=n.mode,j[r+16>>2]=n.nlink,j[r+20>>2]=n.uid,j[r+24>>2]=n.gid,j[r+28>>2]=n.rdev,j[r+32>>2]=0,ke=[n.size>>>0,(Ie=n.size,+Math.abs(Ie)>=1?Ie>0?(0|Math.min(+Math.floor(Ie/4294967296),4294967295))>>>0:~~+Math.ceil((Ie-+(~~Ie>>>0))/4294967296)>>>0:0)],j[r+40>>2]=ke[0],j[r+44>>2]=ke[1],j[r+48>>2]=4096,j[r+52>>2]=n.blocks,j[r+56>>2]=n.atime.getTime()/1e3|0,j[r+60>>2]=0,j[r+64>>2]=n.mtime.getTime()/1e3|0,j[r+68>>2]=0,j[r+72>>2]=n.ctime.getTime()/1e3|0,j[r+76>>2]=0,ke=[n.ino>>>0,(Ie=n.ino,+Math.abs(Ie)>=1?Ie>0?(0|Math.min(+Math.floor(Ie/4294967296),4294967295))>>>0:~~+Math.ceil((Ie-+(~~Ie>>>0))/4294967296)>>>0:0)],j[r+80>>2]=ke[0],j[r+84>>2]=ke[1],0},doMsync:function(e,t,r,n,o){var i=X.slice(e,e+r);_t.msync(t,i,o,r,n)},doMkdir:function(e,t){return"/"===(e=st.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),_t.mkdir(e,t,0),0},doMknod:function(e,t,r){switch(61440&t){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return _t.mknod(e,t,r),0},doReadlink:function(e,t,r){if(r<=0)return-28;var n=_t.readlink(e),o=Math.min(r,Y(n)),i=N[t+o];return V(n,t,r+1),N[t+o]=i,o},doAccess:function(e,t){if(-8&t)return-28;var r;if(!(r=_t.lookupPath(e,{follow:!0}).node))return-44;var n="";return 4&t&&(n+="r"),2&t&&(n+="w"),1&t&&(n+="x"),n&&_t.nodePermissions(r,n)?-2:0},doDup:function(e,t,r){var n=_t.getStream(r);return n&&_t.close(n),_t.open(e,t,0,r,r).fd},doReadv:function(e,t,r,n){for(var o=0,i=0;i<r;i++){var a=j[t+8*i>>2],s=j[t+(8*i+4)>>2],c=_t.read(e,N,a,s,n);if(c<0)return-1;if(o+=c,c<s)break}return o},doWritev:function(e,t,r,n){for(var o=0,i=0;i<r;i++){var a=j[t+8*i>>2],s=j[t+(8*i+4)>>2],c=_t.write(e,N,a,s,n);if(c<0)return-1;o+=c}return o},varargs:void 0,get:function(){return M(null!=mt.varargs),mt.varargs+=4,j[mt.varargs-4>>2]},getStr:function(e){return W(e)},getStreamFromFD:function(e){var t=_t.getStream(e);if(!t)throw new _t.ErrnoError(8);return t},get64:function(e,t){return M(e>=0?0===t:-1===t),e}};function gt(e,t){try{return e=mt.getStr(e),mt.doAccess(e,t)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Tt(e){try{var t=mt.getStreamFromFD(e);return _t.open(t.path,t.flags,0).fd}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function ht(e,t,r){mt.varargs=r;try{var n=mt.getStreamFromFD(e);switch(t){case 0:return(o=mt.get())<0?-28:_t.open(n.path,n.flags,0,o).fd;case 1:case 2:case 13:case 14:return 0;case 3:return n.flags;case 4:var o=mt.get();return n.flags|=o,0;case 12:o=mt.get();return U[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return Ve(28),-1}}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Ot(e,t){try{var r=mt.getStreamFromFD(e);return mt.doStat(_t.stat,r.path,t)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function wt(e,t){try{if(0===t)return-28;var r=_t.cwd();return t<Y(r)+1?-68:(V(r,e,t),e)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function yt(e,t,r){try{var n=mt.getStreamFromFD(e);n.getdents||(n.getdents=_t.readdir(n.path));for(var o=280,i=0,a=_t.llseek(n,0,1),s=Math.floor(a/o);s<n.getdents.length&&i+o<=r;){var c,u,l=n.getdents[s];if("."===l[0])c=1,u=4;else{var d=_t.lookupNode(n.node,l);c=d.id,u=_t.isChrdev(d.mode)?2:_t.isDir(d.mode)?4:_t.isLink(d.mode)?10:8}ke=[c>>>0,(Ie=c,+Math.abs(Ie)>=1?Ie>0?(0|Math.min(+Math.floor(Ie/4294967296),4294967295))>>>0:~~+Math.ceil((Ie-+(~~Ie>>>0))/4294967296)>>>0:0)],j[t+i>>2]=ke[0],j[t+i+4>>2]=ke[1],ke=[(s+1)*o>>>0,(Ie=(s+1)*o,+Math.abs(Ie)>=1?Ie>0?(0|Math.min(+Math.floor(Ie/4294967296),4294967295))>>>0:~~+Math.ceil((Ie-+(~~Ie>>>0))/4294967296)>>>0:0)],j[t+i+8>>2]=ke[0],j[t+i+12>>2]=ke[1],U[t+i+16>>1]=280,N[t+i+18>>0]=u,V(l,t+i+19,256),i+=o,s+=1}return _t.llseek(n,s*o,0),i}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Dt(e,t,r){mt.varargs=r;try{var n=mt.getStreamFromFD(e);switch(t){case 21509:case 21505:case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:case 21523:case 21524:return n.tty?0:-59;case 21519:if(!n.tty)return-59;var o=mt.get();return j[o>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:o=mt.get();return _t.ioctl(n,t,o);default:be("bad ioctl syscall "+t)}}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function vt(e,t){try{return e=mt.getStr(e),mt.doStat(_t.lstat,e,t)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function bt(e,t){try{return e=mt.getStr(e),mt.doMkdir(e,t)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Rt(e,t,r){mt.varargs=r;try{var n=mt.getStr(e),o=r?mt.get():0;return _t.open(n,t,o).fd}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function At(e,t,r){try{return e=mt.getStr(e),mt.doReadlink(e,t,r)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function St(e,t){try{return e=mt.getStr(e),t=mt.getStr(t),_t.rename(e,t),0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Pt(e){try{return e=mt.getStr(e),_t.rmdir(e),0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Mt(e,t){try{return e=mt.getStr(e),mt.doStat(_t.stat,e,t)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function Ft(e){try{return e=mt.getStr(e),_t.unlink(e),0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),-e.errno}}function xt(){be()}function It(e){be("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")}function kt(){be("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")}function Nt(e,t){be("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")}function Xt(e){var t=e.getExtension("ANGLE_instanced_arrays");if(t)return e.vertexAttribDivisor=function(e,r){t.vertexAttribDivisorANGLE(e,r)},e.drawArraysInstanced=function(e,r,n,o){t.drawArraysInstancedANGLE(e,r,n,o)},e.drawElementsInstanced=function(e,r,n,o,i){t.drawElementsInstancedANGLE(e,r,n,o,i)},1}function Ut(e){var t=e.getExtension("OES_vertex_array_object");if(t)return e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(e){t.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){t.bindVertexArrayOES(e)},e.isVertexArray=function(e){return t.isVertexArrayOES(e)},1}function Ct(e){var t=e.getExtension("WEBGL_draw_buffers");if(t)return e.drawBuffers=function(e,r){t.drawBuffersWEBGL(e,r)},1}function jt(e){return!!(e.multiDrawWebgl=e.getExtension("WEBGL_multi_draw"))}r.SYSCALLS=mt,r.___sys_access=gt,r.___sys_dup=Tt,r.___sys_fcntl64=ht,r.___sys_fstat64=Ot,r.___sys_getcwd=wt,r.___sys_getdents64=yt,r.___sys_ioctl=Dt,r.___sys_lstat64=vt,r.___sys_mkdir=bt,r.___sys_open=Rt,r.___sys_readlink=At,r.___sys_rename=St,r.___sys_rmdir=Pt,r.___sys_stat64=Mt,r.___sys_unlink=Ft,r._abort=xt,r._dlclose=It,r._dlerror=kt,r._dlopen=Nt,r.__webgl_enable_ANGLE_instanced_arrays=Xt,r.__webgl_enable_OES_vertex_array_object=Ut,r.__webgl_enable_WEBGL_draw_buffers=Ct,r.__webgl_enable_WEBGL_multi_draw=jt;var Lt={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],uniforms:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},timerQueriesEXT:[],programInfos:{},stringCache:{},unpackAlignment:4,recordError:function(e){Lt.lastError||(Lt.lastError=e)},getNewId:function(e){for(var t=Lt.counter++,r=e.length;r<t;r++)e[r]=null;return t},getSource:function(e,t,r,n){for(var o="",i=0;i<t;++i){var a=n?j[n+4*i>>2]:-1;o+=W(j[r+4*i>>2],a<0?void 0:a)}return o},createContext:function(e,t){var r=e.getContext("webgl",t);return r?Lt.registerContext(r,t):0},registerContext:function(e,t){var r=Lt.getNewId(Lt.contexts),n={handle:r,attributes:t,version:t.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=n),Lt.contexts[r]=n,(void 0===t.enableExtensionsByDefault||t.enableExtensionsByDefault)&&Lt.initExtensions(n),r},makeContextCurrent:function(e){return Lt.currentContext=Lt.contexts[e],r.ctx=fa=Lt.currentContext&&Lt.currentContext.GLctx,!(e&&!fa)},getContext:function(e){return Lt.contexts[e]},deleteContext:function(e){Lt.currentContext===Lt.contexts[e]&&(Lt.currentContext=null),"object"==typeof JSEvents&&JSEvents.removeAllHandlersOnTarget(Lt.contexts[e].GLctx.canvas),Lt.contexts[e]&&Lt.contexts[e].GLctx.canvas&&(Lt.contexts[e].GLctx.canvas.GLctxObject=void 0),Lt.contexts[e]=null},initExtensions:function(e){if(e||(e=Lt.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var t=e.GLctx;Xt(t),Ut(t),Ct(t),t.disjointTimerQueryExt=t.getExtension("EXT_disjoint_timer_query"),jt(t),(t.getSupportedExtensions()||[]).forEach((function(e){e.indexOf("lose_context")<0&&e.indexOf("debug")<0&&t.getExtension(e)}))}},populateUniformTable:function(e){for(var t=Lt.programs[e],r=Lt.programInfos[e]={uniforms:{},maxUniformLength:0,maxAttributeLength:-1,maxUniformBlockNameLength:-1},n=r.uniforms,o=fa.getProgramParameter(t,35718),i=0;i<o;++i){var a=fa.getActiveUniform(t,i),s=a.name;r.maxUniformLength=Math.max(r.maxUniformLength,s.length+1),"]"==s.slice(-1)&&(s=s.slice(0,s.lastIndexOf("[")));var c=fa.getUniformLocation(t,s);if(c){var u=Lt.getNewId(Lt.uniforms);n[s]=[a.size,u],Lt.uniforms[u]=c;for(var l=1;l<a.size;++l){var d=s+"["+l+"]";c=fa.getUniformLocation(t,d),u=Lt.getNewId(Lt.uniforms),Lt.uniforms[u]=c}}}}};function Ht(e){fa.activeTexture(e)}function Qt(e,t){fa.attachShader(Lt.programs[e],Lt.shaders[t])}function Gt(e,t){fa.disjointTimerQueryExt.beginQueryEXT(e,Lt.timerQueriesEXT[t])}function Bt(e,t,r){fa.bindAttribLocation(Lt.programs[e],t,W(r))}function Wt(e,t){fa.bindBuffer(e,Lt.buffers[t])}function zt(e,t){fa.bindFramebuffer(e,Lt.framebuffers[t])}function Vt(e,t){fa.bindRenderbuffer(e,Lt.renderbuffers[t])}function Yt(e,t){fa.bindTexture(e,Lt.textures[t])}function qt(e){fa.bindVertexArray(Lt.vaos[e])}function Kt(e,t,r,n){fa.blendColor(e,t,r,n)}function Jt(e){fa.blendEquation(e)}function Zt(e,t){fa.blendEquationSeparate(e,t)}function $t(e,t){fa.blendFunc(e,t)}function er(e,t,r,n){fa.blendFuncSeparate(e,t,r,n)}function tr(e,t,r,n){fa.bufferData(e,r?X.subarray(r,r+t):t,n)}function rr(e,t,r,n){fa.bufferSubData(e,t,X.subarray(n,n+r))}function nr(e){return fa.checkFramebufferStatus(e)}function or(e){fa.clear(e)}function ir(e,t,r,n){fa.clearColor(e,t,r,n)}function ar(e){fa.clearDepth(e)}function sr(e){fa.clearStencil(e)}function cr(e,t,r,n){fa.colorMask(!!e,!!t,!!r,!!n)}function ur(e){fa.compileShader(Lt.shaders[e])}function lr(e,t,r,n,o,i,a,s){fa.compressedTexImage2D(e,t,r,n,o,i,s?X.subarray(s,s+a):null)}function dr(e,t,r,n,o,i,a,s,c){fa.compressedTexSubImage2D(e,t,r,n,o,i,a,c?X.subarray(c,c+s):null)}function fr(e,t,r,n,o,i,a,s){fa.copyTexImage2D(e,t,r,n,o,i,a,s)}function pr(e,t,r,n,o,i,a,s){fa.copyTexSubImage2D(e,t,r,n,o,i,a,s)}function Er(){var e=Lt.getNewId(Lt.programs),t=fa.createProgram();return t.name=e,Lt.programs[e]=t,e}function _r(e){var t=Lt.getNewId(Lt.shaders);return Lt.shaders[t]=fa.createShader(e),t}function mr(e){fa.cullFace(e)}function gr(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2],o=Lt.buffers[n];o&&(fa.deleteBuffer(o),o.name=0,Lt.buffers[n]=null)}}function Tr(e,t){for(var r=0;r<e;++r){var n=j[t+4*r>>2],o=Lt.framebuffers[n];o&&(fa.deleteFramebuffer(o),o.name=0,Lt.framebuffers[n]=null)}}function hr(e){if(e){var t=Lt.programs[e];t?(fa.deleteProgram(t),t.name=0,Lt.programs[e]=null,Lt.programInfos[e]=null):Lt.recordError(1281)}}function Or(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2],o=Lt.timerQueriesEXT[n];o&&(fa.disjointTimerQueryExt.deleteQueryEXT(o),Lt.timerQueriesEXT[n]=null)}}function wr(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2],o=Lt.renderbuffers[n];o&&(fa.deleteRenderbuffer(o),o.name=0,Lt.renderbuffers[n]=null)}}function yr(e){if(e){var t=Lt.shaders[e];t?(fa.deleteShader(t),Lt.shaders[e]=null):Lt.recordError(1281)}}function Dr(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2],o=Lt.textures[n];o&&(fa.deleteTexture(o),o.name=0,Lt.textures[n]=null)}}function vr(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2];fa.deleteVertexArray(Lt.vaos[n]),Lt.vaos[n]=null}}function br(e){fa.depthFunc(e)}function Rr(e){fa.depthMask(!!e)}function Ar(e,t){fa.depthRange(e,t)}function Sr(e,t){fa.detachShader(Lt.programs[e],Lt.shaders[t])}function Pr(e){fa.disable(e)}function Mr(e){fa.disableVertexAttribArray(e)}function Fr(e,t,r){fa.drawArrays(e,t,r)}function xr(e,t,r,n){fa.drawArraysInstanced(e,t,r,n)}r.GL=Lt,r._emscripten_glActiveTexture=Ht,r._emscripten_glAttachShader=Qt,r._emscripten_glBeginQueryEXT=Gt,r._emscripten_glBindAttribLocation=Bt,r._emscripten_glBindBuffer=Wt,r._emscripten_glBindFramebuffer=zt,r._emscripten_glBindRenderbuffer=Vt,r._emscripten_glBindTexture=Yt,r._emscripten_glBindVertexArrayOES=qt,r._emscripten_glBlendColor=Kt,r._emscripten_glBlendEquation=Jt,r._emscripten_glBlendEquationSeparate=Zt,r._emscripten_glBlendFunc=$t,r._emscripten_glBlendFuncSeparate=er,r._emscripten_glBufferData=tr,r._emscripten_glBufferSubData=rr,r._emscripten_glCheckFramebufferStatus=nr,r._emscripten_glClear=or,r._emscripten_glClearColor=ir,r._emscripten_glClearDepthf=ar,r._emscripten_glClearStencil=sr,r._emscripten_glColorMask=cr,r._emscripten_glCompileShader=ur,r._emscripten_glCompressedTexImage2D=lr,r._emscripten_glCompressedTexSubImage2D=dr,r._emscripten_glCopyTexImage2D=fr,r._emscripten_glCopyTexSubImage2D=pr,r._emscripten_glCreateProgram=Er,r._emscripten_glCreateShader=_r,r._emscripten_glCullFace=mr,r._emscripten_glDeleteBuffers=gr,r._emscripten_glDeleteFramebuffers=Tr,r._emscripten_glDeleteProgram=hr,r._emscripten_glDeleteQueriesEXT=Or,r._emscripten_glDeleteRenderbuffers=wr,r._emscripten_glDeleteShader=yr,r._emscripten_glDeleteTextures=Dr,r._emscripten_glDeleteVertexArraysOES=vr,r._emscripten_glDepthFunc=br,r._emscripten_glDepthMask=Rr,r._emscripten_glDepthRangef=Ar,r._emscripten_glDetachShader=Sr,r._emscripten_glDisable=Pr,r._emscripten_glDisableVertexAttribArray=Mr,r._emscripten_glDrawArrays=Fr,r._emscripten_glDrawArraysInstancedANGLE=xr;var Ir=[];function kr(e,t){for(var r=Ir[e],n=0;n<e;n++)r[n]=j[t+4*n>>2];fa.drawBuffers(r)}function Nr(e,t,r,n){fa.drawElements(e,t,r,n)}function Xr(e,t,r,n,o){fa.drawElementsInstanced(e,t,r,n,o)}function Ur(e){fa.enable(e)}function Cr(e){fa.enableVertexAttribArray(e)}function jr(e){fa.disjointTimerQueryExt.endQueryEXT(e)}function Lr(){fa.finish()}function Hr(){fa.flush()}function Qr(e,t,r,n){fa.framebufferRenderbuffer(e,t,r,Lt.renderbuffers[n])}function Gr(e,t,r,n,o){fa.framebufferTexture2D(e,t,r,Lt.textures[n],o)}function Br(e){fa.frontFace(e)}function Wr(e,t,r,n){for(var o=0;o<e;o++){var i=fa[r](),a=i&&Lt.getNewId(n);i?(i.name=a,n[a]=i):Lt.recordError(1282),j[t+4*o>>2]=a}}function zr(e,t){Wr(e,t,"createBuffer",Lt.buffers)}function Vr(e,t){Wr(e,t,"createFramebuffer",Lt.framebuffers)}function Yr(e,t){for(var r=0;r<e;r++){var n=fa.disjointTimerQueryExt.createQueryEXT();if(!n){for(Lt.recordError(1282);r<e;)j[t+4*r++>>2]=0;return}var o=Lt.getNewId(Lt.timerQueriesEXT);n.name=o,Lt.timerQueriesEXT[o]=n,j[t+4*r>>2]=o}}function qr(e,t){Wr(e,t,"createRenderbuffer",Lt.renderbuffers)}function Kr(e,t){Wr(e,t,"createTexture",Lt.textures)}function Jr(e,t){Wr(e,t,"createVertexArray",Lt.vaos)}function Zr(e){fa.generateMipmap(e)}function $r(e,t,r,n,o,i,a,s){t=Lt.programs[t];var c=fa[e](t,r);if(c){var u=s&&V(c.name,s,n);o&&(j[o>>2]=u),i&&(j[i>>2]=c.size),a&&(j[a>>2]=c.type)}}function en(e,t,r,n,o,i,a){$r("getActiveAttrib",e,t,r,n,o,i,a)}function tn(e,t,r,n,o,i,a){$r("getActiveUniform",e,t,r,n,o,i,a)}function rn(e,t,r,n){var o=fa.getAttachedShaders(Lt.programs[e]),i=o.length;i>t&&(i=t),j[r>>2]=i;for(var a=0;a<i;++a){var s=Lt.shaders.indexOf(o[a]);j[n+4*a>>2]=s}}function nn(e,t){return fa.getAttribLocation(Lt.programs[e],W(t))}function on(e){return L[e>>2]+4294967296*j[e+4>>2]}function an(e){return L[e>>2]+4294967296*L[e+4>>2]}function sn(e,t){L[e>>2]=t,L[e+4>>2]=(t-L[e>>2])/4294967296;var r=t>=0?an(e):on(e);r!=t&&w("writeI53ToI64() out of range: serialized JS Number "+t+" to Wasm heap as bytes lo=0x"+L[e>>2].toString(16)+", hi=0x"+L[e+4>>2].toString(16)+", which deserializes back to "+r+" instead!")}function cn(e,t,r){if(t){var n=void 0;switch(e){case 36346:n=1;break;case 36344:return void(0!=r&&1!=r&&Lt.recordError(1280));case 36345:n=0;break;case 34466:var o=fa.getParameter(34467);n=o?o.length:0}if(void 0===n){var i=fa.getParameter(e);switch(typeof i){case"number":n=i;break;case"boolean":n=i?1:0;break;case"string":return void Lt.recordError(1280);case"object":if(null===i)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 34068:n=0;break;default:return void Lt.recordError(1280)}else{if(i instanceof Float32Array||i instanceof Uint32Array||i instanceof Int32Array||i instanceof Array){for(var a=0;a<i.length;++a)switch(r){case 0:j[t+4*a>>2]=i[a];break;case 2:H[t+4*a>>2]=i[a];break;case 4:N[t+a>>0]=i[a]?1:0}return}try{n=0|i.name}catch(t){return Lt.recordError(1280),void T("GL_INVALID_ENUM in glGet"+r+"v: Unknown object returned from WebGL getParameter("+e+")! (error: "+t+")")}}break;default:return Lt.recordError(1280),void T("GL_INVALID_ENUM in glGet"+r+"v: Native code calling glGet"+r+"v("+e+") and it returns "+i+" of type "+typeof i+"!")}}switch(r){case 1:sn(t,n);break;case 0:j[t>>2]=n;break;case 2:H[t>>2]=n;break;case 4:N[t>>0]=n?1:0}}else Lt.recordError(1281)}function un(e,t){cn(e,t,4)}function ln(e,t,r){r?j[r>>2]=fa.getBufferParameter(e,t):Lt.recordError(1281)}function dn(){var e=fa.getError()||Lt.lastError;return Lt.lastError=0,e}function fn(e,t){cn(e,t,2)}function pn(e,t,r,n){var o=fa.getFramebufferAttachmentParameter(e,t,r);(o instanceof WebGLRenderbuffer||o instanceof WebGLTexture)&&(o=0|o.name),j[n>>2]=o}function En(e,t){cn(e,t,0)}function _n(e,t,r,n){var o=fa.getProgramInfoLog(Lt.programs[e]);null===o&&(o="(unknown error)");var i=t>0&&n?V(o,n,t):0;r&&(j[r>>2]=i)}function mn(e,t,r){if(r)if(e>=Lt.counter)Lt.recordError(1281);else{var n=Lt.programInfos[e];if(n)if(35716==t){var o=fa.getProgramInfoLog(Lt.programs[e]);null===o&&(o="(unknown error)"),j[r>>2]=o.length+1}else if(35719==t)j[r>>2]=n.maxUniformLength;else if(35722==t){if(-1==n.maxAttributeLength){e=Lt.programs[e];var i=fa.getProgramParameter(e,35721);n.maxAttributeLength=0;for(var a=0;a<i;++a){var s=fa.getActiveAttrib(e,a);n.maxAttributeLength=Math.max(n.maxAttributeLength,s.name.length+1)}}j[r>>2]=n.maxAttributeLength}else if(35381==t){if(-1==n.maxUniformBlockNameLength){e=Lt.programs[e];var c=fa.getProgramParameter(e,35382);n.maxUniformBlockNameLength=0;for(a=0;a<c;++a){var u=fa.getActiveUniformBlockName(e,a);n.maxUniformBlockNameLength=Math.max(n.maxUniformBlockNameLength,u.length+1)}}j[r>>2]=n.maxUniformBlockNameLength}else j[r>>2]=fa.getProgramParameter(Lt.programs[e],t);else Lt.recordError(1282)}else Lt.recordError(1281)}function gn(e,t,r){if(r){var n=Lt.timerQueriesEXT[e],o=fa.disjointTimerQueryExt.getQueryObjectEXT(n,t);sn(r,"boolean"==typeof o?o?1:0:o)}else Lt.recordError(1281)}function Tn(e,t,r){if(r){var n,o=Lt.timerQueriesEXT[e],i=fa.disjointTimerQueryExt.getQueryObjectEXT(o,t);n="boolean"==typeof i?i?1:0:i,j[r>>2]=n}else Lt.recordError(1281)}function hn(e,t,r){if(r){var n=Lt.timerQueriesEXT[e],o=fa.disjointTimerQueryExt.getQueryObjectEXT(n,t);sn(r,"boolean"==typeof o?o?1:0:o)}else Lt.recordError(1281)}function On(e,t,r){if(r){var n,o=Lt.timerQueriesEXT[e],i=fa.disjointTimerQueryExt.getQueryObjectEXT(o,t);n="boolean"==typeof i?i?1:0:i,j[r>>2]=n}else Lt.recordError(1281)}function wn(e,t,r){r?j[r>>2]=fa.disjointTimerQueryExt.getQueryEXT(e,t):Lt.recordError(1281)}function yn(e,t,r){r?j[r>>2]=fa.getRenderbufferParameter(e,t):Lt.recordError(1281)}function Dn(e,t,r,n){var o=fa.getShaderInfoLog(Lt.shaders[e]);null===o&&(o="(unknown error)");var i=t>0&&n?V(o,n,t):0;r&&(j[r>>2]=i)}function vn(e,t,r,n){var o=fa.getShaderPrecisionFormat(e,t);j[r>>2]=o.rangeMin,j[r+4>>2]=o.rangeMax,j[n>>2]=o.precision}function bn(e,t,r,n){var o=fa.getShaderSource(Lt.shaders[e]);if(o){var i=t>0&&n?V(o,n,t):0;r&&(j[r>>2]=i)}}function Rn(e,t,r){if(r)if(35716==t){var n=fa.getShaderInfoLog(Lt.shaders[e]);null===n&&(n="(unknown error)");var o=n?n.length+1:0;j[r>>2]=o}else if(35720==t){var i=fa.getShaderSource(Lt.shaders[e]),a=i?i.length+1:0;j[r>>2]=a}else j[r>>2]=fa.getShaderParameter(Lt.shaders[e],t);else Lt.recordError(1281)}function An(e){var t=Y(e)+1,r=Ia(t);return V(e,r,t),r}function Sn(e){if(Lt.stringCache[e])return Lt.stringCache[e];var t;switch(e){case 7939:var r=fa.getSupportedExtensions()||[];t=An((r=r.concat(r.map((function(e){return"GL_"+e})))).join(" "));break;case 7936:case 7937:case 37445:case 37446:var n=fa.getParameter(e);n||Lt.recordError(1280),t=An(n);break;case 7938:var o=fa.getParameter(7938);t=An(o="OpenGL ES 2.0 ("+o+")");break;case 35724:var i=fa.getParameter(35724),a=i.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==a&&(3==a[1].length&&(a[1]=a[1]+"0"),i="OpenGL ES GLSL ES "+a[1]+" ("+i+")"),t=An(i);break;default:return Lt.recordError(1280),0}return Lt.stringCache[e]=t,t}function Pn(e,t,r){r?H[r>>2]=fa.getTexParameter(e,t):Lt.recordError(1281)}function Mn(e,t,r){r?j[r>>2]=fa.getTexParameter(e,t):Lt.recordError(1281)}function Fn(e){return parseInt(e)}function xn(e,t){var r=0;if("]"==(t=W(t))[t.length-1]){var n=t.lastIndexOf("[");r="]"!=t[n+1]?Fn(t.slice(n+1)):0,t=t.slice(0,n)}var o=Lt.programInfos[e]&&Lt.programInfos[e].uniforms[t];return o&&r>=0&&r<o[0]?o[1]+r:-1}function In(e,t,r,n){if(r){var o=fa.getUniform(Lt.programs[e],Lt.uniforms[t]);if("number"==typeof o||"boolean"==typeof o)switch(n){case 0:j[r>>2]=o;break;case 2:H[r>>2]=o}else for(var i=0;i<o.length;i++)switch(n){case 0:j[r+4*i>>2]=o[i];break;case 2:H[r+4*i>>2]=o[i]}}else Lt.recordError(1281)}function kn(e,t,r){In(e,t,r,2)}function Nn(e,t,r){In(e,t,r,0)}function Xn(e,t,r){r?j[r>>2]=fa.getVertexAttribOffset(e,t):Lt.recordError(1281)}function Un(e,t,r,n){if(r){var o=fa.getVertexAttrib(e,t);if(34975==t)j[r>>2]=o&&o.name;else if("number"==typeof o||"boolean"==typeof o)switch(n){case 0:j[r>>2]=o;break;case 2:H[r>>2]=o;break;case 5:j[r>>2]=Math.fround(o)}else for(var i=0;i<o.length;i++)switch(n){case 0:j[r+4*i>>2]=o[i];break;case 2:H[r+4*i>>2]=o[i];break;case 5:j[r+4*i>>2]=Math.fround(o[i])}}else Lt.recordError(1281)}function Cn(e,t,r){Un(e,t,r,2)}function jn(e,t,r){Un(e,t,r,5)}function Ln(e,t){fa.hint(e,t)}function Hn(e){var t=Lt.buffers[e];return t?fa.isBuffer(t):0}function Qn(e){return fa.isEnabled(e)}function Gn(e){var t=Lt.framebuffers[e];return t?fa.isFramebuffer(t):0}function Bn(e){return(e=Lt.programs[e])?fa.isProgram(e):0}function Wn(e){var t=Lt.timerQueriesEXT[e];return t?fa.disjointTimerQueryExt.isQueryEXT(t):0}function zn(e){var t=Lt.renderbuffers[e];return t?fa.isRenderbuffer(t):0}function Vn(e){var t=Lt.shaders[e];return t?fa.isShader(t):0}function Yn(e){var t=Lt.textures[e];return t?fa.isTexture(t):0}function qn(e){var t=Lt.vaos[e];return t?fa.isVertexArray(t):0}function Kn(e){fa.lineWidth(e)}function Jn(e){fa.linkProgram(Lt.programs[e]),Lt.populateUniformTable(e)}function Zn(e,t){3317==e&&(Lt.unpackAlignment=t),fa.pixelStorei(e,t)}function $n(e,t){fa.polygonOffset(e,t)}function eo(e,t){fa.disjointTimerQueryExt.queryCounterEXT(Lt.timerQueriesEXT[e],t)}function to(e,t,r,n){var o;return t*(e*r+(o=n)-1&-o)}function ro(e){return{5:3,6:4,8:2,29502:3,29504:4}[e-6402]||1}function no(e){return 1==(e-=5120)?X:4==e?j:6==e?H:5==e||28922==e?L:C}function oo(e){return 31-Math.clz32(e.BYTES_PER_ELEMENT)}function io(e,t,r,n,o,i){var a=no(e),s=oo(a),c=1<<s,u=to(r,n,ro(t)*c,Lt.unpackAlignment);return a.subarray(o>>s,o+u>>s)}function ao(e,t,r,n,o,i,a){var s=io(i,o,r,n,a);s?fa.readPixels(e,t,r,n,o,i,s):Lt.recordError(1280)}function so(){}function co(e,t,r,n){fa.renderbufferStorage(e,t,r,n)}function uo(e,t){fa.sampleCoverage(e,!!t)}function lo(e,t,r,n){fa.scissor(e,t,r,n)}function fo(){Lt.recordError(1280)}function po(e,t,r,n){var o=Lt.getSource(e,t,r,n);fa.shaderSource(Lt.shaders[e],o)}function Eo(e,t,r){fa.stencilFunc(e,t,r)}function _o(e,t,r,n){fa.stencilFuncSeparate(e,t,r,n)}function mo(e){fa.stencilMask(e)}function go(e,t){fa.stencilMaskSeparate(e,t)}function To(e,t,r){fa.stencilOp(e,t,r)}function ho(e,t,r,n){fa.stencilOpSeparate(e,t,r,n)}function Oo(e,t,r,n,o,i,a,s,c){fa.texImage2D(e,t,r,n,o,i,a,s,c?io(s,a,n,o,c):null)}function wo(e,t,r){fa.texParameterf(e,t,r)}function yo(e,t,r){var n=H[r>>2];fa.texParameterf(e,t,n)}function Do(e,t,r){fa.texParameteri(e,t,r)}function vo(e,t,r){var n=j[r>>2];fa.texParameteri(e,t,n)}function bo(e,t,r,n,o,i,a,s,c){var u=null;c&&(u=io(s,a,o,i,c)),fa.texSubImage2D(e,t,r,n,o,i,a,s,u)}function Ro(e,t){fa.uniform1f(Lt.uniforms[e],t)}r.tempFixedLengthArray=Ir,r._emscripten_glDrawBuffersWEBGL=kr,r._emscripten_glDrawElements=Nr,r._emscripten_glDrawElementsInstancedANGLE=Xr,r._emscripten_glEnable=Ur,r._emscripten_glEnableVertexAttribArray=Cr,r._emscripten_glEndQueryEXT=jr,r._emscripten_glFinish=Lr,r._emscripten_glFlush=Hr,r._emscripten_glFramebufferRenderbuffer=Qr,r._emscripten_glFramebufferTexture2D=Gr,r._emscripten_glFrontFace=Br,r.__glGenObject=Wr,r._emscripten_glGenBuffers=zr,r._emscripten_glGenFramebuffers=Vr,r._emscripten_glGenQueriesEXT=Yr,r._emscripten_glGenRenderbuffers=qr,r._emscripten_glGenTextures=Kr,r._emscripten_glGenVertexArraysOES=Jr,r._emscripten_glGenerateMipmap=Zr,r.__glGetActiveAttribOrUniform=$r,r._emscripten_glGetActiveAttrib=en,r._emscripten_glGetActiveUniform=tn,r._emscripten_glGetAttachedShaders=rn,r._emscripten_glGetAttribLocation=nn,r.readI53FromI64=on,r.readI53FromU64=an,r.writeI53ToI64=sn,r.emscriptenWebGLGet=cn,r._emscripten_glGetBooleanv=un,r._emscripten_glGetBufferParameteriv=ln,r._emscripten_glGetError=dn,r._emscripten_glGetFloatv=fn,r._emscripten_glGetFramebufferAttachmentParameteriv=pn,r._emscripten_glGetIntegerv=En,r._emscripten_glGetProgramInfoLog=_n,r._emscripten_glGetProgramiv=mn,r._emscripten_glGetQueryObjecti64vEXT=gn,r._emscripten_glGetQueryObjectivEXT=Tn,r._emscripten_glGetQueryObjectui64vEXT=hn,r._emscripten_glGetQueryObjectuivEXT=On,r._emscripten_glGetQueryivEXT=wn,r._emscripten_glGetRenderbufferParameteriv=yn,r._emscripten_glGetShaderInfoLog=Dn,r._emscripten_glGetShaderPrecisionFormat=vn,r._emscripten_glGetShaderSource=bn,r._emscripten_glGetShaderiv=Rn,r.stringToNewUTF8=An,r._emscripten_glGetString=Sn,r._emscripten_glGetTexParameterfv=Pn,r._emscripten_glGetTexParameteriv=Mn,r.jstoi_q=Fn,r._emscripten_glGetUniformLocation=xn,r.emscriptenWebGLGetUniform=In,r._emscripten_glGetUniformfv=kn,r._emscripten_glGetUniformiv=Nn,r._emscripten_glGetVertexAttribPointerv=Xn,r.emscriptenWebGLGetVertexAttrib=Un,r._emscripten_glGetVertexAttribfv=Cn,r._emscripten_glGetVertexAttribiv=jn,r._emscripten_glHint=Ln,r._emscripten_glIsBuffer=Hn,r._emscripten_glIsEnabled=Qn,r._emscripten_glIsFramebuffer=Gn,r._emscripten_glIsProgram=Bn,r._emscripten_glIsQueryEXT=Wn,r._emscripten_glIsRenderbuffer=zn,r._emscripten_glIsShader=Vn,r._emscripten_glIsTexture=Yn,r._emscripten_glIsVertexArrayOES=qn,r._emscripten_glLineWidth=Kn,r._emscripten_glLinkProgram=Jn,r._emscripten_glPixelStorei=Zn,r._emscripten_glPolygonOffset=$n,r._emscripten_glQueryCounterEXT=eo,r.computeUnpackAlignedImageSize=to,r.__colorChannelsInGlTextureFormat=ro,r.heapObjectForWebGLType=no,r.heapAccessShiftForWebGLHeap=oo,r.emscriptenWebGLGetTexPixelData=io,r._emscripten_glReadPixels=ao,r._emscripten_glReleaseShaderCompiler=so,r._emscripten_glRenderbufferStorage=co,r._emscripten_glSampleCoverage=uo,r._emscripten_glScissor=lo,r._emscripten_glShaderBinary=fo,r._emscripten_glShaderSource=po,r._emscripten_glStencilFunc=Eo,r._emscripten_glStencilFuncSeparate=_o,r._emscripten_glStencilMask=mo,r._emscripten_glStencilMaskSeparate=go,r._emscripten_glStencilOp=To,r._emscripten_glStencilOpSeparate=ho,r._emscripten_glTexImage2D=Oo,r._emscripten_glTexParameterf=wo,r._emscripten_glTexParameterfv=yo,r._emscripten_glTexParameteri=Do,r._emscripten_glTexParameteriv=vo,r._emscripten_glTexSubImage2D=bo,r._emscripten_glUniform1f=Ro;var Ao=[];function So(e,t,r){if(t<=288)for(var n=Ao[t-1],o=0;o<t;++o)n[o]=H[r+4*o>>2];else n=H.subarray(r>>2,r+4*t>>2);fa.uniform1fv(Lt.uniforms[e],n)}function Po(e,t){fa.uniform1i(Lt.uniforms[e],t)}r.miniTempWebGLFloatBuffers=Ao,r._emscripten_glUniform1fv=So,r._emscripten_glUniform1i=Po;var Mo=[];function Fo(e,t,r){if(t<=288)for(var n=Mo[t-1],o=0;o<t;++o)n[o]=j[r+4*o>>2];else n=j.subarray(r>>2,r+4*t>>2);fa.uniform1iv(Lt.uniforms[e],n)}function xo(e,t,r){fa.uniform2f(Lt.uniforms[e],t,r)}function Io(e,t,r){if(t<=144)for(var n=Ao[2*t-1],o=0;o<2*t;o+=2)n[o]=H[r+4*o>>2],n[o+1]=H[r+(4*o+4)>>2];else n=H.subarray(r>>2,r+8*t>>2);fa.uniform2fv(Lt.uniforms[e],n)}function ko(e,t,r){fa.uniform2i(Lt.uniforms[e],t,r)}function No(e,t,r){if(t<=144)for(var n=Mo[2*t-1],o=0;o<2*t;o+=2)n[o]=j[r+4*o>>2],n[o+1]=j[r+(4*o+4)>>2];else n=j.subarray(r>>2,r+8*t>>2);fa.uniform2iv(Lt.uniforms[e],n)}function Xo(e,t,r,n){fa.uniform3f(Lt.uniforms[e],t,r,n)}function Uo(e,t,r){if(t<=96)for(var n=Ao[3*t-1],o=0;o<3*t;o+=3)n[o]=H[r+4*o>>2],n[o+1]=H[r+(4*o+4)>>2],n[o+2]=H[r+(4*o+8)>>2];else n=H.subarray(r>>2,r+12*t>>2);fa.uniform3fv(Lt.uniforms[e],n)}function Co(e,t,r,n){fa.uniform3i(Lt.uniforms[e],t,r,n)}function jo(e,t,r){if(t<=96)for(var n=Mo[3*t-1],o=0;o<3*t;o+=3)n[o]=j[r+4*o>>2],n[o+1]=j[r+(4*o+4)>>2],n[o+2]=j[r+(4*o+8)>>2];else n=j.subarray(r>>2,r+12*t>>2);fa.uniform3iv(Lt.uniforms[e],n)}function Lo(e,t,r,n,o){fa.uniform4f(Lt.uniforms[e],t,r,n,o)}function Ho(e,t,r){if(t<=72){var n=Ao[4*t-1],o=H;r>>=2;for(var i=0;i<4*t;i+=4){var a=r+i;n[i]=o[a],n[i+1]=o[a+1],n[i+2]=o[a+2],n[i+3]=o[a+3]}}else n=H.subarray(r>>2,r+16*t>>2);fa.uniform4fv(Lt.uniforms[e],n)}function Qo(e,t,r,n,o){fa.uniform4i(Lt.uniforms[e],t,r,n,o)}function Go(e,t,r){if(t<=72)for(var n=Mo[4*t-1],o=0;o<4*t;o+=4)n[o]=j[r+4*o>>2],n[o+1]=j[r+(4*o+4)>>2],n[o+2]=j[r+(4*o+8)>>2],n[o+3]=j[r+(4*o+12)>>2];else n=j.subarray(r>>2,r+16*t>>2);fa.uniform4iv(Lt.uniforms[e],n)}function Bo(e,t,r,n){if(t<=72)for(var o=Ao[4*t-1],i=0;i<4*t;i+=4)o[i]=H[n+4*i>>2],o[i+1]=H[n+(4*i+4)>>2],o[i+2]=H[n+(4*i+8)>>2],o[i+3]=H[n+(4*i+12)>>2];else o=H.subarray(n>>2,n+16*t>>2);fa.uniformMatrix2fv(Lt.uniforms[e],!!r,o)}function Wo(e,t,r,n){if(t<=32)for(var o=Ao[9*t-1],i=0;i<9*t;i+=9)o[i]=H[n+4*i>>2],o[i+1]=H[n+(4*i+4)>>2],o[i+2]=H[n+(4*i+8)>>2],o[i+3]=H[n+(4*i+12)>>2],o[i+4]=H[n+(4*i+16)>>2],o[i+5]=H[n+(4*i+20)>>2],o[i+6]=H[n+(4*i+24)>>2],o[i+7]=H[n+(4*i+28)>>2],o[i+8]=H[n+(4*i+32)>>2];else o=H.subarray(n>>2,n+36*t>>2);fa.uniformMatrix3fv(Lt.uniforms[e],!!r,o)}function zo(e,t,r,n){if(t<=18){var o=Ao[16*t-1],i=H;n>>=2;for(var a=0;a<16*t;a+=16){var s=n+a;o[a]=i[s],o[a+1]=i[s+1],o[a+2]=i[s+2],o[a+3]=i[s+3],o[a+4]=i[s+4],o[a+5]=i[s+5],o[a+6]=i[s+6],o[a+7]=i[s+7],o[a+8]=i[s+8],o[a+9]=i[s+9],o[a+10]=i[s+10],o[a+11]=i[s+11],o[a+12]=i[s+12],o[a+13]=i[s+13],o[a+14]=i[s+14],o[a+15]=i[s+15]}}else o=H.subarray(n>>2,n+64*t>>2);fa.uniformMatrix4fv(Lt.uniforms[e],!!r,o)}function Vo(e){fa.useProgram(Lt.programs[e])}function Yo(e){fa.validateProgram(Lt.programs[e])}function qo(e,t){fa.vertexAttrib1f(e,t)}function Ko(e,t){fa.vertexAttrib1f(e,H[t>>2])}function Jo(e,t,r){fa.vertexAttrib2f(e,t,r)}function Zo(e,t){fa.vertexAttrib2f(e,H[t>>2],H[t+4>>2])}function $o(e,t,r,n){fa.vertexAttrib3f(e,t,r,n)}function ei(e,t){fa.vertexAttrib3f(e,H[t>>2],H[t+4>>2],H[t+8>>2])}function ti(e,t,r,n,o){fa.vertexAttrib4f(e,t,r,n,o)}function ri(e,t){fa.vertexAttrib4f(e,H[t>>2],H[t+4>>2],H[t+8>>2],H[t+12>>2])}function ni(e,t){fa.vertexAttribDivisor(e,t)}function oi(e,t,r,n,o,i){fa.vertexAttribPointer(e,t,r,!!n,o,i)}function ii(e,t,r,n){fa.viewport(e,t,r,n)}function ai(e,t){throw xa(e,t||1),"longjmp"}function si(e,t){return ai(e,t)}function ci(e,t,r){X.copyWithin(e,t,t+r)}function ui(){return X.length}function li(e){try{return R.grow(e-k.byteLength+65535>>>16),$(R.buffer),1}catch(t){console.error("emscripten_realloc_buffer: Attempted to grow heap from "+k.byteLength+" bytes to "+e+" bytes, but got error: "+t)}}function di(e){var t=ui();M(e>t);var r=2147483648;if(e>r)return T("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+"2147483648 bytes!"),!1;for(var n=1;n<=4;n*=2){var o=t*(1+.2/n);o=Math.min(o,e+100663296);var i=Math.min(r,Z(Math.max(e,o),65536));if(li(i))return!0}return T("Failed to grow the heap from "+t+" bytes to "+i+" bytes, not enough memory!"),!1}r.__miniTempWebGLIntBuffers=Mo,r._emscripten_glUniform1iv=Fo,r._emscripten_glUniform2f=xo,r._emscripten_glUniform2fv=Io,r._emscripten_glUniform2i=ko,r._emscripten_glUniform2iv=No,r._emscripten_glUniform3f=Xo,r._emscripten_glUniform3fv=Uo,r._emscripten_glUniform3i=Co,r._emscripten_glUniform3iv=jo,r._emscripten_glUniform4f=Lo,r._emscripten_glUniform4fv=Ho,r._emscripten_glUniform4i=Qo,r._emscripten_glUniform4iv=Go,r._emscripten_glUniformMatrix2fv=Bo,r._emscripten_glUniformMatrix3fv=Wo,r._emscripten_glUniformMatrix4fv=zo,r._emscripten_glUseProgram=Vo,r._emscripten_glValidateProgram=Yo,r._emscripten_glVertexAttrib1f=qo,r._emscripten_glVertexAttrib1fv=Ko,r._emscripten_glVertexAttrib2f=Jo,r._emscripten_glVertexAttrib2fv=Zo,r._emscripten_glVertexAttrib3f=$o,r._emscripten_glVertexAttrib3fv=ei,r._emscripten_glVertexAttrib4f=ti,r._emscripten_glVertexAttrib4fv=ri,r._emscripten_glVertexAttribDivisorANGLE=ni,r._emscripten_glVertexAttribPointer=oi,r._emscripten_glViewport=ii,r._longjmp=ai,r._emscripten_longjmp=si,r._emscripten_memcpy_big=ci,r._emscripten_get_heap_size=ui,r.emscripten_realloc_buffer=li,r._emscripten_resize_heap=di;var fi={};function pi(){return o||"./this.program"}function Ei(){if(!Ei.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:pi()};for(var t in fi)e[t]=fi[t];var r=[];for(var t in e)r.push(t+"="+e[t]);Ei.strings=r}return Ei.strings}function _i(e,t){try{var r=0;return Ei().forEach((function(n,o){var i=t+r;j[e+4*o>>2]=i,J(n,i),r+=n.length+1})),0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function mi(e,t){try{var r=Ei();j[e>>2]=r.length;var n=0;return r.forEach((function(e){n+=e.length+1})),j[t>>2]=n,0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function gi(e){Wa(e)}function Ti(e){try{var t=mt.getStreamFromFD(e);return _t.close(t),0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function hi(e,t){try{var r=mt.getStreamFromFD(e),n=r.tty?2:_t.isDir(r.mode)?3:_t.isLink(r.mode)?7:4;return N[t>>0]=n,0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function Oi(e,t,r,n){try{var o=mt.getStreamFromFD(e),i=mt.doReadv(o,t,r);return j[n>>2]=i,0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function wi(e,t,r,n,o){try{var i=mt.getStreamFromFD(e),a=4294967296*r+(t>>>0),s=9007199254740992;return a<=-s||a>=s?-61:(_t.llseek(i,a,n),ke=[i.position>>>0,(Ie=i.position,+Math.abs(Ie)>=1?Ie>0?(0|Math.min(+Math.floor(Ie/4294967296),4294967295))>>>0:~~+Math.ceil((Ie-+(~~Ie>>>0))/4294967296)>>>0:0)],j[o>>2]=ke[0],j[o+4>>2]=ke[1],i.getdents&&0===a&&0===n&&(i.getdents=null),0)}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function yi(e,t,r,n){try{var o=mt.getStreamFromFD(e),i=mt.doWritev(o,t,r);return j[n>>2]=i,0}catch(e){return void 0!==_t&&e instanceof _t.ErrnoError||be(e),e.errno}}function Di(){return 0|b()}function vi(e,t){fa.bindTexture(e,Lt.textures[t])}function bi(e,t){fa.blendFunc(e,t)}function Ri(e,t,r,n){fa.colorMask(!!e,!!t,!!r,!!n)}function Ai(e){fa.cullFace(e)}function Si(e,t){for(var r=0;r<e;r++){var n=j[t+4*r>>2],o=Lt.textures[n];o&&(fa.deleteTexture(o),o.name=0,Lt.textures[n]=null)}}function Pi(e){fa.depthFunc(e)}function Mi(e){fa.depthMask(!!e)}function Fi(e,t){fa.depthRange(e,t)}function xi(e,t,r){fa.drawArrays(e,t,r)}function Ii(e,t,r,n){fa.drawElements(e,t,r,n)}function ki(e){fa.enable(e)}function Ni(e){fa.frontFace(e)}function Xi(e,t){Wr(e,t,"createTexture",Lt.textures)}function Ui(){var e=fa.getError()||Lt.lastError;return Lt.lastError=0,e}function Ci(e,t){cn(e,t,0)}function ji(e){if(Lt.stringCache[e])return Lt.stringCache[e];var t;switch(e){case 7939:var r=fa.getSupportedExtensions()||[];t=An((r=r.concat(r.map((function(e){return"GL_"+e})))).join(" "));break;case 7936:case 7937:case 37445:case 37446:var n=fa.getParameter(e);n||Lt.recordError(1280),t=An(n);break;case 7938:var o=fa.getParameter(7938);t=An(o="OpenGL ES 2.0 ("+o+")");break;case 35724:var i=fa.getParameter(35724),a=i.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==a&&(3==a[1].length&&(a[1]=a[1]+"0"),i="OpenGL ES GLSL ES "+a[1]+" ("+i+")"),t=An(i);break;default:return Lt.recordError(1280),0}return Lt.stringCache[e]=t,t}function Li(e,t){fa.hint(e,t)}function Hi(e){fa.lineWidth(e)}function Qi(e,t){3317==e&&(Lt.unpackAlignment=t),fa.pixelStorei(e,t)}function Gi(e,t){fa.polygonOffset(e,t)}function Bi(e,t,r,n,o,i,a){var s=io(i,o,r,n,a);s?fa.readPixels(e,t,r,n,o,i,s):Lt.recordError(1280)}function Wi(e,t,r,n){fa.scissor(e,t,r,n)}function zi(){Lt.recordError(1280)}function Vi(e,t,r){fa.stencilFunc(e,t,r)}function Yi(e){fa.stencilMask(e)}function qi(e,t,r){fa.stencilOp(e,t,r)}function Ki(e,t,r,n,o,i,a,s,c){fa.texImage2D(e,t,r,n,o,i,a,s,c?io(s,a,n,o,c):null)}function Ji(e,t,r){fa.texParameterf(e,t,r)}function Zi(e,t,r){var n=H[r>>2];fa.texParameterf(e,t,n)}function $i(e,t,r){fa.texParameteri(e,t,r)}function ea(e,t,r){var n=j[r>>2];fa.texParameteri(e,t,n)}function ta(e,t,r,n,o,i,a,s,c){var u=null;c&&(u=io(s,a,o,i,c)),fa.texSubImage2D(e,t,r,n,o,i,a,s,u)}function ra(e,t,r,n){fa.viewport(e,t,r,n)}function na(e){v(0|e)}function oa(e){return e%4==0&&(e%100!=0||e%400==0)}function ia(e,t){for(var r=0,n=0;n<=t;r+=e[n++]);return r}r.ENV=fi,r.getExecutableName=pi,r.getEnvStrings=Ei,r._environ_get=_i,r._environ_sizes_get=mi,r._exit=gi,r._fd_close=Ti,r._fd_fdstat_get=hi,r._fd_read=Oi,r._fd_seek=wi,r._fd_write=yi,r._getTempRet0=Di,r._glBindTexture=vi,r._glBlendFunc=bi,r._glColorMask=Ri,r._glCullFace=Ai,r._glDeleteTextures=Si,r._glDepthFunc=Pi,r._glDepthMask=Mi,r._glDepthRangef=Fi,r._glDrawArrays=xi,r._glDrawElements=Ii,r._glEnable=ki,r._glFrontFace=Ni,r._glGenTextures=Xi,r._glGetError=Ui,r._glGetIntegerv=Ci,r._glGetString=ji,r._glHint=Li,r._glLineWidth=Hi,r._glPixelStorei=Qi,r._glPolygonOffset=Gi,r._glReadPixels=Bi,r._glScissor=Wi,r._glShaderBinary=zi,r._glStencilFunc=Vi,r._glStencilMask=Yi,r._glStencilOp=qi,r._glTexImage2D=Ki,r._glTexParameterf=Ji,r._glTexParameterfv=Zi,r._glTexParameteri=$i,r._glTexParameteriv=ea,r._glTexSubImage2D=ta,r._glViewport=ra,r._setTempRet0=na,r.__isLeapYear=oa,r.__arraySum=ia;var aa=[31,29,31,30,31,30,31,31,30,31,30,31];r.__MONTH_DAYS_LEAP=aa;var sa=[31,28,31,30,31,30,31,31,30,31,30,31];function ca(e,t){for(var r=new Date(e.getTime());t>0;){var n=oa(r.getFullYear()),o=r.getMonth(),i=(n?aa:sa)[o];if(!(t>i-r.getDate()))return r.setDate(r.getDate()+t),r;t-=i-r.getDate()+1,r.setDate(1),o<11?r.setMonth(o+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1))}return r}function ua(e,t,r,n){var o=j[n+40>>2],i={tm_sec:j[n>>2],tm_min:j[n+4>>2],tm_hour:j[n+8>>2],tm_mday:j[n+12>>2],tm_mon:j[n+16>>2],tm_year:j[n+20>>2],tm_wday:j[n+24>>2],tm_yday:j[n+28>>2],tm_isdst:j[n+32>>2],tm_gmtoff:j[n+36>>2],tm_zone:o?W(o):""},a=W(r),s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var c in s)a=a.replace(new RegExp(c,"g"),s[c]);var u=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],l=["January","February","March","April","May","June","July","August","September","October","November","December"];function d(e,t,r){for(var n="number"==typeof e?e.toString():e||"";n.length<t;)n=r[0]+n;return n}function f(e,t){return d(e,t,"0")}function p(e,t){function r(e){return e<0?-1:e>0?1:0}var n;return 0===(n=r(e.getFullYear()-t.getFullYear()))&&0===(n=r(e.getMonth()-t.getMonth()))&&(n=r(e.getDate()-t.getDate())),n}function E(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function _(e){var t=ca(new Date(e.tm_year+1900,0,1),e.tm_yday),r=new Date(t.getFullYear(),0,4),n=new Date(t.getFullYear()+1,0,4),o=E(r),i=E(n);return p(o,t)<=0?p(i,t)<=0?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var m={"%a":function(e){return u[e.tm_wday].substring(0,3)},"%A":function(e){return u[e.tm_wday]},"%b":function(e){return l[e.tm_mon].substring(0,3)},"%B":function(e){return l[e.tm_mon]},"%C":function(e){return f((e.tm_year+1900)/100|0,2)},"%d":function(e){return f(e.tm_mday,2)},"%e":function(e){return d(e.tm_mday,2," ")},"%g":function(e){return _(e).toString().substring(2)},"%G":function(e){return _(e)},"%H":function(e){return f(e.tm_hour,2)},"%I":function(e){var t=e.tm_hour;return 0==t?t=12:t>12&&(t-=12),f(t,2)},"%j":function(e){return f(e.tm_mday+ia(oa(e.tm_year+1900)?aa:sa,e.tm_mon-1),3)},"%m":function(e){return f(e.tm_mon+1,2)},"%M":function(e){return f(e.tm_min,2)},"%n":function(){return"\n"},"%p":function(e){return e.tm_hour>=0&&e.tm_hour<12?"AM":"PM"},"%S":function(e){return f(e.tm_sec,2)},"%t":function(){return"\t"},"%u":function(e){return e.tm_wday||7},"%U":function(e){var t=new Date(e.tm_year+1900,0,1),r=0===t.getDay()?t:ca(t,7-t.getDay()),n=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(p(r,n)<0){var o=ia(oa(n.getFullYear())?aa:sa,n.getMonth()-1)-31,i=31-r.getDate()+o+n.getDate();return f(Math.ceil(i/7),2)}return 0===p(r,t)?"01":"00"},"%V":function(e){var t,r=new Date(e.tm_year+1900,0,4),n=new Date(e.tm_year+1901,0,4),o=E(r),i=E(n),a=ca(new Date(e.tm_year+1900,0,1),e.tm_yday);return p(a,o)<0?"53":p(i,a)<=0?"01":(t=o.getFullYear()<e.tm_year+1900?e.tm_yday+32-o.getDate():e.tm_yday+1-o.getDate(),f(Math.ceil(t/7),2))},"%w":function(e){return e.tm_wday},"%W":function(e){var t=new Date(e.tm_year,0,1),r=1===t.getDay()?t:ca(t,0===t.getDay()?1:7-t.getDay()+1),n=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(p(r,n)<0){var o=ia(oa(n.getFullYear())?aa:sa,n.getMonth()-1)-31,i=31-r.getDate()+o+n.getDate();return f(Math.ceil(i/7),2)}return 0===p(r,t)?"01":"00"},"%y":function(e){return(e.tm_year+1900).toString().substring(2)},"%Y":function(e){return e.tm_year+1900},"%z":function(e){var t=e.tm_gmtoff,r=t>=0;return t=(t=Math.abs(t)/60)/60*100+t%60,(r?"+":"-")+String("0000"+t).slice(-4)},"%Z":function(e){return e.tm_zone},"%%":function(){return"%"}};for(var c in m)a.indexOf(c)>=0&&(a=a.replace(new RegExp(c,"g"),m[c](i)));var g=ha(a,!1);return g.length>t?0:(K(g,e),g.length-1)}function la(e,t,r,n){return ua(e,t,r,n)}function da(e){var t=Date.now()/1e3|0;return e&&(j[e>>2]=t),t}r.__MONTH_DAYS_REGULAR=sa,r.__addDays=ca,r._strftime=ua,r._strftime_l=la,r._time=da;var fa,pa=function(e,t,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=_t.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},Ea=365,_a=146;Object.defineProperties(pa.prototype,{read:{get:function(){return(this.mode&Ea)===Ea},set:function(e){e?this.mode|=Ea:this.mode&=~Ea}},write:{get:function(){return(this.mode&_a)===_a},set:function(e){e?this.mode|=_a:this.mode&=~_a}},isFolder:{get:function(){return _t.isDir(this.mode)}},isDevice:{get:function(){return _t.isChrdev(this.mode)}}}),_t.FSNode=pa,_t.staticInit();for(var ma=0;ma<32;++ma)Ir.push(new Array(ma));var ga=new Float32Array(288);for(ma=0;ma<288;++ma)Ao[ma]=ga.subarray(0,ma+1);var Ta=new Int32Array(288);for(ma=0;ma<288;++ma)Mo[ma]=Ta.subarray(0,ma+1);function ha(e,t,r){var n=r>0?r:Y(e)+1,o=new Array(n),i=z(e,o,0,o.length);return t&&(o.length=i),o}var Oa={__assert_fail:We,__clock_gettime:qe,__cxa_allocate_exception:Je,__cxa_atexit:$e,__cxa_throw:nt,__localtime_r:at,__sys_access:gt,__sys_dup:Tt,__sys_fcntl64:ht,__sys_fstat64:Ot,__sys_getcwd:wt,__sys_getdents64:yt,__sys_ioctl:Dt,__sys_lstat64:vt,__sys_mkdir:bt,__sys_open:Rt,__sys_readlink:At,__sys_rename:St,__sys_rmdir:Pt,__sys_stat64:Mt,__sys_unlink:Ft,abort:xt,clock_gettime:Ye,dlclose:It,dlerror:kt,dlopen:Nt,emscripten_glActiveTexture:Ht,emscripten_glAttachShader:Qt,emscripten_glBeginQueryEXT:Gt,emscripten_glBindAttribLocation:Bt,emscripten_glBindBuffer:Wt,emscripten_glBindFramebuffer:zt,emscripten_glBindRenderbuffer:Vt,emscripten_glBindTexture:Yt,emscripten_glBindVertexArrayOES:qt,emscripten_glBlendColor:Kt,emscripten_glBlendEquation:Jt,emscripten_glBlendEquationSeparate:Zt,emscripten_glBlendFunc:$t,emscripten_glBlendFuncSeparate:er,emscripten_glBufferData:tr,emscripten_glBufferSubData:rr,emscripten_glCheckFramebufferStatus:nr,emscripten_glClear:or,emscripten_glClearColor:ir,emscripten_glClearDepthf:ar,emscripten_glClearStencil:sr,emscripten_glColorMask:cr,emscripten_glCompileShader:ur,emscripten_glCompressedTexImage2D:lr,emscripten_glCompressedTexSubImage2D:dr,emscripten_glCopyTexImage2D:fr,emscripten_glCopyTexSubImage2D:pr,emscripten_glCreateProgram:Er,emscripten_glCreateShader:_r,emscripten_glCullFace:mr,emscripten_glDeleteBuffers:gr,emscripten_glDeleteFramebuffers:Tr,emscripten_glDeleteProgram:hr,emscripten_glDeleteQueriesEXT:Or,emscripten_glDeleteRenderbuffers:wr,emscripten_glDeleteShader:yr,emscripten_glDeleteTextures:Dr,emscripten_glDeleteVertexArraysOES:vr,emscripten_glDepthFunc:br,emscripten_glDepthMask:Rr,emscripten_glDepthRangef:Ar,emscripten_glDetachShader:Sr,emscripten_glDisable:Pr,emscripten_glDisableVertexAttribArray:Mr,emscripten_glDrawArrays:Fr,emscripten_glDrawArraysInstancedANGLE:xr,emscripten_glDrawBuffersWEBGL:kr,emscripten_glDrawElements:Nr,emscripten_glDrawElementsInstancedANGLE:Xr,emscripten_glEnable:Ur,emscripten_glEnableVertexAttribArray:Cr,emscripten_glEndQueryEXT:jr,emscripten_glFinish:Lr,emscripten_glFlush:Hr,emscripten_glFramebufferRenderbuffer:Qr,emscripten_glFramebufferTexture2D:Gr,emscripten_glFrontFace:Br,emscripten_glGenBuffers:zr,emscripten_glGenFramebuffers:Vr,emscripten_glGenQueriesEXT:Yr,emscripten_glGenRenderbuffers:qr,emscripten_glGenTextures:Kr,emscripten_glGenVertexArraysOES:Jr,emscripten_glGenerateMipmap:Zr,emscripten_glGetActiveAttrib:en,emscripten_glGetActiveUniform:tn,emscripten_glGetAttachedShaders:rn,emscripten_glGetAttribLocation:nn,emscripten_glGetBooleanv:un,emscripten_glGetBufferParameteriv:ln,emscripten_glGetError:dn,emscripten_glGetFloatv:fn,emscripten_glGetFramebufferAttachmentParameteriv:pn,emscripten_glGetIntegerv:En,emscripten_glGetProgramInfoLog:_n,emscripten_glGetProgramiv:mn,emscripten_glGetQueryObjecti64vEXT:gn,emscripten_glGetQueryObjectivEXT:Tn,emscripten_glGetQueryObjectui64vEXT:hn,emscripten_glGetQueryObjectuivEXT:On,emscripten_glGetQueryivEXT:wn,emscripten_glGetRenderbufferParameteriv:yn,emscripten_glGetShaderInfoLog:Dn,emscripten_glGetShaderPrecisionFormat:vn,emscripten_glGetShaderSource:bn,emscripten_glGetShaderiv:Rn,emscripten_glGetString:Sn,emscripten_glGetTexParameterfv:Pn,emscripten_glGetTexParameteriv:Mn,emscripten_glGetUniformLocation:xn,emscripten_glGetUniformfv:kn,emscripten_glGetUniformiv:Nn,emscripten_glGetVertexAttribPointerv:Xn,emscripten_glGetVertexAttribfv:Cn,emscripten_glGetVertexAttribiv:jn,emscripten_glHint:Ln,emscripten_glIsBuffer:Hn,emscripten_glIsEnabled:Qn,emscripten_glIsFramebuffer:Gn,emscripten_glIsProgram:Bn,emscripten_glIsQueryEXT:Wn,emscripten_glIsRenderbuffer:zn,emscripten_glIsShader:Vn,emscripten_glIsTexture:Yn,emscripten_glIsVertexArrayOES:qn,emscripten_glLineWidth:Kn,emscripten_glLinkProgram:Jn,emscripten_glPixelStorei:Zn,emscripten_glPolygonOffset:$n,emscripten_glQueryCounterEXT:eo,emscripten_glReadPixels:ao,emscripten_glReleaseShaderCompiler:so,emscripten_glRenderbufferStorage:co,emscripten_glSampleCoverage:uo,emscripten_glScissor:lo,emscripten_glShaderBinary:fo,emscripten_glShaderSource:po,emscripten_glStencilFunc:Eo,emscripten_glStencilFuncSeparate:_o,emscripten_glStencilMask:mo,emscripten_glStencilMaskSeparate:go,emscripten_glStencilOp:To,emscripten_glStencilOpSeparate:ho,emscripten_glTexImage2D:Oo,emscripten_glTexParameterf:wo,emscripten_glTexParameterfv:yo,emscripten_glTexParameteri:Do,emscripten_glTexParameteriv:vo,emscripten_glTexSubImage2D:bo,emscripten_glUniform1f:Ro,emscripten_glUniform1fv:So,emscripten_glUniform1i:Po,emscripten_glUniform1iv:Fo,emscripten_glUniform2f:xo,emscripten_glUniform2fv:Io,emscripten_glUniform2i:ko,emscripten_glUniform2iv:No,emscripten_glUniform3f:Xo,emscripten_glUniform3fv:Uo,emscripten_glUniform3i:Co,emscripten_glUniform3iv:jo,emscripten_glUniform4f:Lo,emscripten_glUniform4fv:Ho,emscripten_glUniform4i:Qo,emscripten_glUniform4iv:Go,emscripten_glUniformMatrix2fv:Bo,emscripten_glUniformMatrix3fv:Wo,emscripten_glUniformMatrix4fv:zo,emscripten_glUseProgram:Vo,emscripten_glValidateProgram:Yo,emscripten_glVertexAttrib1f:qo,emscripten_glVertexAttrib1fv:Ko,emscripten_glVertexAttrib2f:Jo,emscripten_glVertexAttrib2fv:Zo,emscripten_glVertexAttrib3f:$o,emscripten_glVertexAttrib3fv:ei,emscripten_glVertexAttrib4f:ti,emscripten_glVertexAttrib4fv:ri,emscripten_glVertexAttribDivisorANGLE:ni,emscripten_glVertexAttribPointer:oi,emscripten_glViewport:ii,emscripten_longjmp:si,emscripten_memcpy_big:ci,emscripten_resize_heap:di,environ_get:_i,environ_sizes_get:mi,exit:gi,fd_close:Ti,fd_fdstat_get:hi,fd_read:Oi,fd_seek:wi,fd_write:yi,getTempRet0:Di,glBindTexture:vi,glBlendFunc:bi,glColorMask:Ri,glCullFace:Ai,glDeleteTextures:Si,glDepthFunc:Pi,glDepthMask:Mi,glDepthRangef:Fi,glDrawArrays:xi,glDrawElements:Ii,glEnable:ki,glFrontFace:Ni,glGenTextures:Xi,glGetError:Ui,glGetIntegerv:Ci,glGetString:ji,glHint:Li,glLineWidth:Hi,glPixelStorei:Qi,glPolygonOffset:Gi,glReadPixels:Bi,glScissor:Wi,glShaderBinary:zi,glStencilFunc:Vi,glStencilMask:Yi,glStencilOp:qi,glTexImage2D:Ki,glTexParameterf:Ji,glTexParameterfv:Zi,glTexParameteri:$i,glTexParameteriv:ea,glTexSubImage2D:ta,glViewport:ra,invoke_ii:ka,invoke_iii:Ca,invoke_iiii:La,invoke_iiiii:ja,invoke_vi:Na,invoke_vii:Ua,invoke_viii:Xa,setTempRet0:na,strftime:ua,strftime_l:la,time:da};je();var wa=r.___wasm_call_ctors=xe("__wasm_call_ctors");r._FreeMemory=xe("FreeMemory"),r._OSGBToS3MB=xe("OSGBToS3MB");var ya=r.___errno_location=xe("__errno_location");r._fflush=xe("fflush");var Da=r.__get_tzname=xe("_get_tzname"),va=r.__get_daylight=xe("_get_daylight"),ba=r.__get_timezone=xe("_get_timezone");r._emscripten_main_thread_process_queued_calls=xe("emscripten_main_thread_process_queued_calls");var Ra=r.stackSave=xe("stackSave"),Aa=r.stackRestore=xe("stackRestore"),Sa=r.stackAlloc=xe("stackAlloc"),Pa=r._emscripten_stack_init=function(){return(Pa=r._emscripten_stack_init=r.asm.emscripten_stack_init).apply(null,arguments)};r._emscripten_stack_get_free=function(){return(r._emscripten_stack_get_free=r.asm.emscripten_stack_get_free).apply(null,arguments)};var Ma,Fa=r._emscripten_stack_get_end=function(){return(Fa=r._emscripten_stack_get_end=r.asm.emscripten_stack_get_end).apply(null,arguments)},xa=r._setThrew=xe("setThrew"),Ia=r._malloc=xe("malloc");function ka(e,t){var r=Ra();try{return te.get(e)(t)}catch(e){if(Aa(r),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function Na(e,t){var r=Ra();try{te.get(e)(t)}catch(e){if(Aa(r),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function Xa(e,t,r,n){var o=Ra();try{te.get(e)(t,r,n)}catch(e){if(Aa(o),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function Ua(e,t,r){var n=Ra();try{te.get(e)(t,r)}catch(e){if(Aa(n),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function Ca(e,t,r){var n=Ra();try{return te.get(e)(t,r)}catch(e){if(Aa(n),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function ja(e,t,r,n,o){var i=Ra();try{return te.get(e)(t,r,n,o)}catch(e){if(Aa(i),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function La(e,t,r,n){var o=Ra();try{return te.get(e)(t,r,n)}catch(e){if(Aa(o),e!==e+0&&"longjmp"!==e)throw e;xa(1,0)}}function Ha(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Qa(){Pa(),ne()}function Ga(e){function t(){Ma||(Ma=!0,r.calledRun=!0,P||(fe(),pe(),r.onRuntimeInitialized&&r.onRuntimeInitialized(),M(!r._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),_e()))}Te>0||(Qa(),de(),Te>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),t()}),1)):t(),oe()))}function Ba(){var e=g,t=T,n=!1;g=T=function(e){n=!0};try{var o=r._fflush;o&&o(0),["stdout","stderr"].forEach((function(e){var t=_t.analyzePath("/dev/"+e);if(t){var r=t.object.rdev,o=lt.ttys[r];o&&o.output&&o.output.length&&(n=!0)}}))}catch(e){}g=e,T=t,n&&w("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.")}function Wa(e,t){if(Ba(),!t||!A||0!==e){if(A){if(!t)T("program exited (with status: "+e+"), but EXIT_RUNTIME is not set, so halting execution but not exiting the runtime or preventing further async execution (build with EXIT_RUNTIME=1, if you want a true shutdown)")}else Ee(),r.onExit&&r.onExit(e),P=!0;i(e,new Ha(e))}}if(r._free=xe("free"),r.dynCall_jiji=xe("dynCall_jiji"),r.dynCall_iiiiij=xe("dynCall_iiiiij"),r.dynCall_iiiiijj=xe("dynCall_iiiiijj"),r.dynCall_iiiiiijj=xe("dynCall_iiiiiijj"),r.dynCall_viijii=xe("dynCall_viijii"),r.dynCall_vij=xe("dynCall_vij"),r.dynCall_jii=xe("dynCall_jii"),r.dynCall_iiiji=xe("dynCall_iiiji"),r.dynCall_ij=xe("dynCall_ij"),r.dynCall_ji=xe("dynCall_ji"),r.dynCall_iiiij=xe("dynCall_iiiij"),Object.getOwnPropertyDescriptor(r,"intArrayFromString")||(r.intArrayFromString=function(){be("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"intArrayToString")||(r.intArrayToString=function(){be("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),r.ccall=x,r.cwrap=I,Object.getOwnPropertyDescriptor(r,"setValue")||(r.setValue=function(){be("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),r.getValue=S,Object.getOwnPropertyDescriptor(r,"allocate")||(r.allocate=function(){be("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"UTF8ArrayToString")||(r.UTF8ArrayToString=function(){be("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"UTF8ToString")||(r.UTF8ToString=function(){be("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToUTF8Array")||(r.stringToUTF8Array=function(){be("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToUTF8")||(r.stringToUTF8=function(){be("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"lengthBytesUTF8")||(r.lengthBytesUTF8=function(){be("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stackTrace")||(r.stackTrace=function(){be("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addOnPreRun")||(r.addOnPreRun=function(){be("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addOnInit")||(r.addOnInit=function(){be("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addOnPreMain")||(r.addOnPreMain=function(){be("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addOnExit")||(r.addOnExit=function(){be("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addOnPostRun")||(r.addOnPostRun=function(){be("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeStringToMemory")||(r.writeStringToMemory=function(){be("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeArrayToMemory")||(r.writeArrayToMemory=function(){be("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeAsciiToMemory")||(r.writeAsciiToMemory=function(){be("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addRunDependency")||(r.addRunDependency=function(){be("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"removeRunDependency")||(r.removeRunDependency=function(){be("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_createFolder")||(r.FS_createFolder=function(){be("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"FS_createPath")||(r.FS_createPath=function(){be("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_createDataFile")||(r.FS_createDataFile=function(){be("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_createPreloadedFile")||(r.FS_createPreloadedFile=function(){be("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_createLazyFile")||(r.FS_createLazyFile=function(){be("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_createLink")||(r.FS_createLink=function(){be("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"FS_createDevice")||(r.FS_createDevice=function(){be("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"FS_unlink")||(r.FS_unlink=function(){be("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(r,"getLEB")||(r.getLEB=function(){be("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getFunctionTables")||(r.getFunctionTables=function(){be("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"alignFunctionTables")||(r.alignFunctionTables=function(){be("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerFunctions")||(r.registerFunctions=function(){be("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"addFunction")||(r.addFunction=function(){be("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"removeFunction")||(r.removeFunction=function(){be("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getFuncWrapper")||(r.getFuncWrapper=function(){be("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"prettyPrint")||(r.prettyPrint=function(){be("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"makeBigInt")||(r.makeBigInt=function(){be("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"dynCall")||(r.dynCall=function(){be("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getCompilerSetting")||(r.getCompilerSetting=function(){be("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"print")||(r.print=function(){be("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"printErr")||(r.printErr=function(){be("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getTempRet0")||(r.getTempRet0=function(){be("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setTempRet0")||(r.setTempRet0=function(){be("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"callMain")||(r.callMain=function(){be("'callMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"abort")||(r.abort=function(){be("'abort' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToNewUTF8")||(r.stringToNewUTF8=function(){be("'stringToNewUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setFileTime")||(r.setFileTime=function(){be("'setFileTime' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"emscripten_realloc_buffer")||(r.emscripten_realloc_buffer=function(){be("'emscripten_realloc_buffer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"ENV")||(r.ENV=function(){be("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"ERRNO_CODES")||(r.ERRNO_CODES=function(){be("'ERRNO_CODES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"ERRNO_MESSAGES")||(r.ERRNO_MESSAGES=function(){be("'ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setErrNo")||(r.setErrNo=function(){be("'setErrNo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"inetPton4")||(r.inetPton4=function(){be("'inetPton4' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"inetNtop4")||(r.inetNtop4=function(){be("'inetNtop4' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"inetPton6")||(r.inetPton6=function(){be("'inetPton6' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"inetNtop6")||(r.inetNtop6=function(){be("'inetNtop6' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"readSockaddr")||(r.readSockaddr=function(){be("'readSockaddr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeSockaddr")||(r.writeSockaddr=function(){be("'writeSockaddr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"DNS")||(r.DNS=function(){be("'DNS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getHostByName")||(r.getHostByName=function(){be("'getHostByName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GAI_ERRNO_MESSAGES")||(r.GAI_ERRNO_MESSAGES=function(){be("'GAI_ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"Protocols")||(r.Protocols=function(){be("'Protocols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"Sockets")||(r.Sockets=function(){be("'Sockets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getRandomDevice")||(r.getRandomDevice=function(){be("'getRandomDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"traverseStack")||(r.traverseStack=function(){be("'traverseStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"UNWIND_CACHE")||(r.UNWIND_CACHE=function(){be("'UNWIND_CACHE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"withBuiltinMalloc")||(r.withBuiltinMalloc=function(){be("'withBuiltinMalloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"readAsmConstArgsArray")||(r.readAsmConstArgsArray=function(){be("'readAsmConstArgsArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"readAsmConstArgs")||(r.readAsmConstArgs=function(){be("'readAsmConstArgs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"mainThreadEM_ASM")||(r.mainThreadEM_ASM=function(){be("'mainThreadEM_ASM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"jstoi_q")||(r.jstoi_q=function(){be("'jstoi_q' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"jstoi_s")||(r.jstoi_s=function(){be("'jstoi_s' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getExecutableName")||(r.getExecutableName=function(){be("'getExecutableName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"listenOnce")||(r.listenOnce=function(){be("'listenOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"autoResumeAudioContext")||(r.autoResumeAudioContext=function(){be("'autoResumeAudioContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"dynCallLegacy")||(r.dynCallLegacy=function(){be("'dynCallLegacy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getDynCaller")||(r.getDynCaller=function(){be("'getDynCaller' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"dynCall")||(r.dynCall=function(){be("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"callRuntimeCallbacks")||(r.callRuntimeCallbacks=function(){be("'callRuntimeCallbacks' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"reallyNegative")||(r.reallyNegative=function(){be("'reallyNegative' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"unSign")||(r.unSign=function(){be("'unSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"reSign")||(r.reSign=function(){be("'reSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"formatString")||(r.formatString=function(){be("'formatString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"PATH")||(r.PATH=function(){be("'PATH' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"PATH_FS")||(r.PATH_FS=function(){be("'PATH_FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SYSCALLS")||(r.SYSCALLS=function(){be("'SYSCALLS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"syscallMmap2")||(r.syscallMmap2=function(){be("'syscallMmap2' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"syscallMunmap")||(r.syscallMunmap=function(){be("'syscallMunmap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getSocketFromFD")||(r.getSocketFromFD=function(){be("'getSocketFromFD' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getSocketAddress")||(r.getSocketAddress=function(){be("'getSocketAddress' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"JSEvents")||(r.JSEvents=function(){be("'JSEvents' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerKeyEventCallback")||(r.registerKeyEventCallback=function(){be("'registerKeyEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"specialHTMLTargets")||(r.specialHTMLTargets=function(){be("'specialHTMLTargets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"maybeCStringToJsString")||(r.maybeCStringToJsString=function(){be("'maybeCStringToJsString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"findEventTarget")||(r.findEventTarget=function(){be("'findEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"findCanvasEventTarget")||(r.findCanvasEventTarget=function(){be("'findCanvasEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getBoundingClientRect")||(r.getBoundingClientRect=function(){be("'getBoundingClientRect' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillMouseEventData")||(r.fillMouseEventData=function(){be("'fillMouseEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerMouseEventCallback")||(r.registerMouseEventCallback=function(){be("'registerMouseEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerWheelEventCallback")||(r.registerWheelEventCallback=function(){be("'registerWheelEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerUiEventCallback")||(r.registerUiEventCallback=function(){be("'registerUiEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerFocusEventCallback")||(r.registerFocusEventCallback=function(){be("'registerFocusEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillDeviceOrientationEventData")||(r.fillDeviceOrientationEventData=function(){be("'fillDeviceOrientationEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerDeviceOrientationEventCallback")||(r.registerDeviceOrientationEventCallback=function(){be("'registerDeviceOrientationEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillDeviceMotionEventData")||(r.fillDeviceMotionEventData=function(){be("'fillDeviceMotionEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerDeviceMotionEventCallback")||(r.registerDeviceMotionEventCallback=function(){be("'registerDeviceMotionEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"screenOrientation")||(r.screenOrientation=function(){be("'screenOrientation' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillOrientationChangeEventData")||(r.fillOrientationChangeEventData=function(){be("'fillOrientationChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerOrientationChangeEventCallback")||(r.registerOrientationChangeEventCallback=function(){be("'registerOrientationChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillFullscreenChangeEventData")||(r.fillFullscreenChangeEventData=function(){be("'fillFullscreenChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerFullscreenChangeEventCallback")||(r.registerFullscreenChangeEventCallback=function(){be("'registerFullscreenChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerRestoreOldStyle")||(r.registerRestoreOldStyle=function(){be("'registerRestoreOldStyle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"hideEverythingExceptGivenElement")||(r.hideEverythingExceptGivenElement=function(){be("'hideEverythingExceptGivenElement' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"restoreHiddenElements")||(r.restoreHiddenElements=function(){be("'restoreHiddenElements' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setLetterbox")||(r.setLetterbox=function(){be("'setLetterbox' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"currentFullscreenStrategy")||(r.currentFullscreenStrategy=function(){be("'currentFullscreenStrategy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"restoreOldWindowedStyle")||(r.restoreOldWindowedStyle=function(){be("'restoreOldWindowedStyle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"softFullscreenResizeWebGLRenderTarget")||(r.softFullscreenResizeWebGLRenderTarget=function(){be("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"doRequestFullscreen")||(r.doRequestFullscreen=function(){be("'doRequestFullscreen' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillPointerlockChangeEventData")||(r.fillPointerlockChangeEventData=function(){be("'fillPointerlockChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerPointerlockChangeEventCallback")||(r.registerPointerlockChangeEventCallback=function(){be("'registerPointerlockChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerPointerlockErrorEventCallback")||(r.registerPointerlockErrorEventCallback=function(){be("'registerPointerlockErrorEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"requestPointerLock")||(r.requestPointerLock=function(){be("'requestPointerLock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillVisibilityChangeEventData")||(r.fillVisibilityChangeEventData=function(){be("'fillVisibilityChangeEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerVisibilityChangeEventCallback")||(r.registerVisibilityChangeEventCallback=function(){be("'registerVisibilityChangeEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerTouchEventCallback")||(r.registerTouchEventCallback=function(){be("'registerTouchEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillGamepadEventData")||(r.fillGamepadEventData=function(){be("'fillGamepadEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerGamepadEventCallback")||(r.registerGamepadEventCallback=function(){be("'registerGamepadEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerBeforeUnloadEventCallback")||(r.registerBeforeUnloadEventCallback=function(){be("'registerBeforeUnloadEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"fillBatteryEventData")||(r.fillBatteryEventData=function(){be("'fillBatteryEventData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"battery")||(r.battery=function(){be("'battery' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"registerBatteryEventCallback")||(r.registerBatteryEventCallback=function(){be("'registerBatteryEventCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setCanvasElementSize")||(r.setCanvasElementSize=function(){be("'setCanvasElementSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getCanvasElementSize")||(r.getCanvasElementSize=function(){be("'getCanvasElementSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"polyfillSetImmediate")||(r.polyfillSetImmediate=function(){be("'polyfillSetImmediate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"demangle")||(r.demangle=function(){be("'demangle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"demangleAll")||(r.demangleAll=function(){be("'demangleAll' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"jsStackTrace")||(r.jsStackTrace=function(){be("'jsStackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stackTrace")||(r.stackTrace=function(){be("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getEnvStrings")||(r.getEnvStrings=function(){be("'getEnvStrings' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"checkWasiClock")||(r.checkWasiClock=function(){be("'checkWasiClock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeI53ToI64")||(r.writeI53ToI64=function(){be("'writeI53ToI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeI53ToI64Clamped")||(r.writeI53ToI64Clamped=function(){be("'writeI53ToI64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeI53ToI64Signaling")||(r.writeI53ToI64Signaling=function(){be("'writeI53ToI64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeI53ToU64Clamped")||(r.writeI53ToU64Clamped=function(){be("'writeI53ToU64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeI53ToU64Signaling")||(r.writeI53ToU64Signaling=function(){be("'writeI53ToU64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"readI53FromI64")||(r.readI53FromI64=function(){be("'readI53FromI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"readI53FromU64")||(r.readI53FromU64=function(){be("'readI53FromU64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"convertI32PairToI53")||(r.convertI32PairToI53=function(){be("'convertI32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"convertU32PairToI53")||(r.convertU32PairToI53=function(){be("'convertU32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"uncaughtExceptionCount")||(r.uncaughtExceptionCount=function(){be("'uncaughtExceptionCount' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"exceptionLast")||(r.exceptionLast=function(){be("'exceptionLast' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"exceptionCaught")||(r.exceptionCaught=function(){be("'exceptionCaught' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"ExceptionInfoAttrs")||(r.ExceptionInfoAttrs=function(){be("'ExceptionInfoAttrs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"ExceptionInfo")||(r.ExceptionInfo=function(){be("'ExceptionInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"CatchInfo")||(r.CatchInfo=function(){be("'CatchInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"exception_addRef")||(r.exception_addRef=function(){be("'exception_addRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"exception_decRef")||(r.exception_decRef=function(){be("'exception_decRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"Browser")||(r.Browser=function(){be("'Browser' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"funcWrappers")||(r.funcWrappers=function(){be("'funcWrappers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"getFuncWrapper")||(r.getFuncWrapper=function(){be("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"setMainLoop")||(r.setMainLoop=function(){be("'setMainLoop' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"FS")||(r.FS=function(){be("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"mmapAlloc")||(r.mmapAlloc=function(){be("'mmapAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"MEMFS")||(r.MEMFS=function(){be("'MEMFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"TTY")||(r.TTY=function(){be("'TTY' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"PIPEFS")||(r.PIPEFS=function(){be("'PIPEFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SOCKFS")||(r.SOCKFS=function(){be("'SOCKFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"_setNetworkCallback")||(r._setNetworkCallback=function(){be("'_setNetworkCallback' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"tempFixedLengthArray")||(r.tempFixedLengthArray=function(){be("'tempFixedLengthArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"miniTempWebGLFloatBuffers")||(r.miniTempWebGLFloatBuffers=function(){be("'miniTempWebGLFloatBuffers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"heapObjectForWebGLType")||(r.heapObjectForWebGLType=function(){be("'heapObjectForWebGLType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"heapAccessShiftForWebGLHeap")||(r.heapAccessShiftForWebGLHeap=function(){be("'heapAccessShiftForWebGLHeap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GL")||(r.GL=function(){be("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"emscriptenWebGLGet")||(r.emscriptenWebGLGet=function(){be("'emscriptenWebGLGet' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"computeUnpackAlignedImageSize")||(r.computeUnpackAlignedImageSize=function(){be("'computeUnpackAlignedImageSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"emscriptenWebGLGetTexPixelData")||(r.emscriptenWebGLGetTexPixelData=function(){be("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"emscriptenWebGLGetUniform")||(r.emscriptenWebGLGetUniform=function(){be("'emscriptenWebGLGetUniform' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"emscriptenWebGLGetVertexAttrib")||(r.emscriptenWebGLGetVertexAttrib=function(){be("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"writeGLArray")||(r.writeGLArray=function(){be("'writeGLArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"AL")||(r.AL=function(){be("'AL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SDL_unicode")||(r.SDL_unicode=function(){be("'SDL_unicode' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SDL_ttfContext")||(r.SDL_ttfContext=function(){be("'SDL_ttfContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SDL_audio")||(r.SDL_audio=function(){be("'SDL_audio' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SDL")||(r.SDL=function(){be("'SDL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"SDL_gfx")||(r.SDL_gfx=function(){be("'SDL_gfx' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GLUT")||(r.GLUT=function(){be("'GLUT' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"EGL")||(r.EGL=function(){be("'EGL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GLFW_Window")||(r.GLFW_Window=function(){be("'GLFW_Window' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GLFW")||(r.GLFW=function(){be("'GLFW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"GLEW")||(r.GLEW=function(){be("'GLEW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"IDBStore")||(r.IDBStore=function(){be("'IDBStore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"runAndAbortIfError")||(r.runAndAbortIfError=function(){be("'runAndAbortIfError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"warnOnce")||(r.warnOnce=function(){be("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stackSave")||(r.stackSave=function(){be("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stackRestore")||(r.stackRestore=function(){be("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stackAlloc")||(r.stackAlloc=function(){be("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"AsciiToString")||(r.AsciiToString=function(){be("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToAscii")||(r.stringToAscii=function(){be("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"UTF16ToString")||(r.UTF16ToString=function(){be("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToUTF16")||(r.stringToUTF16=function(){be("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"lengthBytesUTF16")||(r.lengthBytesUTF16=function(){be("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"UTF32ToString")||(r.UTF32ToString=function(){be("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"stringToUTF32")||(r.stringToUTF32=function(){be("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"lengthBytesUTF32")||(r.lengthBytesUTF32=function(){be("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"allocateUTF8")||(r.allocateUTF8=function(){be("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(r,"allocateUTF8OnStack")||(r.allocateUTF8OnStack=function(){be("'allocateUTF8OnStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),r.writeStackCookie=ne,r.checkStackCookie=oe,Object.getOwnPropertyDescriptor(r,"ALLOC_NORMAL")||Object.defineProperty(r,"ALLOC_NORMAL",{configurable:!0,get:function(){be("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Object.getOwnPropertyDescriptor(r,"ALLOC_STACK")||Object.defineProperty(r,"ALLOC_STACK",{configurable:!0,get:function(){be("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Oe=function e(){Ma||Ga(),Ma||(Oe=e)},r.run=Ga,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();Ga(),e.default=r}));
