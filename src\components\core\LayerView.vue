<template>
  <div class="layer-view" :style="{ zIndex: props.zIndex }"
    :class="{ hidden: props.routerName === 'editLayer' && route.name !== 'PageEdit' }">
    <router-view :name="props.routerName" :key="getRouterKey($route)">
    </router-view>
  </div>
</template>
<script setup>
import { provide } from "vue";
import { useRoute } from "vue-router";
import { RouterView } from 'vue-router'

const route = useRoute();

const props = defineProps({
  routerName: {
    type: String,
    default: 'MiddleView'
  },
  zIndex: {
    type: Number,
    default: 1
  },
  renderMode: {
    type: String,
  }
})

provide('layer', props.routerName)
provide('renderMode', props.renderMode)

function getRouterKey($route) {
  if (!props.renderMode) {
    return
  }
  if (props.renderMode === 'path') {
    return $route.path
  }
  if (props.renderMode === 'fullPath') {
    return $route.fullPath
  }
}

</script>

<style>
.layer-view {
  position: absolute;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.layer-view * {
  pointer-events: auto;
}

.hidden {
  visibility: hidden;
}
</style>
