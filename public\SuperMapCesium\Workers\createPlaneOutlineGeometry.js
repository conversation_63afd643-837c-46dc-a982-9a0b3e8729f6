define(["./when-b60132fc","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./FeatureDetection-806b12f0","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(e,t,n,r,a,i,o,u,c,y,p,s,d,b){"use strict";function m(){this._workerName="createPlaneOutlineGeometry"}m.packedLength=0,m.pack=function(e,t){return t},m.unpack=function(t,n,r){return e.defined(r)?r:new m};var f=new n.Cartesian3(-.5,-.5,0),w=new n.Cartesian3(.5,.5,0);return m.createGeometry=function(){var e=new o.GeometryAttributes,r=new Uint16Array(8),c=new Float64Array(12);return c[0]=f.x,c[1]=f.y,c[2]=f.z,c[3]=w.x,c[4]=f.y,c[5]=f.z,c[6]=w.x,c[7]=w.y,c[8]=f.z,c[9]=f.x,c[10]=w.y,c[11]=f.z,e.position=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c}),r[0]=0,r[1]=1,r[2]=1,r[3]=2,r[4]=2,r[5]=3,r[6]=3,r[7]=0,new i.Geometry({attributes:e,indices:r,primitiveType:u.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(n.Cartesian3.ZERO,Math.sqrt(2))})},function(t,n){return e.defined(n)&&(t=m.unpack(t,n)),m.createGeometry(t)}}));
