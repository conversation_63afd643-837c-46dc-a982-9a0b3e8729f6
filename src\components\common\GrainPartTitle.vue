<template>
  <div class="title-box">
    <AnimationText class="title-text" :text="props.title" :position="props.position" />
  </div>
</template>
<script setup>
import { onMounted } from "vue";
import AnimationText from "./AnimationText.vue"

const props = defineProps({
  title: {
    type: String
  },
  position: {
    type: Number
  }
})

onMounted(() => {

})
</script>
<style scoped>
.title-box {
  display: flex;
  align-items: center;
  width: 449px;
  height: 40px;
  background-image: url('../../assets/images/grain_title_bg.png');
  background-size: 100% 100%;
  padding-left: 67px;
  padding-bottom: 14px;
}


.title-text {
  font-size: 20px;
  font-family: YouSheBiaoTiHei;
}

:deep(.aki__char) {
  background: linear-gradient(0deg, rgba(90, 194, 232, 0.862) 0%, rgba(96, 188, 249, 0.725) 0%, rgba(241, 252, 254, 0.878) 58.7646484375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
