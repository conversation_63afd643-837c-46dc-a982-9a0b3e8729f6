# 增强型Cesium三维地图组件

## 功能概述

基于现有的Cesium组件，开发了一个增强版本，集成了四个主要功能模块，专门针对水利工程应用场景设计。

## 主要功能

### 1. 场景漫游 🚶
- **预设路径漫游**: 支持沿预定义路线的自动相机漫游
- **实时坐标显示**: 漫游过程中实时显示当前地理坐标和海拔高度
- **高精度剖面分析**: 提供线路的地形剖面图，便于分析水利工程截面地形
- **动态高程获取**: 结合漫游距离动态获取对应坐标的高精度海拔

### 2. 数据标签信息 🏷️
- **交互式标签**: 在地图上添加可点击的数据标签
- **工程信息展示**: 显示水利工程的关键信息（流量、水位、功率等）
- **动态数据更新**: 支持实时更新标签信息
- **增强真实感**: 提供模型点击交互，增强展示效果

### 3. 分屏比较 📱
- **多方案对比**: 同时显示不同时期或不同设计方案的模型
- **左右分屏**: 支持左右分屏模式，便于直观对比
- **方案切换**: 可以灵活切换不同方案进行比较
- **快速判断**: 帮助快速识别不同方案的优劣

### 4. 洪水淹没模拟 🌊
- **三维淹没效果**: 在三维场景中模拟洪水淹没过程
- **动态水位控制**: 支持手动调节或自动动画的水位变化
- **任意视角观察**: 可从任意角度观察淹没范围和水深
- **真实渲染**: 结合高清影像和高精度地形，提供逼真的淹没效果

## 技术特点

### 组件架构
- **基于现有组件**: 扩展原有Cesium组件，保持兼容性
- **模块化设计**: 四个功能模块独立开发，可单独使用
- **状态管理**: 使用Pinia进行统一的状态管理
- **响应式设计**: 支持不同屏幕尺寸的自适应显示

### 数据管理
- **工程数据**: 集成水利工程的基础数据（坝体、泵站、监测站等）
- **路径数据**: 预定义的巡检路线和漫游路径
- **洪水场景**: 不同等级的洪水淹没场景数据
- **实时更新**: 支持数据的动态加载和更新

### 性能优化
- **按需加载**: 功能模块按需激活，减少资源消耗
- **缓存机制**: 对地形数据和模型进行缓存优化
- **渲染优化**: 针对大场景进行渲染性能优化

## 使用方法

### 1. 访问页面
在浏览器中访问 `/enhanced-cesium` 路径，或通过页面头部的"增强Cesium"按钮进入。

### 2. 功能操作

#### 场景漫游
1. 点击"开始路径漫游"按钮启动自动漫游
2. 漫游过程中可查看实时坐标和高度信息
3. 点击"显示剖面"查看地形剖面分析图
4. 点击"停止漫游"结束当前漫游

#### 数据标签
1. 点击"显示/隐藏标签"控制标签显示
2. 点击"添加示例数据"增加新的标签点
3. 点击地图上的标签查看详细信息

#### 分屏比较
1. 点击"开启分屏"启用分屏模式
2. 使用下拉菜单选择左右两侧要对比的方案
3. 点击"关闭分屏"退出分屏模式

#### 洪水淹没
1. 点击"开始模拟"启动洪水淹没动画
2. 使用滑块手动调节水位高度
3. 观察三维场景中的淹没效果
4. 点击"停止模拟"结束模拟

## 文件结构

```
src/
├── components/cesium/enhanced/
│   └── index.vue                 # 增强Cesium组件主文件
├── stores/modules/
│   └── enhancedCesium.js         # 状态管理store
├── views/
│   └── EnhancedCesium.vue        # 展示页面
└── router/
    └── routes.js                 # 路由配置（已更新）
```

## 扩展说明

### 样式扩展
在 `src/components/cesium/style.js` 中添加了新的样式配置：
- `FloodStyle`: 洪水淹没效果样式
- `PathStyle`: 路径漫游线条样式

### 路由配置
在 `src/router/routes.js` 中添加了新的路由：
```javascript
{
  path: "/enhanced-cesium",
  name: "EnhancedCesium",
  components: {
    firstLayer: EnhancedCesium,
  }
}
```

## 应用场景

### 水利工程监控
- 大坝安全监测
- 泵站运行状态监控
- 水位流量实时监测
- 工程进度可视化

### 防洪减灾
- 洪水风险评估
- 淹没范围预测
- 应急预案演练
- 灾害影响分析

### 工程设计
- 多方案比较分析
- 地形剖面分析
- 工程选址优化
- 设计效果展示

## 技术依赖

- Vue 3
- Cesium
- Pinia
- Vue Router

## 后续优化

1. **数据接口**: 集成真实的地形服务和工程数据API
2. **性能优化**: 针对大数据量场景进行进一步优化
3. **功能扩展**: 添加更多水利工程专业功能
4. **用户体验**: 优化交互界面和操作流程
