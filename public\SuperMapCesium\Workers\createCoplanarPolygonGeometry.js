define(["./arrayRemoveDuplicates-d2f048c5","./BoundingRectangle-1f901ba8","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CoplanarPolygonGeometryLibrary-b4bff604","./when-b60132fc","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryPipeline-44c6c124","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonGeometryLibrary-208ca427","./PolygonPipeline-d328cdf1","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./OrientedBoundingBox-08964f84","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-ce9a1fbb","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./AttributeCompression-90851096","./EncodedCartesian3-f1396b05","./ArcType-29cf2197","./EllipsoidRhumbLine-ed1a6bf4","./earcut-2.2.1-20c8012f"],(function(e,t,a,n,r,o,i,l,s,p,c,y,u,m,d,g,b,v,f,h,C,x,P,w,A,F,G,L,E,T,D,_){"use strict";var k=new r.Cartesian3,V=new t.BoundingRectangle,R=new n.Cartesian2,M=new n.Cartesian2,I=new r.Cartesian3,H=new r.Cartesian3,B=new r.Cartesian3,O=new r.Cartesian3,z=new r.Cartesian3,S=new r.Cartesian3,N=new p.Quaternion,Q=new g.Matrix3,U=new g.Matrix3,j=new r.Cartesian3;function Y(e,t,a,o,l,s,y,u){var b=e.positions,f=v.PolygonPipeline.triangulate(e.positions2D,e.holes);f.length<3&&(f=[0,1,2]);var h=m.IndexDatatype.createTypedArray(b.length,f.length);h.set(f);var C=Q;if(0!==o){var x=p.Quaternion.fromAxisAngle(s,o,N);if(C=g.Matrix3.fromQuaternion(x,C),t.tangent||t.bitangent){x=p.Quaternion.fromAxisAngle(s,-o,N);var P=g.Matrix3.fromQuaternion(x,U);y=r.Cartesian3.normalize(g.Matrix3.multiplyByVector(P,y,y),y),t.bitangent&&(u=r.Cartesian3.normalize(r.Cartesian3.cross(s,y,u),u))}}else C=g.Matrix3.clone(g.Matrix3.IDENTITY,C);var w=M;t.st&&(w.x=a.x,w.y=a.y);for(var A=b.length,F=3*A,G=new Float64Array(F),L=t.normal?new Float32Array(F):void 0,E=t.tangent?new Float32Array(F):void 0,T=t.bitangent?new Float32Array(F):void 0,D=t.st?new Float32Array(2*A):void 0,_=0,V=0,I=0,H=0,B=0,O=0;O<A;O++){var z=b[O];if(G[_++]=z.x,G[_++]=z.y,G[_++]=z.z,t.st){var S=l(g.Matrix3.multiplyByVector(C,z,k),R);n.Cartesian2.subtract(S,w,S);var j=d.CesiumMath.clamp(S.x/a.width,0,1),Y=d.CesiumMath.clamp(S.y/a.height,0,1);D[B++]=j,D[B++]=Y}t.normal&&(L[V++]=s.x,L[V++]=s.y,L[V++]=s.z),t.tangent&&(E[H++]=y.x,E[H++]=y.y,E[H++]=y.z),t.bitangent&&(T[I++]=u.x,T[I++]=u.y,T[I++]=u.z)}var q=new c.GeometryAttributes;return t.position&&(q.position=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G})),t.normal&&(q.normal=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),t.tangent&&(q.tangent=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.bitangent&&(q.bitangent=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),t.st&&(q.st=new p.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),new p.Geometry({attributes:q,indices:h,primitiveType:g.PrimitiveType.TRIANGLES})}function q(e){var t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,a=s.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT);this._vertexFormat=f.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=n.Ellipsoid.clone(s.defaultValue(e.ellipsoid,n.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=b.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+f.VertexFormat.packedLength+n.Ellipsoid.packedLength+2}q.fromPositions=function(e){return new q({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},q.pack=function(e,t,a){return a=s.defaultValue(a,0),a=b.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,a),n.Ellipsoid.pack(e._ellipsoid,t,a),a+=n.Ellipsoid.packedLength,f.VertexFormat.pack(e._vertexFormat,t,a),a+=f.VertexFormat.packedLength,t[a++]=e._stRotation,t[a]=e.packedLength,t};var J=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE),W=new f.VertexFormat,Z={polygonHierarchy:{}};return q.unpack=function(e,t,a){t=s.defaultValue(t,0);var r=b.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=r.startingIndex,delete r.startingIndex;var o=n.Ellipsoid.unpack(e,t,J);t+=n.Ellipsoid.packedLength;var i=f.VertexFormat.unpack(e,t,W);t+=f.VertexFormat.packedLength;var l=e[t++],p=e[t];return s.defined(a)||(a=new q(Z)),a._polygonHierarchy=r,a._ellipsoid=n.Ellipsoid.clone(o,a._ellipsoid),a._vertexFormat=f.VertexFormat.clone(i,a._vertexFormat),a._stRotation=l,a.packedLength=p,a},q.createGeometry=function(t){var n=t._vertexFormat,o=t._polygonHierarchy,i=t._stRotation,s=o.positions;if(!((s=e.arrayRemoveDuplicates(s,r.Cartesian3.equalsEpsilon,!0)).length<3)){var c=I,g=H,v=B,f=z,h=S;if(l.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(s,O,f,h)){if(c=r.Cartesian3.cross(f,h,c),c=r.Cartesian3.normalize(c,c),!r.Cartesian3.equalsEpsilon(O,r.Cartesian3.ZERO,d.CesiumMath.EPSILON6)){var C=t._ellipsoid.geodeticSurfaceNormal(O,j);r.Cartesian3.dot(c,C)<0&&(c=r.Cartesian3.negate(c,c),f=r.Cartesian3.negate(f,f))}var x=l.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(O,f,h),P=l.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(O,f,h);n.tangent&&(g=r.Cartesian3.clone(f,g)),n.bitangent&&(v=r.Cartesian3.clone(h,v));var w=b.PolygonGeometryLibrary.polygonsFromHierarchy(o,x,!1),A=w.hierarchy,F=w.polygons;if(0!==A.length){s=A[0].outerRing;for(var G=a.BoundingSphere.fromPoints(s),L=b.PolygonGeometryLibrary.computeBoundingRectangle(c,P,s,i,V),E=[],T=0;T<F.length;T++){var D=new y.GeometryInstance({geometry:Y(F[T],n,L,i,P,c,g,v)});E.push(D)}var _=u.GeometryPipeline.combineInstances(E)[0];_.attributes.position.values=new Float64Array(_.attributes.position.values),_.indices=m.IndexDatatype.createTypedArray(_.attributes.position.values.length/3,_.indices);var k=_.attributes;return n.position||delete k.position,new p.Geometry({attributes:k,indices:_.indices,primitiveType:_.primitiveType,boundingSphere:G})}}}},function(e,t){return s.defined(t)&&(e=q.unpack(e,t)),q.createGeometry(e)}}));
