define(["./when-b60132fc","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./FeatureDetection-806b12f0","./VertexFormat-6446fca0","./Cartesian2-47311507","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(e,t,a,r,n,o,i,u,m,p,c,y,s,l,b){"use strict";function A(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT);var a=e.defaultValue(t.vertexFormat,m.VertexFormat.DEFAULT);this._vertexFormat=a,this._workerName="createPlaneGeometry"}A.packedLength=m.VertexFormat.packedLength,A.pack=function(t,a,r){return r=e.defaultValue(r,0),m.VertexFormat.pack(t._vertexFormat,a,r),a};var F=new m.VertexFormat,f={vertexFormat:F};A.unpack=function(t,a,r){a=e.defaultValue(a,0);var n=m.VertexFormat.unpack(t,a,F);return e.defined(r)?(r._vertexFormat=m.VertexFormat.clone(n,r._vertexFormat),r):new A(f)};var v=new a.Cartesian3(-.5,-.5,0),d=new a.Cartesian3(.5,.5,0);return A.createGeometry=function(e){var r,m,p=e._vertexFormat,c=new i.GeometryAttributes;if(p.position){if((m=new Float64Array(12))[0]=v.x,m[1]=v.y,m[2]=0,m[3]=d.x,m[4]=v.y,m[5]=0,m[6]=d.x,m[7]=d.y,m[8]=0,m[9]=v.x,m[10]=d.y,m[11]=0,c.position=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m}),p.normal){var y=new Float32Array(12);y[0]=0,y[1]=0,y[2]=1,y[3]=0,y[4]=0,y[5]=1,y[6]=0,y[7]=0,y[8]=1,y[9]=0,y[10]=0,y[11]=1,c.normal=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})}if(p.st){var s=new Float32Array(8);s[0]=0,s[1]=0,s[2]=1,s[3]=0,s[4]=1,s[5]=1,s[6]=0,s[7]=1,c.st=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:s})}if(p.tangent){var l=new Float32Array(12);l[0]=1,l[1]=0,l[2]=0,l[3]=1,l[4]=0,l[5]=0,l[6]=1,l[7]=0,l[8]=0,l[9]=1,l[10]=0,l[11]=0,c.tangent=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:l})}if(p.bitangent){var b=new Float32Array(12);b[0]=0,b[1]=1,b[2]=0,b[3]=0,b[4]=1,b[5]=0,b[6]=0,b[7]=1,b[8]=0,b[9]=0,b[10]=1,b[11]=0,c.bitangent=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:b})}(r=new Uint16Array(6))[0]=0,r[1]=1,r[2]=2,r[3]=0,r[4]=2,r[5]=3}return new o.Geometry({attributes:c,indices:r,primitiveType:u.PrimitiveType.TRIANGLES,boundingSphere:new t.BoundingSphere(a.Cartesian3.ZERO,Math.sqrt(2))})},function(t,a){return e.defined(a)&&(t=A.unpack(t,a)),A.createGeometry(t)}}));
