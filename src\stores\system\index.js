import { ref } from "vue";
import { defineStore } from "pinia";
import axios from "axios";
import { getInfo } from "@/api";
import { AMAP_CONFIG, MAP_CONFIG, TDT_CONFIG, SDTDT_CONFIG } from "../../config";

const mapKeyData = getMapKeyData();

let isAuthenticated = true;

const useSystemStore = defineStore("system", () => {
  const locationData = ref({ center: [117.04135, 36.662320835] });
  const mapKey = ref(Array.isArray(mapKeyData) && mapKeyData.length ? mapKeyData[0] : mapKeyData);
  //获取位置
  async function initLocation() {
    return axios
      .get(`https://restapi.amap.com/v3/ip?&key=${AMAP_CONFIG.key}`)
      .then((res) => {
        if (res.data && res.data.status === "1") {
          console.log("location success");
          locationData.value = { ...res.data, center: getCenter(res.data.rectangle) };
        } else {
          console.log("location fail");
          console.log(res);
        }
      })
      .catch((err) => {
        console.log("location fail");
        console.log(err);
      });
  }
  //挑选可用的地图key
  async function pickAvailableMapKey() {
    if (!MAP_CONFIG.checkKey || !Array.isArray(mapKeyData) || mapKeyData.length < 2) {
      return;
    }
    const controller = new AbortController();
    const promises = [];
    for (let index = 0; index < mapKeyData.length; index++) {
      promises.push(checkKeyAvailable(mapKeyData[index], controller));
    }
    try {
      const availableKey = await Promise.any(promises);
      mapKey.value = availableKey;
      // 取消其他请求
      controller.abort();
    } catch (error) {
      console.log("配置的地图Key今天已用完");
    }
  }
  //检查登录状态
  async function checkLogin() {
    return new Promise((resolve, reject) => {
      getInfo()
        .then((res) => {
          if (res.user) {
            isAuthenticated = true;
          }
          resolve();
        })
        .catch(() => {
          resolve();
        });
    });
  }

  async function systemStoreInit() {
    // const allInit = [initLocation(), pickAvailableMapKey(), checkLogin()];
    const allInit = [initLocation(), pickAvailableMapKey()];
    await Promise.allSettled(allInit);
  }

  return { locationData, mapKey, systemStoreInit };
});
//获取中心点
function getCenter(rectangle) {
  try {
    const coordinates = rectangle.split(";");
    const coordinate1 = coordinates[0].split(",");
    const coordinate2 = coordinates[1].split(",");
    const centerLng = (Number(coordinate1[0]) + Number(coordinate2[0])) / 2;
    const centerLat = (Number(coordinate1[1]) + Number(coordinate2[1])) / 2;
    return [centerLng, centerLat];
  } catch (error) {
    console.log(error);
    return [];
  }
}

function getMapKeyData() {
  if (MAP_CONFIG.provider === "TDT") {
    return TDT_CONFIG.key;
  } else if (MAP_CONFIG.provider === "SDTDT") {
    return SDTDT_CONFIG.key;
  }
}
//校验地图key是否可用
function checkKeyAvailable(key, controller) {
  return new Promise((resolve, reject) => {
    const url = getMapResourceLink(key);
    if (!url) {
      reject();
    }
    axios
      .get(`${url}&${Date.now()}`, {
        signal: controller.signal,
      })
      .then((res) => {
        if (res.status === 200) {
          resolve(key);
        } else {
          reject();
        }
      })
      .catch((err) => {
        reject();
      });
  });
}
//地图key的校验链接（一个尽量小的切片），地图服务有变动时，链接可能需要改动
function getMapResourceLink(key) {
  const { defaultType, provider, projection } = MAP_CONFIG;
  if (defaultType !== "raster" && defaultType !== "vector") {
    return;
  }
  if (provider === "TDT") {
    const urlMark = projection === "EPSG:4326" ? "c" : "w";
    const urlLayer = defaultType === "raster" ? "cia" : "cva";
    return `${document.location.protocol}//t1.tianditu.gov.cn/${urlLayer}_${urlMark}/wmts?tilematrix=4&layer=${urlLayer}&style=default&tilerow=2&tilecol=15&tilematrixset=${urlMark}&format=tiles&service=WMTS&version=1.0.0&request=GetTile&tk=${key}`;
  } else if (provider === "SDTDT") {
    if (defaultType === "raster") {
      return `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmapdj?tilematrix=10&layer=SDRasterPubMapDJ&style=default&tilerow=147&tilecol=851&tilematrixset=rasterdj&format=image%2Fpng&service=WMTS&version=1.0.0&request=GetTile&tk=${key}`;
    } else {
      return `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdpubmap?tilematrix=8&layer=SDPubMap&style=default&tilerow=39&tilecol=214&tilematrixset=vector&format=image%2Fpng&service=WMTS&version=1.0.0&request=GetTile&tk=${key}`;
    }
  }
}

//http://t1.tianditu.gov.cn/cia_c/wmts?tilematrix=4&layer=cia&style=default&tilerow=2&tilecol=15&tilematrixset=c&format=tiles&service=WMTS&version=1.0.0&request=GetTile&tk=65884a3c3eb6fdee894709e06bad8d44
//http://t1.tianditu.gov.cn/cva_c/wmts?tilematrix=4&layer=cva&style=default&tilerow=2&tilecol=15&tilematrixset=c&format=tiles&service=WMTS&version=1.0.0&request=GetTile&tk=65884a3c3eb6fdee894709e06bad8d44

//http://service.sdmap.gov.cn/tileservice/sdrasterpubmapdj?tilematrix=10&layer=SDRasterPubMapDJ&style=default&tilerow=147&tilecol=851&tilematrixset=rasterdj&format=image%2Fpng&service=WMTS&version=1.0.0&request=GetTile&tk=548c3b49fcc664df52f47a8b38aa1054
//http://service.sdmap.gov.cn/tileservice/sdpubmap?tilematrix=8&layer=SDPubMap&style=default&tilerow=39&tilecol=214&tilematrixset=vector&format=image%2Fpng&service=WMTS&version=1.0.0&request=GetTile&tk=548c3b49fcc664df52f47a8b38aa1054
export { isAuthenticated, useSystemStore };
