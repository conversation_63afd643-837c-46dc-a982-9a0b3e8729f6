define(["exports","./Cartographic-3309dd0d","./when-b60132fc","./Check-7b2a090c","./Cartesian2-47311507","./EllipsoidGeodesic-0f19ac62","./EllipsoidRhumbLine-ed1a6bf4","./IntersectionTests-a793ed08","./Math-119be1a3","./FeatureDetection-806b12f0","./Plane-a3d8b3d2"],(function(a,e,r,t,n,i,o,s,c,l,u){"use strict";var h={numberOfPoints:function(a,r,t){var n=e.Cartesian3.distance(a,r);return Math.ceil(n/t)},numberOfPointsRhumbLine:function(a,e,r){var t=Math.pow(a.longitude-e.longitude,2)+Math.pow(a.latitude-e.latitude,2);return Math.ceil(Math.sqrt(t/(r*r)))}},f=new e.Cartographic;h.extractHeights=function(a,e){for(var r=a.length,t=new Array(r),n=0;n<r;n++){var i=a[n];t[n]=e.cartesianToCartographic(i,f).height}return t};var g=new l.Matrix4,C=new e.Cartesian3,d=new e.Cartesian3,p=new u.Plane(e.Cartesian3.UNIT_X,0),v=new e.Cartesian3,m=new u.Plane(e.Cartesian3.UNIT_X,0),w=new e.Cartesian3,P=new e.Cartesian3,T=[];function y(a,e,r){var t,n=T;if(n.length=a,e===r){for(t=0;t<a;t++)n[t]=e;return n}var i=(r-e)/a;for(t=0;t<a;t++){var o=e+t*i;n[t]=o}return n}var b=new e.Cartographic,A=new e.Cartographic,E=new e.Cartesian3,M=new e.Cartesian3,S=new e.Cartesian3,R=new i.EllipsoidGeodesic,D=new o.EllipsoidRhumbLine;function x(a,r,t,n,i,o,s,c,l){var u=n.scaleToGeodeticSurface(a,M),f=n.scaleToGeodeticSurface(r,S),g=h.numberOfPoints(a,r,t),C=n.cartesianToCartographic(u,b),d=n.cartesianToCartographic(f,A),p=y(g,i,o);l>0&&(p=function(a,e){var r=T;r.length=a;for(var t=0;t<a;t++)r[t]+=e*Math.sin(Math.PI*t/a);return r}(g,l)),R.setEndPoints(C,d);var v=R.surfaceDistance/g,m=c;C.height=i;var w=n.cartographicToCartesian(C,E);e.Cartesian3.pack(w,s,m),m+=3;for(var P=1;P<g;P++){var D=R.interpolateUsingSurfaceDistance(P*v,A);D.height=p[P],w=n.cartographicToCartesian(D,E),e.Cartesian3.pack(w,s,m),m+=3}return m}function G(a,r,t,n,i,s,c,l){var u=n.scaleToGeodeticSurface(a,M),f=n.scaleToGeodeticSurface(r,S),g=n.cartesianToCartographic(u,b),C=n.cartesianToCartographic(f,A),d=h.numberOfPointsRhumbLine(g,C,t),p=y(d,i,s);D.ellipsoid.equals(n)||(D=new o.EllipsoidRhumbLine(void 0,void 0,n)),D.setEndPoints(g,C);var v=D.surfaceDistance/d,m=l;g.height=i;var w=n.cartographicToCartesian(g,E);e.Cartesian3.pack(w,c,m),m+=3;for(var P=1;P<d;P++){var T=D.interpolateUsingSurfaceDistance(P*v,A);T.height=p[P],w=n.cartographicToCartesian(T,E),e.Cartesian3.pack(w,c,m),m+=3}return m}h.wrapLongitude=function(a,t){var n=[],i=[];if(r.defined(a)&&a.length>0){t=r.defaultValue(t,l.Matrix4.IDENTITY);var o=l.Matrix4.inverseTransformation(t,g),c=l.Matrix4.multiplyByPoint(o,e.Cartesian3.ZERO,C),h=e.Cartesian3.normalize(l.Matrix4.multiplyByPointAsVector(o,e.Cartesian3.UNIT_Y,d),d),f=u.Plane.fromPointNormal(c,h,p),T=e.Cartesian3.normalize(l.Matrix4.multiplyByPointAsVector(o,e.Cartesian3.UNIT_X,v),v),y=u.Plane.fromPointNormal(c,T,m),b=1;n.push(e.Cartesian3.clone(a[0]));for(var A=n[0],E=a.length,M=1;M<E;++M){var S=a[M];if(u.Plane.getPointDistance(y,A)<0||u.Plane.getPointDistance(y,S)<0){var R=s.IntersectionTests.lineSegmentPlane(A,S,f,w);if(r.defined(R)){var D=e.Cartesian3.multiplyByScalar(h,5e-9,P);u.Plane.getPointDistance(f,A)<0&&e.Cartesian3.negate(D,D),n.push(e.Cartesian3.add(R,D,new e.Cartesian3)),i.push(b+1),e.Cartesian3.negate(D,D),n.push(e.Cartesian3.add(R,D,new e.Cartesian3)),b=1}}n.push(e.Cartesian3.clone(a[M])),b++,A=S}i.push(b)}return{positions:n,lengths:i}},h.generateArc=function(a){r.defined(a)||(a={});var t=a.positions,i=t.length,o=r.defaultValue(a.ellipsoid,n.Ellipsoid.WGS84),s=r.defaultValue(a.height,0),l=Array.isArray(s);if(i<1)return[];if(1===i){var u=o.scaleToGeodeticSurface(t[0],M);if(0!==(s=l?s[0]:s)){var f=o.geodeticSurfaceNormal(u,E);e.Cartesian3.multiplyByScalar(f,s,f),e.Cartesian3.add(u,f,u)}return[u.x,u.y,u.z]}var g=a.minDistance;if(!r.defined(g)){var C=r.defaultValue(a.granularity,c.CesiumMath.RADIANS_PER_DEGREE);g=c.CesiumMath.chordLength(C,o.maximumRadius)}var d,p=0;for(d=0;d<i-1;d++)p+=h.numberOfPoints(t[d],t[d+1],g);var v=a.hMax,m=3*(p+1),w=new Array(m),P=0;for(d=0;d<i-1;d++){P=x(t[d],t[d+1],g,o,l?s[d]:s,l?s[d+1]:s,w,P,v)}T.length=0;var y=t[i-1],A=o.cartesianToCartographic(y,b);A.height=l?s[i-1]:s;var S=o.cartographicToCartesian(A,E);return e.Cartesian3.pack(S,w,m-3),w};var I=new e.Cartographic,N=new e.Cartographic;h.generateRhumbArc=function(a){r.defined(a)||(a={});var t=a.positions,i=t.length,o=r.defaultValue(a.ellipsoid,n.Ellipsoid.WGS84),s=r.defaultValue(a.height,0),l=Array.isArray(s);if(i<1)return[];if(1===i){var u=o.scaleToGeodeticSurface(t[0],M);if(0!==(s=l?s[0]:s)){var f=o.geodeticSurfaceNormal(u,E);e.Cartesian3.multiplyByScalar(f,s,f),e.Cartesian3.add(u,f,u)}return[u.x,u.y,u.z]}var g,C,d=r.defaultValue(a.granularity,c.CesiumMath.RADIANS_PER_DEGREE),p=0,v=o.cartesianToCartographic(t[0],I);for(g=0;g<i-1;g++)C=o.cartesianToCartographic(t[g+1],N),p+=h.numberOfPointsRhumbLine(v,C,d),v=e.Cartographic.clone(C,I);var m=3*(p+1),w=new Array(m),P=0;for(g=0;g<i-1;g++){P=G(t[g],t[g+1],d,o,l?s[g]:s,l?s[g+1]:s,w,P)}T.length=0;var y=t[i-1],A=o.cartesianToCartographic(y,b);A.height=l?s[i-1]:s;var S=o.cartographicToCartesian(A,E);return e.Cartesian3.pack(S,w,m-3),w},h.generateCartesianArc=function(a){for(var r=h.generateArc(a),t=r.length/3,n=new Array(t),i=0;i<t;i++)n[i]=e.Cartesian3.unpack(r,3*i);return n},h.generateCartesianRhumbArc=function(a){for(var r=h.generateRhumbArc(a),t=r.length/3,n=new Array(t),i=0;i<t;i++)n[i]=e.Cartesian3.unpack(r,3*i);return n},a.PolylinePipeline=h}));
