<template>
  <div class="header">
    <img class="logo" src="../assets/images/logo.png" alt="icon">
    <Time />
    <div style="flex: 1;"></div>
    <div class="button-box" v-if="pagesConfig.length">
      <template v-for="item in pagesConfig">
        <div class="button" v-if="!item.disabled" :class="{ active: route.path === item.routePath }"
          :style="{ order: item.order || 11 }" @click="onClick(item.routePath)">
          {{ item.name }}
        </div>
      </template>
    </div>
    <Weather />
  </div>
</template>
<script setup>
import Time from "@/components/common/Time.vue"
import Weather from "@/components/common/Weather.vue"
import { pagesConfig } from "@/dynamicPage/utils"
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

function onClick(path) {
  router.push({
    path,
    query: route.query
  })
}

</script>
<style scoped>
.header {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 80px;
  background-color: #eff5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1);
  padding-left: 100px;
  padding-right: 100px;
}

.logo {
  width: 70px;
  height: 70px;
}

.button-box {
  display: flex;
  align-items: center;
}

.button {
  --bg: #e74c3c;
  --text-color: #fff;
  position: relative;
  border: none;
  background: var(--bg);
  color: var(--text-color);
  padding: 0.5em 1em;
  margin: 0 1em;
  font-size: 19px;
  font-weight: bold;
  text-transform: uppercase;
  transition: 0.2s;
  border-radius: 5px;
  opacity: 0.8;
  letter-spacing: 1px;
  box-shadow: #c0392b 0px 7px 2px, #000 0px 8px 5px;
  cursor: pointer
}

.button:hover {
  opacity: 1;
}

.button:active {
  top: 4px;
  box-shadow: #c0392b 0px 3px 2px, #000 0px 3px 5px;
}

.active {
  opacity: 1;
  top: 4px;
  box-shadow: #c0392b 0px 3px 2px, #000 0px 3px 5px;
}
</style>
