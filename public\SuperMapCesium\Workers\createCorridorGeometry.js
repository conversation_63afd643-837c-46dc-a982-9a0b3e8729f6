define(["./arrayFill-4513d7ad","./arrayRemoveDuplicates-d2f048c5","./buildModuleUrl-8958744c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./PolylineVolumeGeometryLibrary-33fe7c53","./CorridorGeometryLibrary-4ed47ca6","./when-b60132fc","./Cartesian2-47311507","./GeometryAttribute-06a41648","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolygonPipeline-d328cdf1","./FeatureDetection-806b12f0","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-ce9a1fbb","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./PolylinePipeline-3454449c","./EllipsoidGeodesic-0f19ac62","./EllipsoidRhumbLine-ed1a6bf4","./earcut-2.2.1-20c8012f"],(function(t,e,r,a,i,o,n,s,l,d,u,m,f,y,c,p,h,g,C,b,v,A,_,w,G,T,E,V,F){"use strict";var L=new a.Cartesian3,P=new a.Cartesian3,x=new a.Cartesian3,N=new a.Cartesian3,D=new a.Cartesian3,M=new a.Cartesian3,O=new a.Cartesian3,I=new a.Cartesian3;function S(t,e){for(var r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function R(t,e,r,i,o,n){var l=t.normals,d=t.tangents,u=t.bitangents,m=a.Cartesian3.normalize(a.Cartesian3.cross(r,e,O),O);n.normal&&s.CorridorGeometryLibrary.addAttribute(l,e,i,o),n.tangent&&s.CorridorGeometryLibrary.addAttribute(d,m,i,o),n.bitangent&&s.CorridorGeometryLibrary.addAttribute(u,r,i,o)}function k(t,e,r){var i,n,d,f=t.positions,p=t.corners,h=t.endPositions,g=t.lefts,C=t.normals,b=new m.GeometryAttributes,v=0,A=0,_=0;for(n=0;n<f.length;n+=2)v+=d=f[n].length-3,_+=2*d,A+=f[n+1].length-3;for(v+=3,A+=3,n=0;n<p.length;n++){i=p[n];var w=p[n].leftPositions;l.defined(w)?(v+=d=w.length,_+=d):(A+=d=p[n].rightPositions.length,_+=d)}var G,T=l.defined(h);T&&(v+=G=h[0].length-3,A+=G,_+=6*(G/=3));var E,V,F,D,S,k,H=v+A,z=new Float64Array(H),U={normals:e.normal?new Float32Array(H):void 0,tangents:e.tangent?new Float32Array(H):void 0,bitangents:e.bitangent?new Float32Array(H):void 0},B=0,Y=H-1,W=L,q=P,J=G/2,j=y.IndexDatatype.createTypedArray(H/3,_),K=0;if(T){k=x,S=N;var Q=h[0];for(W=a.Cartesian3.fromArray(C,0,W),q=a.Cartesian3.fromArray(g,0,q),n=0;n<J;n++)k=a.Cartesian3.fromArray(Q,3*(J-1-n),k),S=a.Cartesian3.fromArray(Q,3*(J+n),S),s.CorridorGeometryLibrary.addAttribute(z,S,B),s.CorridorGeometryLibrary.addAttribute(z,k,void 0,Y),R(U,W,q,B,Y,e),D=(V=B/3)+1,F=(E=(Y-2)/3)-1,j[K++]=E,j[K++]=V,j[K++]=F,j[K++]=F,j[K++]=V,j[K++]=D,B+=3,Y-=3}var X,Z,$=0,tt=0,et=f[$++],rt=f[$++];for(z.set(et,B),z.set(rt,Y-rt.length+1),q=a.Cartesian3.fromArray(g,tt,q),d=rt.length-3,n=0;n<d;n+=3)X=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(et,n,O),O),Z=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(rt,d-n,I),I),R(U,W=a.Cartesian3.normalize(a.Cartesian3.add(X,Z,W),W),q,B,Y,e),D=(V=B/3)+1,F=(E=(Y-2)/3)-1,j[K++]=E,j[K++]=V,j[K++]=F,j[K++]=F,j[K++]=V,j[K++]=D,B+=3,Y-=3;for(X=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(et,d,O),O),Z=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(rt,d,I),I),W=a.Cartesian3.normalize(a.Cartesian3.add(X,Z,W),W),tt+=3,n=0;n<p.length;n++){var at,it,ot,nt=(i=p[n]).leftPositions,st=i.rightPositions,lt=M,dt=x,ut=N;if(W=a.Cartesian3.fromArray(C,tt,W),l.defined(nt)){for(R(U,W,q,void 0,Y,e),Y-=3,it=D,ot=F,at=0;at<nt.length/3;at++)lt=a.Cartesian3.fromArray(nt,3*at,lt),j[K++]=it,j[K++]=ot-at-1,j[K++]=ot-at,s.CorridorGeometryLibrary.addAttribute(z,lt,void 0,Y),dt=a.Cartesian3.fromArray(z,3*(ot-at-1),dt),ut=a.Cartesian3.fromArray(z,3*it,ut),R(U,W,q=a.Cartesian3.normalize(a.Cartesian3.subtract(dt,ut,q),q),void 0,Y,e),Y-=3;lt=a.Cartesian3.fromArray(z,3*it,lt),dt=a.Cartesian3.subtract(a.Cartesian3.fromArray(z,3*ot,dt),lt,dt),ut=a.Cartesian3.subtract(a.Cartesian3.fromArray(z,3*(ot-at),ut),lt,ut),R(U,W,q=a.Cartesian3.normalize(a.Cartesian3.add(dt,ut,q),q),B,void 0,e),B+=3}else{for(R(U,W,q,B,void 0,e),B+=3,it=F,ot=D,at=0;at<st.length/3;at++)lt=a.Cartesian3.fromArray(st,3*at,lt),j[K++]=it,j[K++]=ot+at,j[K++]=ot+at+1,s.CorridorGeometryLibrary.addAttribute(z,lt,B),dt=a.Cartesian3.fromArray(z,3*it,dt),ut=a.Cartesian3.fromArray(z,3*(ot+at),ut),R(U,W,q=a.Cartesian3.normalize(a.Cartesian3.subtract(dt,ut,q),q),B,void 0,e),B+=3;lt=a.Cartesian3.fromArray(z,3*it,lt),dt=a.Cartesian3.subtract(a.Cartesian3.fromArray(z,3*(ot+at),dt),lt,dt),ut=a.Cartesian3.subtract(a.Cartesian3.fromArray(z,3*ot,ut),lt,ut),R(U,W,q=a.Cartesian3.normalize(a.Cartesian3.negate(a.Cartesian3.add(ut,dt,q),q),q),void 0,Y,e),Y-=3}for(et=f[$++],rt=f[$++],et.splice(0,3),rt.splice(rt.length-3,3),z.set(et,B),z.set(rt,Y-rt.length+1),d=rt.length-3,tt+=3,q=a.Cartesian3.fromArray(g,tt,q),at=0;at<rt.length;at+=3)X=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(et,at,O),O),Z=r.geodeticSurfaceNormal(a.Cartesian3.fromArray(rt,d-at,I),I),R(U,W=a.Cartesian3.normalize(a.Cartesian3.add(X,Z,W),W),q,B,Y,e),V=(D=B/3)-1,E=(F=(Y-2)/3)+1,j[K++]=E,j[K++]=V,j[K++]=F,j[K++]=F,j[K++]=V,j[K++]=D,B+=3,Y-=3;B-=3,Y+=3}if(R(U,W=a.Cartesian3.fromArray(C,C.length-3,W),q,B,Y,e),T){B+=3,Y-=3,k=x,S=N;var mt=h[1];for(n=0;n<J;n++)k=a.Cartesian3.fromArray(mt,3*(G-n-1),k),S=a.Cartesian3.fromArray(mt,3*n,S),s.CorridorGeometryLibrary.addAttribute(z,k,void 0,Y),s.CorridorGeometryLibrary.addAttribute(z,S,B),R(U,W,q,B,Y,e),V=(D=B/3)-1,E=(F=(Y-2)/3)+1,j[K++]=E,j[K++]=V,j[K++]=F,j[K++]=F,j[K++]=V,j[K++]=D,B+=3,Y-=3}if(b.position=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:z}),e.st){var ft,yt,ct=new Float32Array(H/3*2),pt=0;if(T){v/=3,A/=3;var ht,gt=Math.PI/(G+1);yt=1/(v-G+1),ft=1/(A-G+1);var Ct=G/2;for(n=Ct+1;n<G+1;n++)ht=c.CesiumMath.PI_OVER_TWO+gt*n,ct[pt++]=ft*(1+Math.cos(ht)),ct[pt++]=.5*(1+Math.sin(ht));for(n=1;n<A-G+1;n++)ct[pt++]=n*ft,ct[pt++]=0;for(n=G;n>Ct;n--)ht=c.CesiumMath.PI_OVER_TWO-n*gt,ct[pt++]=1-ft*(1+Math.cos(ht)),ct[pt++]=.5*(1+Math.sin(ht));for(n=Ct;n>0;n--)ht=c.CesiumMath.PI_OVER_TWO-gt*n,ct[pt++]=1-yt*(1+Math.cos(ht)),ct[pt++]=.5*(1+Math.sin(ht));for(n=v-G;n>0;n--)ct[pt++]=n*yt,ct[pt++]=1;for(n=1;n<Ct+1;n++)ht=c.CesiumMath.PI_OVER_TWO+gt*n,ct[pt++]=yt*(1+Math.cos(ht)),ct[pt++]=.5*(1+Math.sin(ht))}else{for(yt=1/((v/=3)-1),ft=1/((A/=3)-1),n=0;n<A;n++)ct[pt++]=n*ft,ct[pt++]=0;for(n=v;n>0;n--)ct[pt++]=(n-1)*yt,ct[pt++]=1}b.st=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:ct})}return e.normal&&(b.normal=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:U.normals})),e.tangent&&(b.tangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:U.tangents})),e.bitangent&&(b.bitangent=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:U.bitangents})),{attributes:b,indices:j}}function H(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(var a=3;a<t.length;a+=3){var i=t[a],o=t[a+1],n=t[a+2];r[e++]=i,r[e++]=o,r[e++]=n,r[e++]=i,r[e++]=o,r[e++]=n}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function z(e,r){var i=new g.VertexFormat({position:r.position,normal:r.normal||r.bitangent||e.shadowVolume,tangent:r.tangent,bitangent:r.normal||r.bitangent,st:r.st}),n=e.ellipsoid,d=k(s.CorridorGeometryLibrary.computePositions(e),i,n),m=e.height,c=e.extrudedHeight,h=d.attributes,C=d.indices,b=h.position.values,v=b.length,A=new Float64Array(6*v),_=new Float64Array(v);_.set(b);var w,G=new Float64Array(4*v);G=H(b=p.PolygonPipeline.scaleToGeodeticHeight(b,m,n),0,G),G=H(_=p.PolygonPipeline.scaleToGeodeticHeight(_,c,n),2*v,G),A.set(b),A.set(_,v),A.set(G,2*v),h.position.values=A,h=function(t,e){if(!(e.normal||e.tangent||e.bitangent||e.st))return t;var r,i,o=t.position.values;(e.normal||e.bitangent)&&(r=t.normal.values,i=t.bitangent.values);var n,l=t.position.values.length/18,d=3*l,u=2*l,m=2*d;if(e.normal||e.bitangent||e.tangent){var f=e.normal?new Float32Array(6*d):void 0,y=e.tangent?new Float32Array(6*d):void 0,c=e.bitangent?new Float32Array(6*d):void 0,p=L,h=P,g=x,C=N,b=D,v=M,A=m;for(n=0;n<d;n+=3){var _=A+m;p=a.Cartesian3.fromArray(o,n,p),h=a.Cartesian3.fromArray(o,n+d,h),g=a.Cartesian3.fromArray(o,(n+3)%d,g),h=a.Cartesian3.subtract(h,p,h),g=a.Cartesian3.subtract(g,p,g),C=a.Cartesian3.normalize(a.Cartesian3.cross(h,g,C),C),e.normal&&(s.CorridorGeometryLibrary.addAttribute(f,C,_),s.CorridorGeometryLibrary.addAttribute(f,C,_+3),s.CorridorGeometryLibrary.addAttribute(f,C,A),s.CorridorGeometryLibrary.addAttribute(f,C,A+3)),(e.tangent||e.bitangent)&&(v=a.Cartesian3.fromArray(r,n,v),e.bitangent&&(s.CorridorGeometryLibrary.addAttribute(c,v,_),s.CorridorGeometryLibrary.addAttribute(c,v,_+3),s.CorridorGeometryLibrary.addAttribute(c,v,A),s.CorridorGeometryLibrary.addAttribute(c,v,A+3)),e.tangent&&(b=a.Cartesian3.normalize(a.Cartesian3.cross(v,C,b),b),s.CorridorGeometryLibrary.addAttribute(y,b,_),s.CorridorGeometryLibrary.addAttribute(y,b,_+3),s.CorridorGeometryLibrary.addAttribute(y,b,A),s.CorridorGeometryLibrary.addAttribute(y,b,A+3))),A+=6}if(e.normal){for(f.set(r),n=0;n<d;n+=3)f[n+d]=-r[n],f[n+d+1]=-r[n+1],f[n+d+2]=-r[n+2];t.normal.values=f}else t.normal=void 0;if(e.bitangent?(c.set(i),c.set(i,d),t.bitangent.values=c):t.bitangent=void 0,e.tangent){var w=t.tangent.values;y.set(w),y.set(w,d),t.tangent.values=y}}if(e.st){var G=t.st.values,T=new Float32Array(6*u);T.set(G),T.set(G,u);for(var E=2*u,V=0;V<2;V++){for(T[E++]=G[0],T[E++]=G[1],n=2;n<u;n+=2){var F=G[n],O=G[n+1];T[E++]=F,T[E++]=O,T[E++]=F,T[E++]=O}T[E++]=G[0],T[E++]=G[1]}t.st.values=T}return t}(h,r);var T=v/3;if(e.shadowVolume){var E=h.normal.values;v=E.length;var V=new Float32Array(6*v);for(w=0;w<v;w++)E[w]=-E[w];V.set(E,v),V=H(E,4*v,V),h.extrudeDirection=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:V}),r.normal||(h.normal=void 0)}if(l.defined(e.offsetAttribute)){var F=new Uint8Array(6*T);if(e.offsetAttribute===f.GeometryOffsetAttribute.TOP)F=t.arrayFill(F,1,0,T),F=t.arrayFill(F,1,2*T,4*T);else{var O=e.offsetAttribute===f.GeometryOffsetAttribute.NONE?0:1;F=t.arrayFill(F,O)}h.applyOffset=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:F})}var I=C.length,S=T+T,R=y.IndexDatatype.createTypedArray(A.length/3,2*I+3*S);R.set(C);var z,U,B,Y,W=I;for(w=0;w<I;w+=3){var q=C[w],J=C[w+1],j=C[w+2];R[W++]=j+T,R[W++]=J+T,R[W++]=q+T}for(w=0;w<S;w+=2)B=(z=w+S)+1,Y=(U=z+S)+1,R[W++]=z,R[W++]=U,R[W++]=B,R[W++]=B,R[W++]=U,R[W++]=Y;return{attributes:h,indices:R}}var U=new a.Cartesian3,B=new a.Cartesian3,Y=new a.Cartographic;function W(t,e,r,i,o,n){var s=a.Cartesian3.subtract(e,t,U);a.Cartesian3.normalize(s,s);var l=r.geodeticSurfaceNormal(t,B),d=a.Cartesian3.cross(s,l,U);a.Cartesian3.multiplyByScalar(d,i,d);var u=o.latitude,m=o.longitude,f=n.latitude,y=n.longitude;a.Cartesian3.add(t,d,B),r.cartesianToCartographic(B,Y);var c=Y.latitude,p=Y.longitude;u=Math.min(u,c),m=Math.min(m,p),f=Math.max(f,c),y=Math.max(y,p),a.Cartesian3.subtract(t,d,B),r.cartesianToCartographic(B,Y),c=Y.latitude,p=Y.longitude,u=Math.min(u,c),m=Math.min(m,p),f=Math.max(f,c),y=Math.max(y,p),o.latitude=u,o.longitude=m,n.latitude=f,n.longitude=y}var q=new a.Cartesian3,J=new a.Cartesian3,j=new a.Cartographic,K=new a.Cartographic;function Q(t,r,i,o,s){t=S(t,r);var u=e.arrayRemoveDuplicates(t,a.Cartesian3.equalsEpsilon),m=u.length;if(m<2||i<=0)return new d.Rectangle;var f,y,c=.5*i;if(j.latitude=Number.POSITIVE_INFINITY,j.longitude=Number.POSITIVE_INFINITY,K.latitude=Number.NEGATIVE_INFINITY,K.longitude=Number.NEGATIVE_INFINITY,o===n.CornerType.ROUNDED){var p=u[0];a.Cartesian3.subtract(p,u[1],q),a.Cartesian3.normalize(q,q),a.Cartesian3.multiplyByScalar(q,c,q),a.Cartesian3.add(p,q,J),r.cartesianToCartographic(J,Y),f=Y.latitude,y=Y.longitude,j.latitude=Math.min(j.latitude,f),j.longitude=Math.min(j.longitude,y),K.latitude=Math.max(K.latitude,f),K.longitude=Math.max(K.longitude,y)}for(var h=0;h<m-1;++h)W(u[h],u[h+1],r,c,j,K);var g=u[m-1];a.Cartesian3.subtract(g,u[m-2],q),a.Cartesian3.normalize(q,q),a.Cartesian3.multiplyByScalar(q,c,q),a.Cartesian3.add(g,q,J),W(g,J,r,c,j,K),o===n.CornerType.ROUNDED&&(r.cartesianToCartographic(J,Y),f=Y.latitude,y=Y.longitude,j.latitude=Math.min(j.latitude,f),j.longitude=Math.min(j.longitude,y),K.latitude=Math.max(K.latitude,f),K.longitude=Math.max(K.longitude,y));var C=l.defined(s)?s:new d.Rectangle;return C.north=K.latitude,C.south=j.latitude,C.east=K.longitude,C.west=j.longitude,C}function X(t){var e=(t=l.defaultValue(t,l.defaultValue.EMPTY_OBJECT)).positions,r=t.width,i=l.defaultValue(t.height,0),o=l.defaultValue(t.extrudedHeight,i);this._positions=e,this._ellipsoid=d.Ellipsoid.clone(l.defaultValue(t.ellipsoid,d.Ellipsoid.WGS84)),this._vertexFormat=g.VertexFormat.clone(l.defaultValue(t.vertexFormat,g.VertexFormat.DEFAULT)),this._width=r,this._height=Math.max(i,o),this._extrudedHeight=Math.min(i,o),this._cornerType=l.defaultValue(t.cornerType,n.CornerType.ROUNDED),this._granularity=l.defaultValue(t.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=l.defaultValue(t.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*a.Cartesian3.packedLength+d.Ellipsoid.packedLength+g.VertexFormat.packedLength+7}X.pack=function(t,e,r){r=l.defaultValue(r,0);var i=t._positions,o=i.length;e[r++]=o;for(var n=0;n<o;++n,r+=a.Cartesian3.packedLength)a.Cartesian3.pack(i[n],e,r);return d.Ellipsoid.pack(t._ellipsoid,e,r),r+=d.Ellipsoid.packedLength,g.VertexFormat.pack(t._vertexFormat,e,r),r+=g.VertexFormat.packedLength,e[r++]=t._width,e[r++]=t._height,e[r++]=t._extrudedHeight,e[r++]=t._cornerType,e[r++]=t._granularity,e[r++]=t._shadowVolume?1:0,e[r]=l.defaultValue(t._offsetAttribute,-1),e};var Z=d.Ellipsoid.clone(d.Ellipsoid.UNIT_SPHERE),$=new g.VertexFormat,tt={positions:void 0,ellipsoid:Z,vertexFormat:$,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};return X.unpack=function(t,e,r){e=l.defaultValue(e,0);for(var i=t[e++],o=new Array(i),n=0;n<i;++n,e+=a.Cartesian3.packedLength)o[n]=a.Cartesian3.unpack(t,e);var s=d.Ellipsoid.unpack(t,e,Z);e+=d.Ellipsoid.packedLength;var u=g.VertexFormat.unpack(t,e,$);e+=g.VertexFormat.packedLength;var m=t[e++],f=t[e++],y=t[e++],c=t[e++],p=t[e++],h=1===t[e++],C=t[e];return l.defined(r)?(r._positions=o,r._ellipsoid=d.Ellipsoid.clone(s,r._ellipsoid),r._vertexFormat=g.VertexFormat.clone(u,r._vertexFormat),r._width=m,r._height=f,r._extrudedHeight=y,r._cornerType=c,r._granularity=p,r._shadowVolume=h,r._offsetAttribute=-1===C?void 0:C,r):(tt.positions=o,tt.width=m,tt.height=f,tt.extrudedHeight=y,tt.cornerType=c,tt.granularity=p,tt.shadowVolume=h,tt.offsetAttribute=-1===C?void 0:C,new X(tt))},X.computeRectangle=function(t,e){var r=(t=l.defaultValue(t,l.defaultValue.EMPTY_OBJECT)).positions,a=t.width;return Q(r,l.defaultValue(t.ellipsoid,d.Ellipsoid.WGS84),a,l.defaultValue(t.cornerType,n.CornerType.ROUNDED),e)},X.createGeometry=function(i){var n=i._positions,d=i._width,m=i._ellipsoid;n=S(n,m);var y=e.arrayRemoveDuplicates(n,a.Cartesian3.equalsEpsilon);if(!(y.length<2||d<=0)){var g,C=i._height,b=i._extrudedHeight,v=!c.CesiumMath.equalsEpsilon(C,b,0,c.CesiumMath.EPSILON2),A=i._vertexFormat,_={ellipsoid:m,positions:y,width:d,cornerType:i._cornerType,granularity:i._granularity,saveAttributes:!0};if(v)_.height=C,_.extrudedHeight=b,_.shadowVolume=i._shadowVolume,_.offsetAttribute=i._offsetAttribute,g=z(_,A);else if((g=k(s.CorridorGeometryLibrary.computePositions(_),A,m)).attributes.position.values=p.PolygonPipeline.scaleToGeodeticHeight(g.attributes.position.values,C,m),l.defined(i._offsetAttribute)){var w=i._offsetAttribute===f.GeometryOffsetAttribute.NONE?0:1,G=g.attributes.position.values.length,T=new Uint8Array(G/3);t.arrayFill(T,w),g.attributes.applyOffset=new u.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:T})}var E=g.attributes,V=r.BoundingSphere.fromVertices(E.position.values,void 0,3);return A.position||(g.attributes.position.values=void 0),new u.Geometry({attributes:E,indices:g.indices,primitiveType:h.PrimitiveType.TRIANGLES,boundingSphere:V,offsetAttribute:i._offsetAttribute})}},X.createShadowVolume=function(t,e,r){var a=t._granularity,i=t._ellipsoid,o=e(a,i),n=r(a,i);return new X({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:o,height:n,vertexFormat:g.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(X.prototype,{rectangle:{get:function(){return l.defined(this._rectangle)||(this._rectangle=Q(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),function(t,e){return l.defined(e)&&(t=X.unpack(t,e)),t._ellipsoid=d.Ellipsoid.clone(t._ellipsoid),X.createGeometry(t)}}));
