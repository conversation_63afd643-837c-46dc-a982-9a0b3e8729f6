export default {
  url: "/api/chart",
  method: "get",
  response: () => {
    return {
      "leftOne|3": [
        {
          key: "@province",
          "value|7": ["@integer(2000, 5000)"],
        },
      ],
      "leftTwo|10": [
        {
          key: "@province",
          value: "@integer(100, 800)",
        },
      ],
      "leftThree|5": [
        {
          key: "@province",
          value: "@integer(100, 800)",
        },
      ],
      "rightOne|3": [
        {
          key: "@province",
          value: [
            "@integer(1000, 6500)",
            "@integer(8000, 16000)",
            "@integer(10000, 30000)",
            "@integer(20000, 38000)",
            "@integer(40000, 52000)",
            "@integer(10000, 25000)",
          ],
        },
      ],
      "rightTwo|2": [
        {
          key: "@province",
          value: "@integer(1, 100)",
        },
      ],
      "rightThree|5": [
        {
          key: "@province",
          value: "@integer(10, 100)",
        },
      ],
    };
  },
};
