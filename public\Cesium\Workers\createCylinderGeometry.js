define(["./CylinderGeometry-c437b77a","./defaultValue-f6d5e6da","./Transforms-c842a68c","./Matrix3-b2351961","./Math-355606c6","./Matrix2-7a8e9daf","./RuntimeError-9b4ce3fb","./combine-0c102d93","./ComponentDatatype-ab629b88","./WebGLConstants-7f557f93","./CylinderGeometryLibrary-4d7f606d","./GeometryAttribute-0e790d82","./GeometryAttributes-1e4ddcd2","./GeometryOffsetAttribute-2579b8d2","./IndexDatatype-a9b1bc18","./VertexFormat-fbdec922"],(function(e,t,r,n,i,o,a,y,m,u,G,d,f,s,b,c){"use strict";return function(r,n){return t.defined(n)&&(r=e.CylinderGeometry.unpack(r,n)),e.CylinderGeometry.createGeometry(r)}}));
