<template>
  <div class="enhanced-cesium-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>增强型Cesium三维地图</h1>
      <p class="subtitle">集成场景漫游、数据标签、分屏比较、洪水淹没等功能</p>
    </div>
    
    <!-- 增强的Cesium组件 -->
    <EnhancedCesium />
    
    <!-- 功能说明面板 -->
    <div class="info-panel" v-if="showInfo">
      <div class="info-header">
        <h3>功能说明</h3>
        <button @click="toggleInfo">×</button>
      </div>
      <div class="info-content">
        <div class="feature-item">
          <h4>🚶 场景漫游</h4>
          <p>支持预设路径的自动漫游，实时显示地理坐标和海拔高度，提供高精度的地形剖面分析。</p>
        </div>
        
        <div class="feature-item">
          <h4>🏷️ 数据标签</h4>
          <p>在地图上添加交互式标签，显示工程关键信息，支持点击查看详细数据。</p>
        </div>
        
        <div class="feature-item">
          <h4>📱 分屏比较</h4>
          <p>同时显示不同时期或不同方案的模型，便于快速对比分析设计差异。</p>
        </div>
        
        <div class="feature-item">
          <h4>🌊 洪水淹没</h4>
          <p>三维洪水淹没模拟，可在任意视角观察洪水水深和淹没范围，效果直观逼真。</p>
        </div>
      </div>
    </div>
    
    <!-- 信息按钮 -->
    <button class="info-toggle" @click="toggleInfo" v-if="!showInfo">
      ℹ️ 功能说明
    </button>
    
    <!-- 返回按钮 -->
    <router-link to="/" class="back-button">
      ← 返回首页
    </router-link>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EnhancedCesium from '@/components/cesium/enhanced/index.vue'

const showInfo = ref(true)

function toggleInfo() {
  showInfo.value = !showInfo.value
}
</script>

<style scoped>
.enhanced-cesium-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
}

.page-header {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.page-header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #4CAF50;
}

.subtitle {
  margin: 0;
  font-size: 14px;
  color: #ccc;
  max-width: 400px;
}

.info-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  z-index: 2000;
  overflow: hidden;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(76, 175, 80, 0.1);
}

.info-header h3 {
  margin: 0;
  color: #4CAF50;
  font-size: 20px;
}

.info-header button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}

.info-header button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.info-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  color: white;
}

.feature-item {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item h4 {
  margin: 0 0 8px 0;
  color: #4CAF50;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-item p {
  margin: 0;
  line-height: 1.5;
  color: #ccc;
  font-size: 14px;
}

.info-toggle {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  z-index: 1000;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
}

.info-toggle:hover {
  background: rgba(76, 175, 80, 1);
  transform: translateY(-2px);
}

.back-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  z-index: 1000;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 滚动条样式 */
.info-content::-webkit-scrollbar {
  width: 6px;
}

.info-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.info-content::-webkit-scrollbar-thumb {
  background: rgba(76, 175, 80, 0.6);
  border-radius: 3px;
}

.info-content::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 175, 80, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    border-radius: 0;
  }
  
  .page-header h1 {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .info-panel {
    width: 95vw;
    max-height: 85vh;
  }
  
  .back-button {
    position: relative;
    display: block;
    text-align: center;
    margin: 10px;
  }
}
</style>
