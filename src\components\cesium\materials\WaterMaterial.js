// 真实的水体材质，支持波浪、反射、透明度等效果
Cesium.Material.WaterMaterialType = 'WaterMaterial';
Cesium.Material.WaterMaterialSource = `
  uniform sampler2D normalMap;
  uniform float frequency;
  uniform float animationSpeed;
  uniform float amplitude;
  uniform float specularIntensity;
  uniform vec3 waterColor;
  uniform float transparency;
  
  czm_material czm_getMaterial(czm_materialInput materialInput) {
    czm_material material = czm_getDefaultMaterial(materialInput);
    
    vec2 st = materialInput.st;
    float time = czm_frameNumber * animationSpeed / 1000.0;
    
    // 创建波浪效果
    vec2 wave1 = vec2(sin(st.x * frequency + time), cos(st.y * frequency + time)) * amplitude;
    vec2 wave2 = vec2(cos(st.x * frequency * 1.5 - time * 0.8), sin(st.y * frequency * 1.5 - time * 0.8)) * amplitude * 0.7;
    
    vec2 distortedSt = st + wave1 + wave2;
    
    // 法线贴图采样
    vec3 normalSample = texture2D(normalMap, distortedSt).rgb;
    vec3 normal = normalize(normalSample * 2.0 - 1.0);
    
    // 水体颜色
    material.diffuse = waterColor;
    material.alpha = transparency;
    material.normal = normal;
    material.specular = specularIntensity;
    material.shininess = 64.0;
    
    // 添加泡沫效果
    float foam = smoothstep(0.7, 1.0, sin(st.x * 20.0 + time * 2.0) * cos(st.y * 20.0 + time * 2.0));
    material.diffuse = mix(material.diffuse, vec3(1.0), foam * 0.3);
    
    return material;
  }
`;

Cesium.Material._materialCache.addMaterial(Cesium.Material.WaterMaterialType, {
  fabric: {
    type: Cesium.Material.WaterMaterialType,
    uniforms: {
      normalMap: Cesium.Material.DefaultImageId,
      frequency: 10.0,
      animationSpeed: 0.01,
      amplitude: 0.01,
      specularIntensity: 0.5,
      waterColor: new Cesium.Color(0.0, 0.4, 0.8, 1.0),
      transparency: 0.7
    },
    source: Cesium.Material.WaterMaterialSource
  },
  translucent: function(material) {
    return material.uniforms.transparency < 1.0;
  }
});

// 洪水淹没材质属性
Cesium.WaterMaterialProperty = function(options) {
  options = Cesium.defaultValue(options, {});
  
  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._transparency = undefined;
  this._transparencySubscription = undefined;
  this._frequency = undefined;
  this._frequencySubscription = undefined;
  this._animationSpeed = undefined;
  this._animationSpeedSubscription = undefined;
  this._amplitude = undefined;
  this._amplitudeSubscription = undefined;
  this._specularIntensity = undefined;
  this._specularIntensitySubscription = undefined;
  
  this.color = options.color;
  this.transparency = options.transparency;
  this.frequency = options.frequency;
  this.animationSpeed = options.animationSpeed;
  this.amplitude = options.amplitude;
  this.specularIntensity = options.specularIntensity;
};

Object.defineProperties(Cesium.WaterMaterialProperty.prototype, {
  isConstant: {
    get: function() {
      return false;
    }
  },
  definitionChanged: {
    get: function() {
      return this._definitionChanged;
    }
  },
  color: Cesium.createPropertyDescriptor('color'),
  transparency: Cesium.createPropertyDescriptor('transparency'),
  frequency: Cesium.createPropertyDescriptor('frequency'),
  animationSpeed: Cesium.createPropertyDescriptor('animationSpeed'),
  amplitude: Cesium.createPropertyDescriptor('amplitude'),
  specularIntensity: Cesium.createPropertyDescriptor('specularIntensity')
});

Cesium.WaterMaterialProperty.prototype.getType = function(time) {
  return Cesium.Material.WaterMaterialType;
};

Cesium.WaterMaterialProperty.prototype.getValue = function(time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  
  result.waterColor = Cesium.Property.getValueOrClonedDefault(this._color, time, Cesium.Color.CYAN, result.waterColor);
  result.transparency = Cesium.Property.getValueOrDefault(this._transparency, time, 0.7);
  result.frequency = Cesium.Property.getValueOrDefault(this._frequency, time, 10.0);
  result.animationSpeed = Cesium.Property.getValueOrDefault(this._animationSpeed, time, 0.01);
  result.amplitude = Cesium.Property.getValueOrDefault(this._amplitude, time, 0.01);
  result.specularIntensity = Cesium.Property.getValueOrDefault(this._specularIntensity, time, 0.5);
  
  return result;
};

Cesium.WaterMaterialProperty.prototype.equals = function(other) {
  return this === other ||
    (other instanceof Cesium.WaterMaterialProperty &&
     Cesium.Property.equals(this._color, other._color) &&
     Cesium.Property.equals(this._transparency, other._transparency) &&
     Cesium.Property.equals(this._frequency, other._frequency) &&
     Cesium.Property.equals(this._animationSpeed, other._animationSpeed) &&
     Cesium.Property.equals(this._amplitude, other._amplitude) &&
     Cesium.Property.equals(this._specularIntensity, other._specularIntensity));
};

// 将材质属性添加到Cesium全局对象
if (typeof window !== 'undefined' && window.Cesium) {
  window.Cesium.WaterMaterialProperty = Cesium.WaterMaterialProperty;
}
