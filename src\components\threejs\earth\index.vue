<template>
  <div ref="box" class="earth-box"></div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import * as THREE from 'three'
import { CSS2DObject, CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { createEarthObj } from "./earth"
import { cityList, flyLineDatas } from "./testData"
import { createStarsBackground } from "./starsBg"

const props = defineProps({
  position: {
    type: Number
  }
})

const box = ref(null)
let boxWith
let boxHeight

let renderer
let renderer2d
let scene
let camera
let orbitControl
let earthAnimateLoop

function init() {
  initRenderer()
  initScene()
  initCamera()
  initLight()
  initControls()
  animate()
  load()
}

function initRenderer() {
  //主渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true
  });
  renderer.shadowMap.enabled = false;
  renderer.shadowMap.type = THREE.PCFShadowMap;
  renderer.setSize(boxWith, boxHeight)

  box.value.appendChild(renderer.domElement);
}

function initRenderer2d() {
  renderer2d = new CSS2DRenderer();
  renderer2d.setSize(boxWith, boxHeight)
  //设置样式
  renderer2d.domElement.style.position = 'fixed'
  renderer2d.domElement.style.top = '0px'
  renderer2d.domElement.style.left = '0px'
  renderer2d.domElement.style.zIndex = '10'
  renderer2d.domElement.style.pointerEvents = 'none'

  box.value.appendChild(renderer2d.domElement);
}

function initScene() {
  scene = new THREE.Scene()
}

function initCamera() {
  const aspect = boxWith / boxHeight;
  camera = new THREE.PerspectiveCamera(75, aspect, 1, 5000)
  camera.position.set(25, 20, 25);
}

function initLight() {
  /**
  * 光源设置
  */
  // 平行光1
  const directionalLight1 = new THREE.DirectionalLight(0x80b5ff, 1);
  directionalLight1.position.set(-250, 250, 100);
  scene.add(directionalLight1);
  const directionalLight2 = new THREE.DirectionalLight(0x80d4ff, 1);
  directionalLight2.position.set(-250, 250, 100);
  scene.add(directionalLight2);
  const directionalLight3 = new THREE.DirectionalLight(0x80b5ff, 1);
  directionalLight3.position.set(-250, 250, 100);
  scene.add(directionalLight3);
  const directionalLight4 = new THREE.DirectionalLight(0xc2ffff, 1);
  directionalLight4.position.set(-250, 250, 100);
  scene.add(directionalLight4);
  // 点光1
  const pointLight1 = new THREE.PointLight(0x80d4ff, 1);
  pointLight1.position.set(-250, 250, 100);
  scene.add(pointLight1);

  // 半球光1
  const hemisphereLight1 = new THREE.HemisphereLight(0xffffff, 0x3d6399, 1);
  hemisphereLight1.position.set(-250, 250, 100);
  scene.add(hemisphereLight1);
  const hemisphereLight2 = new THREE.HemisphereLight(0xffffff, 0x3d6399, 1);
  hemisphereLight2.position.set(-250, 250, 100);
  scene.add(hemisphereLight2);
  //环境光
  const ambient1 = new THREE.AmbientLight(0x002bff, 0.8);
  scene.add(ambient1);
}

function initControls() {
  orbitControl = new OrbitControls(camera, renderer.domElement)
  //阻尼
  orbitControl.enableDamping = true
  //缩放比例
  orbitControl.minZoom = 0.5;
  orbitControl.maxZoom = 2;
  //竖直旋转
  orbitControl.minPolarAngle = 0;
  orbitControl.maxPolarAngle = Math.PI / 2;
  //旋转
  orbitControl.enableRotate = true;
  //缩放
  orbitControl.enableZoom = true;
  //自动旋转
  orbitControl.autoRotate = true;
  //平移
  orbitControl.enablePan = false;
  orbitControl.update();
}

function animate() {
  const animation = () => {
    orbitControl.update();
    renderer.render(scene, camera)
    if (typeof earthAnimateLoop === 'function') {
      earthAnimateLoop()
    }
  }
  renderer.setAnimationLoop(animation)
}

function load() {
  const stars = createStarsBackground()
  scene.add(stars)
  const { object3D, earthAnimate } = createEarthObj(cityList, flyLineDatas)
  earthAnimateLoop = earthAnimate
  scene.add(object3D)
  // const axes = new THREE.AxesHelper(2000)
  // scene.add(axes)
}

onMounted(() => {
  boxWith = box.value.clientWidth
  boxHeight = box.value.clientHeight
  init()
})
</script>
<style scoped>
.earth-box {
  width: 100%;
  height: 100%;
  background-color: black;
}
</style>
