define(["./EllipsoidTangentPlane-ce9a1fbb","./buildModuleUrl-8958744c","./Cartesian2-47311507","./Cartographic-3309dd0d","./when-b60132fc","./TerrainEncoding-895d4619","./Math-119be1a3","./FeatureDetection-806b12f0","./OrientedBoundingBox-08964f84","./RuntimeError-4a5c8994","./GeometryAttribute-06a41648","./WebMercatorProjection-01b1b5e7","./createTaskProcessorWorker","./Check-7b2a090c","./Cartesian4-3ca25aab","./IntersectionTests-a793ed08","./Plane-a3d8b3d2","./Event-16a2dfbf","./AttributeCompression-90851096","./ComponentDatatype-c140a87d","./WebGLConstants-4ae0db90","./PolygonPipeline-d328cdf1","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-ed1a6bf4"],(function(e,t,i,a,r,n,o,s,u,h,d,c,l,g,m,p,v,E,I,f,C,T,M,b){"use strict";var N=Uint16Array.BYTES_PER_ELEMENT,x=Int32Array.BYTES_PER_ELEMENT,S=Uint32Array.BYTES_PER_ELEMENT,P=Float32Array.BYTES_PER_ELEMENT,w=Float64Array.BYTES_PER_ELEMENT;function B(e,t,i){i=r.defaultValue(i,o.CesiumMath);for(var a=e.length,n=0;n<a;++n)if(i.equalsEpsilon(e[n],t,o.CesiumMath.EPSILON12))return n;return-1}var y=new a.Cartographic,A=new a.Cartesian3,R=new a.Cartesian3,_=new a.Cartesian3,F=new s.Matrix4;function W(e,t,n,u,h,d,c,l,g,m){for(var p=c.length,v=0;v<p;++v){var E=c[v],I=E.cartographic,f=E.index,C=e.length,T=I.longitude,M=I.latitude;M=o.CesiumMath.clamp(M,-o.CesiumMath.PI_OVER_TWO,o.CesiumMath.PI_OVER_TWO);var b=I.height-d.skirtHeight;d.hMin=Math.min(d.hMin,b),a.Cartographic.fromRadians(T,M,b,y),g&&(y.longitude+=l),g?v===p-1?y.latitude+=m:0===v&&(y.latitude-=m):y.latitude+=l;var N=d.ellipsoid.cartographicToCartesian(y);e.push(N),t.push(b),n.push(i.Cartesian2.clone(n[f])),u.length>0&&u.push(u[f]),s.Matrix4.multiplyByPoint(d.toENU,N,A);var x=d.minimum,S=d.maximum;a.Cartesian3.minimumByComponent(A,x,x),a.Cartesian3.maximumByComponent(A,S,S);var P=d.lastBorderPoint;if(r.defined(P)){var w=P.index;h.push(w,C-1,C,C,f,w)}d.lastBorderPoint=E}}return l((function(l,g){l.ellipsoid=i.Ellipsoid.clone(l.ellipsoid),l.rectangle=i.Rectangle.clone(l.rectangle);var m=function(l,g,m,p,v,E,I,f,C,T){var M,b,O,U,Y,k;r.defined(p)?(M=p.west,b=p.south,O=p.east,U=p.north,Y=p.width,k=p.height):(M=o.CesiumMath.toRadians(v.west),b=o.CesiumMath.toRadians(v.south),O=o.CesiumMath.toRadians(v.east),U=o.CesiumMath.toRadians(v.north),Y=o.CesiumMath.toRadians(p.width),k=o.CesiumMath.toRadians(p.height));var V,L,H=[b,U],D=[M,O],G=d.Transforms.eastNorthUpToFixedFrame(g,m),j=s.Matrix4.inverseTransformation(G,F);f&&(V=c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(b),L=1/(c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(U)-V));var z=new DataView(l),q=Number.POSITIVE_INFINITY,J=Number.NEGATIVE_INFINITY,K=R;K.x=Number.POSITIVE_INFINITY,K.y=Number.POSITIVE_INFINITY,K.z=Number.POSITIVE_INFINITY;var Q=_;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;var X,Z,$=0,ee=0,te=0;for(Z=0;Z<4;++Z){var ie=$;X=z.getUint32(ie,!0),ie+=S;var ae=o.CesiumMath.toRadians(180*z.getFloat64(ie,!0));ie+=w,-1===B(D,ae)&&D.push(ae);var re=o.CesiumMath.toRadians(180*z.getFloat64(ie,!0));ie+=w,-1===B(H,re)&&H.push(re),ie+=2*w;var ne=z.getInt32(ie,!0);ie+=x,ee+=ne,te+=3*(ne=z.getInt32(ie,!0)),$+=X+S}var oe=[],se=[],ue=new Array(ee),he=new Array(ee),de=new Array(ee),ce=f?new Array(ee):[],le=new Array(te),ge=[],me=[],pe=[],ve=[],Ee=0,Ie=0;for($=0,Z=0;Z<4;++Z){X=z.getUint32($,!0);var fe=$+=S,Ce=o.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=w;var Te=o.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=w;var Me=o.CesiumMath.toRadians(180*z.getFloat64($,!0)),be=.5*Me;$+=w;var Ne=o.CesiumMath.toRadians(180*z.getFloat64($,!0)),xe=.5*Ne;$+=w;var Se=z.getInt32($,!0);$+=x;var Pe=z.getInt32($,!0);$+=x,$+=x;for(var we=new Array(Se),Be=0;Be<Se;++Be){var ye=Ce+z.getUint8($++)*Me;y.longitude=ye;var Ae=Te+z.getUint8($++)*Ne;y.latitude=Ae;var Re=z.getFloat32($,!0);if($+=P,0!==Re&&Re<T&&(Re*=-Math.pow(2,C)),Re*=6371010*E,y.height=Re,-1!==B(D,ye)||-1!==B(H,Ae)){var _e=B(oe,y,a.Cartographic);if(-1!==_e){we[Be]=se[_e];continue}oe.push(a.Cartographic.clone(y)),se.push(Ee)}we[Be]=Ee,Math.abs(ye-M)<be?ge.push({index:Ee,cartographic:a.Cartographic.clone(y)}):Math.abs(ye-O)<be?pe.push({index:Ee,cartographic:a.Cartographic.clone(y)}):Math.abs(Ae-b)<xe?me.push({index:Ee,cartographic:a.Cartographic.clone(y)}):Math.abs(Ae-U)<xe&&ve.push({index:Ee,cartographic:a.Cartographic.clone(y)}),q=Math.min(Re,q),J=Math.max(Re,J),de[Ee]=Re;var Fe=m.cartographicToCartesian(y);ue[Ee]=Fe,f&&(ce[Ee]=(c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(Ae)-V)*L),s.Matrix4.multiplyByPoint(j,Fe,A),a.Cartesian3.minimumByComponent(A,K,K),a.Cartesian3.maximumByComponent(A,Q,Q);var We=(ye-M)/(O-M);We=o.CesiumMath.clamp(We,0,1);var Oe=(Ae-b)/(U-b);Oe=o.CesiumMath.clamp(Oe,0,1),he[Ee]=new i.Cartesian2(We,Oe),++Ee}for(var Ue=3*Pe,Ye=0;Ye<Ue;++Ye,++Ie)le[Ie]=we[z.getUint16($,!0)],$+=N;if(X!==$-fe)throw new h.RuntimeError("Invalid terrain tile.")}ue.length=Ee,he.length=Ee,de.length=Ee,f&&(ce.length=Ee);var ke=Ee,Ve=Ie,Le={hMin:q,lastBorderPoint:void 0,skirtHeight:I,toENU:j,ellipsoid:m,minimum:K,maximum:Q};ge.sort((function(e,t){return t.cartographic.latitude-e.cartographic.latitude})),me.sort((function(e,t){return e.cartographic.longitude-t.cartographic.longitude})),pe.sort((function(e,t){return e.cartographic.latitude-t.cartographic.latitude})),ve.sort((function(e,t){return t.cartographic.longitude-e.cartographic.longitude}));var He=1e-5;if(W(ue,de,he,ce,le,Le,ge,-He*Y,!0,-He*k),W(ue,de,he,ce,le,Le,me,-He*k,!1),W(ue,de,he,ce,le,Le,pe,He*Y,!0,He*k),W(ue,de,he,ce,le,Le,ve,He*k,!1),ge.length>0&&ve.length>0){var De=ge[0].index,Ge=ke,je=ve[ve.length-1].index,ze=ue.length-1;le.push(je,ze,Ge,Ge,De,je)}ee=ue.length;var qe,Je=t.BoundingSphere.fromPoints(ue);r.defined(p)&&(qe=u.OrientedBoundingBox.fromRectangle(p,q,J,m));for(var Ke=new n.EllipsoidalOccluder(m).computeHorizonCullingPointPossiblyUnderEllipsoid(g,ue,q),Qe=new e.AxisAlignedBoundingBox(K,Q,g),Xe=new n.TerrainEncoding(Qe,Le.hMin,J,G,!1,f),Ze=new Float32Array(ee*Xe.getStride()),$e=0,et=0;et<ee;++et)$e=Xe.encode(Ze,$e,ue[et],he[et],de[et],void 0,ce[et]);var tt=ge.map((function(e){return e.index})).reverse(),it=me.map((function(e){return e.index})).reverse(),at=pe.map((function(e){return e.index})).reverse(),rt=ve.map((function(e){return e.index})).reverse();return it.unshift(at[at.length-1]),it.push(tt[0]),rt.unshift(tt[tt.length-1]),rt.push(at[0]),{vertices:Ze,indices:new Uint16Array(le),maximumHeight:J,minimumHeight:q,encoding:Xe,boundingSphere3D:Je,orientedBoundingBox:qe,occludeePointInScaledSpace:Ke,vertexCountWithoutSkirts:ke,indexCountWithoutSkirts:Ve,westIndicesSouthToNorth:tt,southIndicesEastToWest:it,eastIndicesNorthToSouth:at,northIndicesWestToEast:rt}}(l.buffer,l.relativeToCenter,l.ellipsoid,l.rectangle,l.nativeRectangle,l.exaggeration,l.skirtHeight,l.includeWebMercatorT,l.negativeAltitudeExponentBias,l.negativeElevationThreshold),p=m.vertices;g.push(p.buffer);var v=m.indices;return g.push(v.buffer),{vertices:p.buffer,indices:v.buffer,numberOfAttributes:m.encoding.getStride(),minimumHeight:m.minimumHeight,maximumHeight:m.maximumHeight,boundingSphere3D:m.boundingSphere3D,orientedBoundingBox:m.orientedBoundingBox,occludeePointInScaledSpace:m.occludeePointInScaledSpace,encoding:m.encoding,vertexCountWithoutSkirts:m.vertexCountWithoutSkirts,indexCountWithoutSkirts:m.indexCountWithoutSkirts,westIndicesSouthToNorth:m.westIndicesSouthToNorth,southIndicesEastToWest:m.southIndicesEastToWest,eastIndicesNorthToSouth:m.eastIndicesNorthToSouth,northIndicesWestToEast:m.northIndicesWestToEast}}))}));
