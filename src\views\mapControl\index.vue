<template>
  <LayoutBox :parts="parts"></LayoutBox>
  <MapDialog name="type1" :mount-map="false">
    <div class="type1-dialog">
      <div>全屏弹窗</div>
      <div>{{ dialogStore.dialogContent.title }}</div>
    </div>
  </MapDialog>
  <MapDialog name="type2">
    <div class="type2-dialog">
      <div>地图弹窗</div>
      <div>{{ dialogStore.dialogContent.title }}</div>
    </div>
  </MapDialog>
  <MapDialog name="type3">
    <div class="type3-dialog">
      <div>地图弹窗</div>
      <div>{{ dialogStore.dialogContent.title }}</div>
    </div>
  </MapDialog>
</template>
<script setup>
import { onMounted } from "vue";
import { useMapDialogStore } from "../../stores/modules/mapDialog";
import TypeChange from './TypeChange.vue'
import MarkControl from './MarkControl.vue'
import PlaceChange from './PlaceChange.vue'
import MapDialog from "../../components/common/MapDialog.vue"
const parts = [
  {
    component: TypeChange,
    positionStyle: {
      top: "100px",
      right: "600px"
    },
  },
  {
    component: MarkControl,
    positionStyle: {
      top: "100px",
      left: "600px"
    },
  },
  {
    component: PlaceChange,
    positionStyle: {
      top: "150px",
      right: "600px"
    },
  }
]

const dialogStore = useMapDialogStore();


</script>
<style scoped>
.type1-dialog {
  width: 500px;
  height: 500px;
  background-color: rgb(0, 170, 255);
}

.type2-dialog {
  width: 100px;
  height: 100px;
  background-color: rgb(0, 170, 255);
}

.type3-dialog {
  width: 200px;
  height: 200px;
  background-color: rgb(0, 170, 255);
}
</style>