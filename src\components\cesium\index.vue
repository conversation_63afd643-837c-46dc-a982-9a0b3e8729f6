<template>
  <div class="box">
    <div ref="mapDom" class="map-box">
    </div>
    <div ref="dialogDom" :style="dialogDomStyle" id="cesium-overlay-container"></div>
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, ref, watch, computed } from "vue";
import { onBeforeRouteLeave } from 'vue-router'
// import { Viewer, ImageryLayer, WebMapTileServiceImageryProvider, GeographicTilingScheme, Math as CesiumMath, Cartesian3 } from "cesium";
// import "cesium/Build/Cesium/Widgets/widgets.css";
import { MAP_CONFIG } from "../../config"
import { useMapStore } from "../../stores/modules/map";
import { useMapDialogStore } from "../../stores/modules/mapDialog"
import { useSystemStore } from "../../stores/system";
import { useGeoJsonStore } from "../../stores/modules/geoJson";
import { ImageStyle, TextStyle, PolygonStyle, WallStyle, CircleStyle, GeoJsonStyle } from "./style"
import { getGifBillboard, unbindGif, destroyCache, isGif } from "../../utils/gifRender"
import { request } from "../../api/request";
import { usePageScale } from '../../composables/pageScale'

const mapStore = useMapStore();
const dialogStore = useMapDialogStore();
const systemStore = useSystemStore();
const geoJsonStore = useGeoJsonStore();


let isLeave = false

const mapDom = ref(null)
const dialogDom = ref(null)

let mapViewer

const layersMap = []
//俯视角参数
const lookDownOrientation = {
  heading: Cesium.Math.toRadians(0),
  pitch: Cesium.Math.toRadians(-90),
  roll: 0.0
}

function initMap() {
  if (Cesium.SuperMapVersion) {
    Cesium.Ellipsoid.WGS84 = new Cesium.Ellipsoid(6378137.0, 6378137.0, 6356752.3142451793)
  }
  mapViewer = new Cesium.Viewer(mapDom.value, {
    infoBox: false,
    baseLayerPicker: false, // 如果设置为false，将不会创建右上角图层按钮。
    fullscreenButton: false, // 如果设置为false，将不会创建右下角全屏按钮。
    vrButton: false, // 如果设置为false，将不会创建VR应用场景
    geocoder: false, // 如果设置为false，将不会创建右上角查询(放大镜)按钮。
    homeButton: false, // 如果设置为false，将不会创建右上角主页(房子)按钮。
    infoBox: false, // 是否显示点击要素之后显示的信息,cesium中的沙盒开关
    sceneModePicker: false, // 如果设置为false，将不会创建右上角投影方式控件(显示二三维切换按钮)。
    selectionIndicator: false, // 获取当选定实体更改时引发的事件。
    navigationHelpButton: false, // 如果设置为false，则不会创建右上角帮助(问号)按钮。
    navigationInstructionsInitiallyVisible: false, // 如果帮助说明最初应该是可见的，则为true；如果直到用户明确单击该按钮，则不显示该说明，否则为false。
    timeline: false, // 如果设置为false，则不会创建正下方时间轴小部件。
    scene3DOnly: true, // 为 true 时，每个几何实例将仅以3D渲染以节省GPU内存。
    animation: false, // 如果设置为false，将不会创建左下角动画小部件。
    skyBox: false,//设置星空和云层
    // skyAtmosphere: false,//大气散射效果
    shouldAnimate: false, // 默认true ，是否显示动画。此选项优先于设置 Viewer＃clockViewModel 。
    // ps. Viewer＃clockViewModel 是用于控制当前时间的时钟视图模型。我们这里用不到时钟，就把shouldAnimate设为false
    // sceneMode: SceneMode.SCENE3D, // 场景模式 默认3D
    requestRenderMode: false, // 如果为true，则仅在场景内发生更改时才会渲染一帧。
    baseLayer: false,//地球的最底层图像图层。如果设置为false，则不会添加任何图像提供程序。
    imageryProvider: false,
    navigation: false,//超图导航罗盘
    contextOptions: { requestWebgl1: true }//使用webgl1
  });
  mapViewer.resolutionScale = window.devicePixelRatio;
  // window.mapViewer = mapViewer
  mapViewer.cesiumWidget.creditContainer.remove()
  if (MAP_CONFIG.provider === 'TDT') {
    initTDTMap()
  } else if (MAP_CONFIG.provider === 'SDTDT') {
    initSDTDTMap()
  }
  initCamera()
  initPickListener()
  initMoveListener()
}
//初始化点击事件监听
function initPickListener() {
  let handler = new Cesium.ScreenSpaceEventHandler(mapViewer.scene.canvas);
  handler.setInputAction((click) => {
    dialogStore.close()
    const pickedObject = mapViewer.scene.pick(click.position);
    if (Cesium.defined(pickedObject) && pickedObject.id instanceof Cesium.Entity) {
      const pickedEntity = pickedObject.id;
      if (pickedEntity?.entityCollection?.owner?.markType) {
        const dialogData = {
          id: pickedEntity.id,
          name: pickedEntity.entityCollection.owner.markType,
          position: pickedEntity.position.getValue(),
        }
        dialogStore.openRequest(dialogData, 'cesium')
      }
    } else if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive) && pickedObject.primitive.type === "integration") {
      console.log('map click integration')
      console.log(pickedObject.id)
    } else if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive) && pickedObject.primitive.type === "regionPolygon") {
      console.log('map click regionPolygon')
      console.log(pickedObject)
      geoJsonStore.goRegion(pickedObject.id)
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}
//初始化鼠标移动事件监听
function initMoveListener() {
  //单体化对象
  let currentObjectId
  let currentPrimitive
  //区域块对象
  let regionObjectId
  let regionPrimitive

  let handler = new Cesium.ScreenSpaceEventHandler(mapViewer.scene.canvas);
  handler.setInputAction((movement) => {
    const pickedObject = mapViewer.scene.pick(movement.endPosition);
    //单体化效果处理
    if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive) && pickedObject.primitive.type === "integration") {
      if (pickedObject.id === currentObjectId) {
        return
      }
      if (currentObjectId && currentPrimitive) {
        const attributes = currentPrimitive.getGeometryInstanceAttributes(
          currentObjectId
        );
        attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
          Cesium.Color.fromCssColorString("#ffffff").withAlpha(0)
        )
      }
      currentObjectId = pickedObject.id;
      currentPrimitive = pickedObject.primitive;
      if (currentObjectId && currentPrimitive) {
        const attributes = currentPrimitive.getGeometryInstanceAttributes(
          currentObjectId
        );
        attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
          Cesium.Color.fromCssColorString("#007cdc").withAlpha(0.7)
        )
      }
    } else if (currentObjectId && currentPrimitive) {
      const attributes = currentPrimitive.getGeometryInstanceAttributes(
        currentObjectId
      );
      attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
        Cesium.Color.fromCssColorString("#ffffff").withAlpha(0)
      )
      currentObjectId = null
      currentPrimitive = null
    }
    //区域块效果处理
    // if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.primitive) && pickedObject.primitive.type === "regionPolygon") {
    //   if (pickedObject.id === regionObjectId) {
    //     return
    //   }
    //   if (regionObjectId && regionPrimitive) {
    //     const attributes = regionPrimitive.getGeometryInstanceAttributes(
    //       regionObjectId
    //     );
    //     if (attributes) {
    //       attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
    //         Cesium.Color.fromCssColorString('#fbf0d2')
    //       )
    //     }
    //   }
    //   regionObjectId = pickedObject.id;
    //   regionPrimitive = pickedObject.primitive;
    //   if (regionObjectId && regionPrimitive) {
    //     const attributes = regionPrimitive.getGeometryInstanceAttributes(
    //       regionObjectId
    //     );
    //     if (attributes) {
    //       attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
    //         Cesium.Color.fromCssColorString('#48ff00')
    //       )
    //     }
    //   }
    // } else if (regionObjectId && regionPrimitive) {
    //   const attributes = regionPrimitive.getGeometryInstanceAttributes(
    //     regionObjectId
    //   );
    //   if (attributes) {
    //     attributes.color = Cesium.ColorGeometryInstanceAttribute.toValue(
    //       Cesium.Color.fromCssColorString('#fbf0d2')
    //     )
    //   }
    //   regionObjectId = null
    //   regionPrimitive = null
    // }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}
//初始化天地图
function initTDTMap() {
  addTDTLayers(mapStore.mapType)
}
//创建天地图
function addTDTLayers(type) {
  if (type === 'vector') {
    const [layerA, layerB] = createTDTVector()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('vector')
    layersMap.push('vector')
  } else if (type === 'raster') {
    const [layerA, layerB] = createTDTRaster()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('raster')
    layersMap.push('raster')
  }
}
//天地图影像地图
function createTDTRaster() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const layerAParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/img_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'img',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  const layerBParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cia_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'cia',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}
//天地图电子地图
function createTDTVector() {
  const projection = MAP_CONFIG.projection
  const urlMark = projection === 'EPSG:4326' ? 'c' : 'w'
  const layerAParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/vec_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'vec',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  const layerBParams = {
    url: `https://t${Math.floor(Math.random() * 8)}.tianditu.gov.cn/cva_${urlMark}/wmts?tk=${systemStore.mapKey}`,
    layer: 'cva',
    tileMatrixSetID: urlMark,
    format: 'tiles',
    style: 'default',
    maximumLevel: 18,
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}
//初始化天地图
function initSDTDTMap() {
  addSDTDTLayers(mapStore.mapType)
  limitCameraDistance(1116387)
}
//创建山东天地图
function addSDTDTLayers(type) {
  if (type === 'vector') {
    const layer = createSDTDTVector()
    mapViewer.imageryLayers.add(layer)
    layersMap.push('vector')
  } else if (type === 'raster') {
    const [layerA, layerB] = createSDTDTRaster()
    mapViewer.imageryLayers.add(layerA)
    mapViewer.imageryLayers.add(layerB)
    layersMap.push('raster')
    layersMap.push('raster')
  }
}
//山东天地图影像地图
function createSDTDTRaster() {
  const projection = 'EPSG:4326'
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmap?tk=${systemStore.mapKey}`,
    layer: 'SDRasterPubMap',
    tileMatrixSetID: 'raster',
    format: 'image/jpeg',
    style: 'default',
    maximumLevel: 18
  }
  const layerBParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdrasterpubmapdj?tk=${systemStore.mapKey}`,
    layer: 'SDRasterPubMapDJ',
    tileMatrixSetID: 'rasterdj',
    format: 'image/png',
    style: 'default',
    maximumLevel: 18
  }
  return [createWMTSLayer(layerAParams, projection), createWMTSLayer(layerBParams, projection)]
}
//山东天地图电子地图
function createSDTDTVector() {
  const projection = 'EPSG:4326'
  const layerAParams = {
    url: `${document.location.protocol}//service.sdmap.gov.cn/tileservice/sdpubmap?tk=${systemStore.mapKey}`,
    layer: 'SDPubMap',
    tileMatrixSetID: 'vector',
    format: 'image/png',
    style: 'default',
    maximumLevel: 18
  }
  return createWMTSLayer(layerAParams, projection)
}
//创建wmts图层
function createWMTSLayer(baseParams, projection) {
  const tileMatrixLabels = [];
  const startZ = projection === 'EPSG:4326' ? 1 : 0
  for (let z = startZ; z <= baseParams.maximumLevel; z++) {
    tileMatrixLabels.push(z);
  }
  const params = {
    ...baseParams,
    tileMatrixLabels
  }
  if (projection === 'EPSG:4326') {
    params.tilingScheme = new Cesium.GeographicTilingScheme()
    params.maximumLevel = params.maximumLevel - 1
  }
  const provider = new Cesium.WebMapTileServiceImageryProvider(params)
  return new Cesium.ImageryLayer(provider)
}
//更新区域地块显示
function updateRegion(features) {
  removePrimitiveByTypes(['regionPolygon', 'regionPolyline', 'regionLabels'])
  const polygonInstances = []
  const PolylineInstances = []
  const labels = new Cesium.LabelCollection()
  if (!features || !features.length) {
    return
  }
  for (let index = 0; index < features.length; index++) {
    try {
      for (let cIndex = 0; cIndex < features[index].geometry.coordinates.length; cIndex++) {
        const positionsArr = features[index].geometry.coordinates[cIndex][0].toString().split(',');
        const id = `${features[index].properties.grid_id}-${cIndex}`
        polygonInstances.push(createPolygonInstances(id, positionsArr, '#fbf0d2'))
        PolylineInstances.push(createPolylineInstances(id, positionsArr, 1, '#007bff'))
        labels.add(createRegionLabel(features[index].properties.lon, features[index].properties.lat, features[index].properties.abbreviation))
      }
    } catch (error) {
      console.log(error)
    }
  }

  if (polygonInstances.length) {
    const primitive = new Cesium.GroundPrimitive({
      geometryInstances: polygonInstances,
      appearance: new Cesium.PerInstanceColorAppearance({
        flat: true, // 为每个instance着色
        translucent: true,
        closed: false,
      }),
    })
    primitive.type = 'regionPolygon'
    mapViewer.scene.primitives.add(primitive);
  }
  if (PolylineInstances.length) {
    const primitive = new Cesium.GroundPolylinePrimitive({
      geometryInstances: PolylineInstances,
      appearance: new Cesium.PolylineColorAppearance({
        translucent: true
      })
    })
    primitive.type = 'regionPolyline'
    mapViewer.scene.primitives.add(primitive);
  }
  if (labels.length) {
    labels.type = 'regionLabels'
    mapViewer.scene.primitives.add(labels);
  }
}
//更新区域范围显示
function updateRange(features) {
  removePrimitiveByTypes(['rangePolyline'])
  const PolylineInstances = []
  if (!features || !features.length) {
    return
  }
  for (let index = 0; index < features.length; index++) {
    try {
      for (let cIndex = 0; cIndex < features[index].geometry.coordinates.length; cIndex++) {
        const positionsArr = features[index].geometry.coordinates[cIndex][0].toString().split(',');
        PolylineInstances.push(createPolylineInstances(features[index].id, positionsArr, 5, '#f95050'))
      }
    } catch (error) {
      console.log(error)
    }
  }
  if (PolylineInstances.length) {
    const primitive = new Cesium.GroundPolylinePrimitive({
      geometryInstances: PolylineInstances,
      appearance: new Cesium.PolylineColorAppearance({
        translucent: true
      })
    })
    primitive.type = 'rangePolyline'
    mapViewer.scene.primitives.add(primitive);
  }
}
//创建多边形实体
function createPolygonInstances(id, positionsArr, color) {
  const geometry = new Cesium.PolygonGeometry({
    polygonHierarchy: new Cesium.PolygonHierarchy(
      Cesium.Cartesian3.fromDegreesArray(positionsArr)
    ),
    vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
  });

  return new Cesium.GeometryInstance({
    id,
    geometry,
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString(color)),
    }
  })
}
//创建折线实体
function createPolylineInstances(id, positionsArr, width, color) {
  const geometry = new Cesium.GroundPolylineGeometry({
    positions: Cesium.Cartesian3.fromDegreesArray(positionsArr),
    width: 4.0
  });
  return new Cesium.GeometryInstance({
    id,
    geometry,
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString(color)),
    },
  });
}
//创建标签实体
function createRegionLabel(lon, lat, text, height = 3) {
  return {
    position: Cesium.Cartesian3.fromDegrees(lon, lat, height),
    text,
    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
      0,
      300000
    ),
    ...TextStyle.default
  }
}
//按type字段删除指定primitive
function removePrimitiveByTypes(types) {
  if (!types || !types.length) {
    return
  }
  const primitives = mapViewer.scene.primitives;
  const removePrimitives = []
  for (let i = 0; i < primitives.length; i++) {
    const primitive = primitives.get(i);
    if (types.includes(primitive.type)) {
      removePrimitives.push(primitive)
    }
  }
  removePrimitives.forEach(p => {
    mapViewer.scene.primitives.remove(p)
  })
}
//更新内容层状态
async function updateDataSource(entitiesList, nameKey, rewrite) {
  const dataSourcesMap = {}
  const existDataSources = mapViewer.dataSources.getByName(nameKey)
  for (const dataSource of existDataSources) {
    if (dataSource[nameKey]) {
      dataSourcesMap[dataSource[nameKey]] = dataSource
    }
  }
  for (const entities of entitiesList) {
    const entitiesTypeValue = entities[nameKey]
    if (!entitiesTypeValue || !entities.datas) {
      continue
    }
    if (entities.visible) {
      if (dataSourcesMap[entitiesTypeValue]) {
        updateDataSourceVisible(dataSourcesMap[entitiesTypeValue], true)
        if (rewrite) {
          await rewriteContent(entities, dataSourcesMap[entitiesTypeValue], nameKey)
        } else {
          await updateContent(entities, dataSourcesMap[entitiesTypeValue], nameKey)
        }
      } else {
        await createDataSource(entities, nameKey)
      }
    } else {
      if (dataSourcesMap[entitiesTypeValue]) {
        updateDataSourceVisible(dataSourcesMap[entitiesTypeValue], false)
      }
    }
  }
}
//创建新的datasoure
async function createDataSource(entities, nameKey) {
  const dataSource = new Cesium.CustomDataSource(nameKey);
  dataSource[nameKey] = entities[nameKey]
  for (const entityData of entities.datas) {
    const newEntity = await createEntity(entities, entityData, nameKey)
    dataSource.entities.add(newEntity)
  }
  dataSource.entities.collectionChanged.addEventListener((collection, added, removed, changed) => {
    if (removed && removed.length) {
      for (const item of removed) {
        onRemoveEntity(item)
      }
    }
  })
  mapViewer.dataSources.add(dataSource);
}
//删除entity回调
function onRemoveEntity(entity) {
  const image = entity.properties && entity.properties.image && entity.properties.image.getValue()
  if (isGif(image)) {
    unbindGif(image)
  }
}
//重写内容
async function rewriteContent(entities, dataSource, nameKey) {
  dataSource.entities.removeAll()
  for (const entityData of entities.datas) {
    const newEntity = await createEntity(entities, entityData, nameKey)
    dataSource.entities.add(newEntity)
  }
}
//更新内容
async function updateContent(entities, dataSource, nameKey) {
  for (const entityData of entities.datas) {
    const entityID = getEntityID(entityData)
    const entity = dataSource.entities.getById(entityID)
    //状态不同时，删除原有材质，新建材质
    if (entity && entity.properties && entity.properties.status && entity.properties.status.getValue() !== entityData.status) {
      dataSource.entities.remove(entity)
      const newEntity = await createEntity(entities, entityData, nameKey)
      dataSource.entities.add(newEntity)
    }
  }
}
//创建Entity
async function createEntity(entities, entityData, nameKey) {
  const styleType = entityData.styleType || entities.styleType || "default"
  const height = entityData.height || entities.height || 30
  const isWall = entityData.wall || entities.wall
  const options = {
    id: getEntityID(entityData),
    position: getPosition(entityData, height),
    properties: {
      status: entityData.status,
      image: nameKey === 'markType' && entityData.image
    }
  }
  if (nameKey === 'markType') {
    //地图标识
    options.billboard = await createBillboard(entityData, styleType)
    options.label = createLabel(entityData, styleType)
    options.ellipse = createEllipse(entityData, styleType)
  } else if (nameKey === 'polygonType') {
    //范围框定
    if (isWall) {
      options.wall = createWall(entityData, styleType)
    } else {
      options.polygon = createPolygon(entityData, styleType)
    }
    options.label = createLabel(entityData, styleType)
  }
  return new Cesium.Entity(options)
}
//创建标牌
async function createBillboard(entityData, styleType) {
  const { image } = entityData
  if (!image) {
    return
  }
  const styleParams = ImageStyle[styleType] || ImageStyle.default
  if (isGif(image)) {
    const gifBillboard = await getGifBillboard(image, styleParams, mapViewer)
    return gifBillboard
  }
  return new Cesium.BillboardGraphics({
    image,
    // scaleByDistance: new Cesium.NearFarScalar(100, 1.0, 1000, 0),
    ...styleParams
  })
}
//创建标签
function createLabel(entityData, styleType) {
  const { text } = entityData
  if (!text) {
    return
  }
  const styleParams = TextStyle[styleType] || TextStyle.default
  return new Cesium.LabelGraphics({
    text,
    // scaleByDistance: new Cesium.NearFarScalar(100, 1.0, 1000, 0),
    // pixelOffsetScaleByDistance: new Cesium.NearFarScalar(100, 1.0, 1000, 0),
    ...styleParams
  })
}
//创建圆形
function createEllipse(entityData, styleType) {
  const { radius } = entityData
  if (!radius) {
    return
  }
  const styleParams = CircleStyle[styleType] || CircleStyle.default
  return new Cesium.EllipseGraphics({
    ...styleParams,
    semiMajorAxis: radius,
    semiMinorAxis: radius
  })
}
//创建多边形
function createPolygon(entityData, styleType) {
  const { coordinates } = entityData
  const styleParams = PolygonStyle[styleType] || PolygonStyle.default
  return new Cesium.PolygonGraphics({
    hierarchy: getPositions(coordinates),
    ...styleParams
  })
}
//创建围墙
function createWall(entityData, styleType) {
  const { coordinates } = entityData
  const styleParams = WallStyle[styleType] || WallStyle.default
  const positions = getWallPositions(coordinates)
  return new Cesium.WallGraphics({
    positions,
    ...styleParams,
    minimumHeights: new Array(positions.length).fill(styleParams.minimumHeights),
    maximumHeights: new Array(positions.length).fill(styleParams.maximumHeights)
  })
}
//获取墙范围坐标
function getWallPositions(coordinates, height = 0) {
  try {
    if (coordinates[0][0] === coordinates[coordinates.length - 1][0] && coordinates[0][1] === coordinates[coordinates.length - 1][1]) {
      return getPositions(coordinates, height)
    } else {
      return getPositions([...coordinates, coordinates[0]], height)
    }
  } catch (error) {
    console.log(error)
  }
}
//获取范围坐标
function getPositions(coordinates, height = 0) {
  if (coordinates && coordinates.length) {
    return coordinates.map((coordinate) => {
      return Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1], height)
    })
  }
}
//datasource显隐
function updateDataSourceVisible(dataSource, visible) {
  dataSource.show = visible
}
//获取位置
function getPosition(entityData, height) {
  if (entityData.coordinate && entityData.coordinate.length > 1) {
    return Cesium.Cartesian3.fromDegrees(entityData.coordinate[0], entityData.coordinate[1], height)
  }
  if (entityData.coordinates && entityData.coordinates.length > 1) {
    const polyPositions = getPositions(entityData.coordinates, height)
    return Cesium.BoundingSphere.fromPoints(polyPositions).center;
  }
}
//实体ID
function getEntityID(item) {
  return item.id || item.ID || item.Id
}
//更新上层建筑
function updateBuildingData(buildingDatas = []) {
  const buildingGeoJson = []
  const building3DTiles = []
  const buildingS3MTiles = []
  for (const building of buildingDatas) {
    if (building.type === 'geojson') {
      buildingGeoJson.push(building)
    } else if (building.type === 'S3MTiles') {
      buildingS3MTiles.push(building)
    } else if (building.type === '3DTiles') {
      building3DTiles.push(building)
    }
  }
  updateGeoJson(buildingGeoJson)
  update3DTiles(building3DTiles)
  updateS3MTiles(buildingS3MTiles)
}
//更新geojson数据
function updateGeoJson(buildingDatas) {
  const existDataSources = mapViewer.dataSources.getByName("building")
  for (const dataSource of existDataSources) {
    const index = buildingDatas.findIndex((d) => d.id === dataSource.id)
    if (index > -1) {
      buildingDatas.splice(index, 1)
    } else {
      mapViewer.dataSources.remove(dataSource, true)
    }
  }
  for (const buildingData of buildingDatas) {
    createGeoJson(buildingData)
  }
}
//更新3DTiles数据
function update3DTiles(buildingDatas) {
  const primitives = mapViewer.scene.primitives;
  for (let i = 0; i < primitives.length; ++i) {
    const primitive = primitives.get(i);
    if (!primitive.id || primitive.type !== '3DTiles') {
      continue
    }
    const index = buildingDatas.findIndex((d) => d.id === primitive.id)
    if (index > -1) {
      buildingDatas.splice(index, 1)
    } else {
      mapViewer.scene.primitives.remove(primitive)
    }
  }
  for (const buildingData of buildingDatas) {
    create3DTiles(buildingData)
  }
}
//更新S3MTiles数据
function updateS3MTiles(buildingDatas) {
  if (!Cesium.SuperMapVersion) {
    return
  }
  const layers = mapViewer.scene.layers;
  for (let i = 0; i < layers.length; ++i) {
    const layer = layers.get(i);
    if (layer.type !== 'S3MTiles') {
      continue
    }
    const index = buildingDatas.findIndex((d) => d.id === layer.name)
    if (index > -1) {
      buildingDatas.splice(index, 1)
    } else {
      mapViewer.scene.layers.remove(layer, true)
    }
  }
  for (const buildingData of buildingDatas) {
    createSuperMap3DTiles(buildingData)
  }
}
//加载GeoJson
async function createGeoJson(params) {
  try {
    const { id, url, styleType, options, displayDistance } = params
    const loadOptions = {
      ...GeoJsonStyle[styleType] || GeoJsonStyle.default,
      ...options,
    }
    const dataSource = await Cesium.GeoJsonDataSource.load(url, loadOptions)
    dataSource.name = "building"
    dataSource.id = id
    const entities = dataSource.entities.values;
    for (let index = 0; index < entities.length; index++) {
      const entity = entities[index];
      //高度根据properties中height设置
      entity.polygon.extrudedHeight = entity.properties.height
      //设置可视范围，显示过多会崩溃！
      entity.polygon.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(10.0, displayDistance || 3000.0);
    }
    mapViewer.dataSources.add(dataSource);
  } catch (error) {
    console.log(error)
  }
}
//使用Primitive加载GeoJson
async function createPrimitiveGeoJson(params) {
  const { id, url, styleType, options, displayDistance } = params
  request.get(url).then((data) => {
    const features = data.features;
    const primitive = getGeoJsonPrimitive(features)
    if (primitive) {
      mapViewer.scene.primitives.add(primitive);
    }
  }).catch((error) => {
    console.log(error)
  })
}
function getGeoJsonPrimitive(features) {
  if (!features || !features.length) {
    return
  }
  const instances = [];
  for (let index = 0; index < features.length; index++) {
    try {
      for (let cIndex = 0; cIndex < features[index].geometry.coordinates.length; cIndex++) {
        const polygonArr = features[index].geometry.coordinates[cIndex].toString().split(',');
        const height = features[index]?.properties?.height
        const polygon = new Cesium.PolygonGeometry({
          polygonHierarchy: new Cesium.PolygonHierarchy(
            Cesium.Cartesian3.fromDegreesArray(polygonArr)
          ),
          extrudedHeight: height,
          vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT
        });
        const geometry = Cesium.PolygonGeometry.createGeometry(polygon);
        instances.push(new Cesium.GeometryInstance({
          geometry,
          attributes: {
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString("#ffffff")),
          },
        }));
      }
    } catch (error) {
      console.log(error)
    }
  }
  if (instances.length) {
    const primitive = new Cesium.Primitive({
      geometryInstances: instances,
      appearance: new Cesium.PerInstanceColorAppearance({ // 为每个instance着色
        translucent: false,
        closed: false,
      }),
    })
    return primitive
  }
}
//加载3DTile
async function create3DTiles(params) {
  const { id, url, heightCorrect, options, customShader, shaderColor } = params
  //通用优化
  const commonOptimization = {
    maximumScreenSpaceError: 16,//最大屏幕空间误差，用于控制模型的细节程度和渲染效果。数值越小，模型细节越高，但也会导致性能下降。
    maximumMemoryUsage: 512,//单位MB，最大内存使用量，用于控制模型数据在内存中的占用量。如果模型数据过大，可以适当调低该值来减少内存占用。
    preloadWhenHidden: false,//是否在模型隐藏时预加载子节点数据。如果启用，可以提高模型显示时的加载速度，但会增加资源消耗。默认值为false。
    preferLeaves: false, //是否优先加载叶子节点。如果启用，Cesium会优先加载叶子节点，从而提高模型显示的速度。默认值为false。
  }
  //跳过细节层次优化
  const skipLevelOfDetailOptimization = {
    skipLevelOfDetail: true,//设置为true后，Cesium会在渲染时跳过某些细节级别，从而提高性能。
    baseScreenSpaceError: 1024,//表示相对于屏幕像素的误差。当Cesium计算出相机距离每个细节级别的距离时，将用此值乘以相机的高度以确定跳过哪些细节级别。较高的数字表示跳过更多的细节级别，但可能会影响地形和模型的外观质量。
    skipScreenSpaceErrorFactor: 16,//表示在计算屏幕空间误差时要跳过的因子。较高的数字表示跳过更多的细节级别，但可能会影响地形和模型的外观质量。
    skipLevels: 1,//表示要跳过的细节级别的数量。默认情况下为0，表示不跳过任何细节级别。将其设置为1或更高数字，则会跳过相应数量的细节级别。较高的数字表示跳过更多的细节级别，但可能会影响地形和模型的外观质量。
    immediatelyLoadDesiredLevelOfDetail: false,//设置为true，则会立即加载所需的细节级别，这可能会导致加载时间更长，但可以获得更高的清晰度。
    loadSiblings: false,//设置为true，则会加载当前视图瓦片的相邻瓦片，以提高渲染性能和地图质量。然而，相邻瓦片的加载也会增加数据传输和内存使用，需要谨慎使用。该属性通常用于需要在初始加载时获得更高清晰度和更流畅渲染的场景。
    cullWithChildrenBounds: true,//是否启用子节点剔除。如果启用，会根据子节点的包围盒来进行剔除，从而减少不必要的渲染开销。默认值为true。
  }
  const dynamicScreenSpaceErrorOptimization = {
    dynamicScreenSpaceError: true,//通过动态调整地图瓦片的细节级别，dynamicScreenSpaceError可以在保证地图质量的同时，提高地图渲染性能。
    dynamicScreenSpaceErrorDensity: 0.00278,//用于设置瓦片的屏幕空间误差的动态密度。这个属性的值越小，相机距离更远的瓦片的屏幕空间误差就越小，但这会增加渲染时间和内存使用量。
    dynamicScreenSpaceErrorFactor: 4.0,//这个属性的值越大，相机距离更远的瓦片的屏幕空间误差就越小，但这会增加渲染时间和内存使用量。相反，如果这个属性的值越小，相机距离更远的瓦片的屏幕空间误差就越大，但是渲染时间和内存使用量会减少。
    dynamicScreenSpaceErrorHeightFalloff: 0.25//这个属性的值越大，相机距离更远的瓦片的屏幕空间误差下降得越快，但这会增加渲染时间和内存使用量。相反，如果这个属性的值越小，相机距离更远的瓦片的屏幕空间误差下降得越慢，但是渲染时间和内存使用量会减少。
  }
  const completeOptions = {
    ...commonOptimization,
    ...skipLevelOfDetailOptimization,
    ...dynamicScreenSpaceErrorOptimization,
    ...options
  }
  //配置自定义shader
  if (customShader) {
    completeOptions.customShader = getBuildingCustomShader(shaderColor)
  }
  try {
    let tileset
    if (Cesium.SuperMapVersion) {
      //超图
      tileset = new Cesium.Cesium3DTileset({
        url, //数据路径
        ...completeOptions
      })
      tileset.readyPromise.then((tileset) => {
        if (heightCorrect) {
          tileset.modelMatrix = getTranslationMatrix(tileset, 0, 0, heightCorrect);
        }
      })
    } else {
      //cesium
      tileset = await Cesium.Cesium3DTileset.fromUrl(url, completeOptions);
      if (heightCorrect) {
        tileset.modelMatrix = getTranslationMatrix(tileset, 0, 0, heightCorrect);
      }
    }
    tileset.id = id
    tileset.type = '3DTiles'
    mapViewer.scene.primitives.add(tileset);

  } catch (error) {
    console.log(error)
  }
}
//计算偏移矩阵
function getTranslationMatrix(tileset, x, y, z) {
  const cartographic = Cesium.Cartographic.fromCartesian(tileset.boundingSphere.center);
  //初始向量
  const surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, cartographic.height);
  //计算当前位置对应的Matrix4
  const currentMatrix4 = Cesium.Transforms.eastNorthUpToFixedFrame(surface);
  //计算偏移矩阵
  const tempTranslation = new Cesium.Cartesian3(x, y, z);
  //新位置向量
  const offset = Cesium.Matrix4.multiplyByPoint(currentMatrix4, tempTranslation, new Cesium.Cartesian3(0, 0, 0));
  //得到平移向量
  const translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
  //根据平移向量生成平移矩阵
  return Cesium.Matrix4.fromTranslation(translation);
}
//白模3dtiles自定义shader
function getBuildingCustomShader(colorStr) {
  return new Cesium.CustomShader({
    uniforms: {
      u_color: {
        value: colorStr ? Cesium.Color.fromCssColorString(colorStr) : Cesium.Color.CHOCOLATE, // initial value
        type: Cesium.UniformType.VEC4
      }
    },
    fragmentShaderText: `
      void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
         vec4 position = czm_inverseModelView * vec4(fsInput.attributes.positionEC,1); // 位置
         float glowRange = 300.0; // 光环的移动范围(高度)
         vec4 temp = u_color; // 颜色
         temp *= vec4(vec3(position.z / 100.0), 1.0);  // 渐变
         // 动态光环
         float time = fract(czm_frameNumber / 360.0);
         time = abs(time - 0.5) * 2.0;
         float diff = step(0.005, abs( clamp(position.z / glowRange, 0.0, 1.0) - time));
         material.diffuse = vec3(temp.rgb + temp.rgb * (1.0 - diff)) ;
       }
      `
  });
}
//加载超图3DTile
function createSuperMap3DTiles(params) {
  if (!Cesium.SuperMapVersion) {
    return
  }
  const { id, url, heightCorrect, options } = params
  const promise = mapViewer.scene.addS3MTilesLayerByScp(url, { name: id });
  promise.then((layer) => {
    if (heightCorrect) {
      layer.style3D.bottomAltitude = heightCorrect
      layer.refresh()
    }
  });
}
//更新单体化数据
function updateIntegration(integrationDatas) {
  const primitives = mapViewer.scene.primitives;
  for (let i = 0; i < primitives.length; ++i) {
    const primitive = primitives.get(i);
    if (primitive.type === 'integration') {
      mapViewer.scene.primitives.remove(primitive)
    }
  }
  const instances = [];
  for (const integrationData of integrationDatas) {
    instances.push(...createIntegrationInstances(integrationData))
  }
  if (instances.length) {
    const primitive = new Cesium.ClassificationPrimitive({
      geometryInstances: instances,
      appearance: new Cesium.PerInstanceColorAppearance({ // 为每个instance着色
        translucent: false,
        closed: false,
      }),
      classificationType: Cesium.ClassificationType.CESIUM_3D_TILE
    })
    primitive.type = 'integration'
    mapViewer.scene.primitives.add(primitive);
  }
}
//创建单体化矩形primitive
function createIntegrationInstances(integrationData) {
  const { coordinates, floors } = integrationData
  // const polygonArr = coordinates.toString().split(',');
  const instances = []
  for (let index = 0; index < floors.length; index++) {
    const { id, dimensions, height, rotationZ, metadata } = floors[index];

    // 绘制多边形（数据结构与矩形不同）
    // const polygon = new Cesium.PolygonGeometry({
    //   polygonHierarchy: new Cesium.PolygonHierarchy(
    //     Cesium.Cartesian3.fromDegreesArray(polygonArr)
    //   ),
    //   height: floors[index].height,
    //   extrudedHeight: floors[index].extrudedHeight,
    //   vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT
    // });
    // const geometry = Cesium.PolygonGeometry.createGeometry(polygon);

    //绘制矩形
    const box = Cesium.BoxGeometry.fromDimensions({
      vertexFormat: Cesium.PerInstanceColorAppearance.VERTEX_FORMAT,
      dimensions: new Cesium.Cartesian3(...dimensions)
    });
    const geometry = Cesium.BoxGeometry.createGeometry(box);
    instances.push(new Cesium.GeometryInstance({
      id,
      geometry,
      modelMatrix: getPositionMatrix(coordinates, height, rotationZ),
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString("#ffffff").withAlpha(0)),
        ...metadata
      },
    }));
  }
  return instances
}
//获取位置矩阵
function getPositionMatrix(coordinates, height, rotationZ) {
  //位置矩阵
  const position = Cesium.Cartesian3.fromDegrees(coordinates[0], coordinates[1], height);
  const positionMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
  //旋转矩阵
  const rotation = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(rotationZ));
  const rotationMatrix = Cesium.Matrix4.fromRotationTranslation(rotation)
  //目标矩阵
  return Cesium.Matrix4.multiply(positionMatrix, rotationMatrix, positionMatrix)
}
//限制显示高度
function limitCameraDistance(max, min) {
  // 获取屏幕空间相机控制器
  const screenSpaceCameraController = mapViewer.scene.screenSpaceCameraController;
  // 设置最大距离(米)
  if (max) {
    screenSpaceCameraController.maximumZoomDistance = max;
  }
  // 设置最小距离(米)
  if (min) {
    screenSpaceCameraController.minimumZoomDistance = min;
  }
}
//按键监听
function onKeyEvent(e) {
  //ctrl+m
  if (e.ctrlKey && e.keyCode === 77) {
    consolePosition()
  }
}
//打印视角位置
function consolePosition() {
  const camera = mapViewer.camera; // 获取场景相机对象
  const viewOptions = {
    destination: camera.position,
    orientation: {
      heading: camera.heading,
      pitch: camera.pitch,
      roll: camera.roll
    }
  }
  const cartographic = cartesian2Cartographic(camera.position)
  console.info('视角信息：')
  console.info('viewOptions:')
  console.info(JSON.stringify(viewOptions))
  console.info('经纬度:')
  console.info(JSON.stringify(cartographic))
}
//笛卡尔转经纬度
function cartesian2Cartographic(cartesian) {
  try {
    const cartographic = mapViewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
    let longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
    let latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
    return { longitude, latitude, height: cartographic.height.toFixed(6) }
  } catch (error) {
    return {}
  }
}
//初始化视角
function initCamera() {
  mapViewer.camera.setView(getViewData(mapStore.mapView))
}
//获取视角数据
function getViewData(params) {
  const { destination, orientation = lookDownOrientation, center, height } = params
  if (destination) {
    return {
      destination,
      orientation
    }
  } else if (center && center.length) {
    return {
      destination: Cesium.Cartesian3.fromDegrees(center[0], center[1], height),
      orientation
    }
  }
}
//根据经纬度设置俯视角
function setLookDownCamera(Lng, Lat, height) {
  mapViewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(Lng, Lat, height),
    orientation: lookDownOrientation
  })
}
//layer显隐
function updateLayerVisible(layerIndex, visible) {
  const layer = mapViewer.imageryLayers.get(layerIndex)
  layer.show = visible
}
//控制弹窗显隐
function activateDialog(name) {
  if (name && dialogStore.dialogData.position) {
    mapViewer.scene.postRender.addEventListener(onScenePostRender)
  } else {
    mapViewer.scene.postRender.removeEventListener(onScenePostRender)
  }
}
//场景渲染回调,修正弹窗位置
function onScenePostRender() {
  if (dialogStore.dialogData && dialogStore.dialogData.position) {
    const canvasPosition = mapViewer.scene.cartesianToCanvasCoordinates(dialogStore.dialogData.position);
    dialogDom.value.style.top = `${canvasPosition.y}px`
    dialogDom.value.style.left = `${canvasPosition.x}px`
  }
}
//弹窗大屏适配
const { layout, scale, adaptScale } = usePageScale()
const dialogDomStyle = computed(() => {
  if (layout.value === 'adapt') {
    return {
      transformOrigin: 'top left',
      transform: `scale(${adaptScale.value})`,
    }
  }
  return {
    transformOrigin: 'top left',
    transform: `scale(${scale.value})`,
  }
})

//切换地图类型
watch(() => mapStore.mapType, (newType, oldType) => {
  if (isLeave) {
    return
  }
  console.log('watch mapType')
  let isLayerExist = false
  layersMap.forEach((layerType, index) => {
    if (layerType === newType) {
      updateLayerVisible(index, true)
      isLayerExist = true
    } else {
      updateLayerVisible(index, false)
    }
  })
  if (!isLayerExist) {
    if (MAP_CONFIG.provider === 'TDT') {
      addTDTLayers(newType)
    } else if (MAP_CONFIG.provider === 'SDTDT') {
      addSDTDTLayers(newType)
    }
  }
})
//切换地图视角
watch(() => mapStore.mapView, (newView, oldView) => {
  if (isLeave) {
    return
  }
  console.log('watch view')
  mapViewer.camera.flyTo(getViewData(newView))
})
//更新标识
watch(() => mapStore.markData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch markData')
  updateDataSource(newData, "markType", true)
})
//更新多边形
watch(() => mapStore.polygonData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch polygonData')
  updateDataSource(newData, "polygonType", true)
})
//更新建筑物
watch(() => mapStore.buildingData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch buildingData')
  updateBuildingData(newData)
})
//更新单体化
watch(() => mapStore.integrationData, (newData, oldData) => {
  if (isLeave) {
    return
  }
  console.log('watch integrationData')
  updateIntegration(newData)
})
//更新弹窗状态
watch(() => dialogStore.activateName, (newName, oldName) => {
  if (isLeave) {
    return
  }
  activateDialog(newName)
})
//更新区域块
watch(() => geoJsonStore.regionGeojson, (newData, oldData) => {
  if (isLeave) {
    return
  }
  updateRegion(newData)
})
//更新区域块范围
watch(() => geoJsonStore.rangeGeojson, (newData, oldData) => {
  if (isLeave) {
    return
  }
  updateRange(newData)
})

onMounted(() => {
  document.addEventListener('keydown', onKeyEvent)
  initMap()
  updateDataSource(mapStore.markData, "markType")
  updateDataSource(mapStore.polygonData, "polygonType")
  updateBuildingData(mapStore.buildingData)
  updateIntegration(mapStore.integrationData)
  // updateRegion(geoJsonStore.regionGeojson)
  // updateRange(geoJsonStore.rangeGeojson)
})

onBeforeRouteLeave(() => {
  isLeave = true
})
onBeforeUnmount(() => {
  mapViewer.destroy()
  destroyCache()
})
onUnmounted(() => {
  document.removeEventListener('keydown', onKeyEvent)
})
</script>
<style scoped>
.box {
  width: 100%;
  height: 100%;
}

.map-box {
  width: 100%;
  height: 100%;
}

#cesium-overlay-container {
  position: absolute;
}
</style>
