import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useEnhancedCesiumStore = defineStore('enhancedCesium', () => {
  // 场景漫游相关状态
  const tourState = ref({
    isActive: false,
    currentPath: [],
    currentPosition: null,
    speed: 1.0,
    showProfile: false
  })

  // 数据标签相关状态
  const labelState = ref({
    visible: true,
    labels: [],
    selectedLabel: null
  })

  // 分屏比较相关状态
  const splitScreenState = ref({
    enabled: false,
    leftScene: 'current',
    rightScene: 'plan1',
    splitPosition: 0.5
  })

  // 洪水淹没相关状态
  const floodState = ref({
    isActive: false,
    waterLevel: 0,
    maxLevel: 50,
    speed: 1.0,
    areas: []
  })

  // 地形剖面相关状态
  const profileState = ref({
    visible: false,
    data: [],
    selectedPath: null
  })

  // 工程数据
  const engineeringData = ref({
    waterProjects: [
      {
        id: 'wp001',
        name: '主坝工程',
        type: 'dam',
        position: [116.40, 39.91, 0],
        status: 'construction',
        progress: 75,
        info: {
          height: '120m',
          length: '800m',
          capacity: '5000万m³'
        }
      },
      {
        id: 'wp002',
        name: '泵站A',
        type: 'pump',
        position: [116.42, 39.93, 0],
        status: 'operational',
        progress: 100,
        info: {
          power: '500kW',
          flow: '100m³/s',
          efficiency: '85%'
        }
      },
      {
        id: 'wp003',
        name: '监测站B',
        type: 'monitor',
        position: [116.44, 39.95, 0],
        status: 'operational',
        progress: 100,
        info: {
          waterLevel: '2.5m',
          flow: '80m³/s',
          quality: '良好'
        }
      }
    ],
    pathRoutes: [
      {
        id: 'route001',
        name: '主要巡检路线',
        points: [
          [116.39, 39.90, 100],
          [116.40, 39.91, 120],
          [116.41, 39.92, 150],
          [116.42, 39.93, 180],
          [116.43, 39.94, 200],
          [116.44, 39.95, 160],
          [116.45, 39.96, 140]
        ],
        description: '沿主要水利设施的巡检路线'
      }
    ],
    floodScenarios: [
      {
        id: 'flood001',
        name: '百年一遇洪水',
        maxLevel: 30,
        duration: 120, // 分钟
        affectedArea: [
          [116.38, 39.89],
          [116.46, 39.89],
          [116.46, 39.97],
          [116.38, 39.97]
        ]
      }
    ]
  })

  // 计算属性
  const activeTourPath = computed(() => {
    return engineeringData.value.pathRoutes.find(route => 
      route.id === tourState.value.currentPath
    )
  })

  const visibleLabels = computed(() => {
    return labelState.value.visible ? labelState.value.labels : []
  })

  const floodProgress = computed(() => {
    if (!floodState.value.isActive) return 0
    return (floodState.value.waterLevel / floodState.value.maxLevel) * 100
  })

  // Actions
  function startTour(pathId) {
    const path = engineeringData.value.pathRoutes.find(r => r.id === pathId)
    if (path) {
      tourState.value.isActive = true
      tourState.value.currentPath = pathId
    }
  }

  function stopTour() {
    tourState.value.isActive = false
    tourState.value.currentPath = null
    tourState.value.currentPosition = null
  }

  function updateTourPosition(position) {
    tourState.value.currentPosition = position
  }

  function toggleLabels() {
    labelState.value.visible = !labelState.value.visible
  }

  function addLabel(labelData) {
    const newLabel = {
      id: `label_${Date.now()}`,
      ...labelData,
      createdAt: new Date()
    }
    labelState.value.labels.push(newLabel)
    return newLabel
  }

  function removeLabel(labelId) {
    const index = labelState.value.labels.findIndex(l => l.id === labelId)
    if (index > -1) {
      labelState.value.labels.splice(index, 1)
    }
  }

  function selectLabel(labelId) {
    labelState.value.selectedLabel = labelId
  }

  function enableSplitScreen(leftScene = 'current', rightScene = 'plan1') {
    splitScreenState.value.enabled = true
    splitScreenState.value.leftScene = leftScene
    splitScreenState.value.rightScene = rightScene
  }

  function disableSplitScreen() {
    splitScreenState.value.enabled = false
  }

  function updateSplitPosition(position) {
    splitScreenState.value.splitPosition = Math.max(0.1, Math.min(0.9, position))
  }

  function startFloodSimulation(scenarioId) {
    const scenario = engineeringData.value.floodScenarios.find(s => s.id === scenarioId)
    if (scenario) {
      floodState.value.isActive = true
      floodState.value.maxLevel = scenario.maxLevel
      floodState.value.waterLevel = 0
      floodState.value.areas = [scenario.affectedArea]
    }
  }

  function stopFloodSimulation() {
    floodState.value.isActive = false
    floodState.value.waterLevel = 0
    floodState.value.areas = []
  }

  function updateFloodLevel(level) {
    floodState.value.waterLevel = Math.max(0, Math.min(floodState.value.maxLevel, level))
  }

  function showProfile(pathId) {
    const path = engineeringData.value.pathRoutes.find(r => r.id === pathId)
    if (path) {
      profileState.value.visible = true
      profileState.value.selectedPath = pathId
      // 模拟生成剖面数据
      profileState.value.data = path.points.map((point, index) => ({
        distance: index * 1000,
        elevation: point[2] + Math.random() * 50,
        longitude: point[0],
        latitude: point[1]
      }))
    }
  }

  function hideProfile() {
    profileState.value.visible = false
    profileState.value.selectedPath = null
    profileState.value.data = []
  }

  function addWaterProject(projectData) {
    const newProject = {
      id: `wp_${Date.now()}`,
      ...projectData,
      createdAt: new Date()
    }
    engineeringData.value.waterProjects.push(newProject)
    return newProject
  }

  function updateProjectStatus(projectId, status, progress) {
    const project = engineeringData.value.waterProjects.find(p => p.id === projectId)
    if (project) {
      project.status = status
      if (progress !== undefined) {
        project.progress = progress
      }
    }
  }

  function getProjectsByType(type) {
    return engineeringData.value.waterProjects.filter(p => p.type === type)
  }

  function getProjectsInArea(bounds) {
    // bounds: { minLng, maxLng, minLat, maxLat }
    return engineeringData.value.waterProjects.filter(project => {
      const [lng, lat] = project.position
      return lng >= bounds.minLng && lng <= bounds.maxLng &&
             lat >= bounds.minLat && lat <= bounds.maxLat
    })
  }

  // 重置所有状态
  function resetAll() {
    stopTour()
    disableSplitScreen()
    stopFloodSimulation()
    hideProfile()
    labelState.value.labels = []
    labelState.value.selectedLabel = null
  }

  return {
    // 状态
    tourState,
    labelState,
    splitScreenState,
    floodState,
    profileState,
    engineeringData,
    
    // 计算属性
    activeTourPath,
    visibleLabels,
    floodProgress,
    
    // 方法
    startTour,
    stopTour,
    updateTourPosition,
    toggleLabels,
    addLabel,
    removeLabel,
    selectLabel,
    enableSplitScreen,
    disableSplitScreen,
    updateSplitPosition,
    startFloodSimulation,
    stopFloodSimulation,
    updateFloodLevel,
    showProfile,
    hideProfile,
    addWaterProject,
    updateProjectStatus,
    getProjectsByType,
    getProjectsInArea,
    resetAll
  }
})
