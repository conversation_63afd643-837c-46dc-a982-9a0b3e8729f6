const defaultColor = Cesium.Color.RED;
const defaultSpeed = 3;

function DiffuseCircleMaterialProperty(options) {
  options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
}

Object.defineProperties(DiffuseCircleMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Cesium.Property.isConstant(this._color) && Cesium.Property.isConstant(this._speed);
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
  speed: Cesium.createPropertyDescriptor("speed"),
});

DiffuseCircleMaterialProperty.prototype.getType = function (time) {
  return "DiffuseCircle";
};

DiffuseCircleMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Cesium.Property.getValueOrClonedDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

DiffuseCircleMaterialProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof DiffuseCircleMaterialProperty && //
      Cesium.Property.equals(this._color, other._color) && //
      Cesium.Property.equals(this._speed, other._speed))
  );
};

Cesium.DiffuseCircleMaterialProperty = DiffuseCircleMaterialProperty;

const type = "DiffuseCircle";

const source = `
uniform vec4 color;
uniform float speed;

vec3 circlePing(float r, float innerTail,  float frontierBorder, float timeResetSeconds,  float radarPingSpeed,  float fadeDistance){
  float t = fract(czm_frameNumber * speed / 1000.0);
  float time = mod(t, timeResetSeconds) * radarPingSpeed;
  float circle;
  circle += smoothstep(time - innerTail, time, r) * smoothstep(time + frontierBorder,time, r);
  circle *= smoothstep(fadeDistance, 0.0, r);
  return vec3(circle);
}

czm_material czm_getMaterial(czm_materialInput materialInput){
  czm_material material = czm_getDefaultMaterial(materialInput);
  vec2 st = materialInput.st * 2.0  - 1.0 ;
  vec2 center = vec2(0.);
  float time = fract(czm_frameNumber * speed / 1000.0);
  vec3 flagColor;
  float r = length(st - center) / 4.;
  flagColor += circlePing(r, 0.25, 0.025, 4.0, 0.3, 1.0) * color.rgb;
  material.alpha = length(flagColor);
  material.diffuse = flagColor.rgb;
  return material;
}
`;

Cesium.Material._materialCache.addMaterial(type, {
  fabric: {
    type,
    uniforms: {
      color: defaultColor,
      speed: defaultSpeed,
    },
    source,
  },
  translucent: function (material) {
    return true;
  },
});
