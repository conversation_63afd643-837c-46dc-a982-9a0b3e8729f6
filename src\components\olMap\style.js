import Fill from "ol/style/Fill";
import Stroke from "ol/style/Stroke";

export const ImageStyle = {
  default: {
    width: 24, //宽度
    height: 24, //高度
    anchor: [12, 0], //设置锚点
    anchorOrigin: "bottom-left", //锚点依据
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    // offset: [0, 0], //设置偏移
    // offsetOrigin: "bottom-left",
    opacity: 0.9, //透明度
    crossOrigin: "anonymous",
  },
  label: {
    width: 24, //宽度
    height: 24, //高度
    anchor: [0.5, 1], //设置锚点
    // // anchorOrigin: "bottom-left",
    // anchorXUnits: "pixels",
    // anchorYUnits: "pixels",
    // displacement: [0, 0],
    // offset: [0, 0], //设置偏移
    // offsetOrigin: "bottom-left",
    opacity: 1, //透明度
    crossOrigin: "anonymous",
  },
  gif: {
    width: 150, //宽度
    height: 150, //高度
  },
  project: {
    width: 102, //宽度
    height: 145, //高度
    anchor: [51, 30], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  river: {
    width: 54, //宽度
    height: 54, //高度
    anchor: [27, 25], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  mountain: {
    width: 54, //宽度
    height: 54, //高度
    anchor: [27, 25], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  scenic: {
    width: 54, //宽度
    height: 54, //高度
    anchor: [27, 25], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  village: {
    width: 54, //宽度
    height: 54, //高度
    anchor: [27, 25], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  grainLabel: {
    width: 158, //宽度
    height: 57, //高度
    anchor: [31, 0], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 33],
    crossOrigin: "anonymous",
  },
  grainName: {
    width: 34, //宽度
    height: 34, //高度
    anchor: [17, 17], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
  grainNameActive: {
    width: 34, //宽度
    height: 34, //高度
    anchor: [17, 17], //设置锚点
    anchorOrigin: "bottom-left",
    anchorXUnits: "pixels",
    anchorYUnits: "pixels",
    displacement: [0, 0],
    crossOrigin: "anonymous",
  },
};
export const TextStyle = {
  default: {
    textAlign: "center", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 12px 微软雅黑", //字体样式
    offsetY: -30, // Y轴偏置
    fill: new Fill({
      //填充样式
      color: "#ffffff",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#0000007a",
    }),
    padding: [5, 2, 5, 2],
  },
  label: {
    textAlign: "center", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 16px 微软雅黑", //字体样式
    offsetY: -45, // Y轴偏置
    fill: new Fill({
      //填充样式
      color: "#ffffff",
    }),
    // backgroundFill: new Fill({
    //   // 填充背景
    //   color: "#0000007a",
    // }),
    padding: [5, 2, 5, 2],
  },
  polygon: {
    textAlign: "center", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 12px 微软雅黑", //字体样式
    fill: new Fill({
      //填充样式
      color: "#ffffff",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#3399CC7a",
    }),
    backgroundStroke: new Stroke({
      color: "#3399CC",
      width: 2,
      lineCap: "square",
    }),
    padding: [3, 3, 3, 3],
  },
  river: {
    textAlign: "left", //对齐方式
    justify: "center",
    offsetX: 35, // Y轴偏置
    font: "normal 24px zcoolwenyiti", //字体样式
    fill: new Fill({
      //填充样式
      color: "#020D14",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#35EBE1ca",
    }),
    padding: [3, 5, 1, 5],
  },
  mountain: {
    textAlign: "left", //对齐方式
    justify: "center",
    offsetX: 35, // Y轴偏置
    font: "normal 24px zcoolwenyiti", //字体样式
    fill: new Fill({
      //填充样式
      color: "#020D14",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#39EB8Aca",
    }),
    padding: [3, 5, 1, 5],
  },
  scenic: {
    textAlign: "left", //对齐方式
    justify: "center",
    offsetX: 35, // Y轴偏置
    font: "normal 24px zcoolwenyiti", //字体样式
    fill: new Fill({
      //填充样式
      color: "#020D14",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#FF74BFca",
    }),
    padding: [3, 5, 1, 5],
  },
  village: {
    textAlign: "left", //对齐方式
    justify: "center",
    offsetX: 35, // Y轴偏置
    font: "normal 24px zcoolwenyiti", //字体样式
    fill: new Fill({
      //填充样式
      color: "#020D14",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#FFA900ca",
    }),
    padding: [3, 5, 1, 5],
  },
  grainName: {
    textAlign: "center", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 12px 微软雅黑", //字体样式
    offsetY: -20, // Y轴偏置
    fill: new Fill({
      //填充样式
      color: "#ffffff",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#0000005a",
    }),
    padding: [2, 5, 2, 5],
  },
  grainNameActive: {
    textAlign: "center", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 12px 微软雅黑", //字体样式
    offsetY: -20, // Y轴偏置
    fill: new Fill({
      //填充样式
      color: "#ffffff",
    }),
    backgroundFill: new Fill({
      // 填充背景
      color: "#004dcf",
    }),
    padding: [2, 5, 2, 5],
  },
  grainLabel: {
    textAlign: "left", //对齐方式
    justify: "center",
    // textBaseline: "middle", //文本基线
    font: "normal 35px digital", //字体样式
    offsetX: 45, // x轴偏置
    offsetY: -65, // Y轴偏置
    fill: new Fill({
      //填充样式
      color: "#A6EFFF",
    }),
  },
};
export const StrokeStyle = {
  default: {
    color: "#3399CC",
    width: 2,
  },
  jiangtuan: {
    color: "#5EFF99",
    width: 3,
  },
};
export const FillStyle = {
  default: {
    color: "rgba(255, 255, 255, 0.2)",
  },
  jiangtuan: {
    color: "rgba(0, 30, 11, 0.4)",
  },
};
